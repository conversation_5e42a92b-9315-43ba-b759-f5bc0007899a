<script setup lang="ts">
const { data, page, pageSize, total, loading } = apiDigitalCard.getDigitalCard();
const { cardEdit } = pinia.storeToRefs(storePassing());
function onItem(item: TsApis.ApiResponse<"general", "post_aivatarapi_getdigitalcard">["rows"][number]) {
  cardEdit.value = item;
	router.push({
		name: "CardEdit"
	});
}
</script>

<template>
	<layout-table>
		<template #table>
			<div class="grid-container" v-loading="loading" element-loading-text="加载中...">
				<digital-business-card v-for="item in data" :key="item.id" :data="item" @click-use="onItem(item)" />
			</div>
		</template>
		<template #pagination>
			<base-pagination v-model:current-page="page" v-model:page-size="pageSize" :total="total" />
		</template>
	</layout-table>
</template>

<style scoped lang="scss">
:deep(.layout-table-main .part) {
	background: var(--el-bg-color-page);
}

.grid-container {
	min-height: 100%;
	display: grid;
	grid-template-columns: repeat(auto-fill, 346px);
	grid-gap: var(--el-gap) var(--el-gap-half);
}

.el-tag {
	cursor: pointer;
}
</style>
