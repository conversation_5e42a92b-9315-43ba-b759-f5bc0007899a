<script setup lang="ts">
const {prop} = defineProps<{
    label: TsTableColumn.Label,
    prop: TsTableColumn.Prop,
    sets?: TsTableColumn.Sets<TsBase.Object>,
}>()
function text(row:TsBase.Object) {
    let t = row;
    let pp:string[] = [prop];
    if(prop.indexOf(".")) pp = prop.split(".");
    for (let i = 0; i < pp.length; i++) {
        const val = t[pp[i]];
        t = val ?? "--";
    }
    return t;
}
</script>

<template>
    <el-table-column
        :prop="prop"
        :label="label"
        :min-width="label.length * 14 + 25"
        header-align="center"
        v-bind="sets"
    >
        <template #default="params">
            <slot v-bind="params">{{ text(params.row) }}</slot>
        </template>
    </el-table-column>
</template>