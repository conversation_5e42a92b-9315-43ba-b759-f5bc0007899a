<script setup lang="ts">
const props = defineProps<{
    data: TsApis.ApiResponse<"general", "get_consumption_record_list">["rows"],
    loading: boolean,
}>()
const emits = defineEmits(["update:data"]);
const tableData = vueuse.useVModel(props, 'data', emits);
// 格式化商品类型
function formatProductType(row: TsMyOrder.Spend.Data) {
    // 这里需要根据实际的字典表buiness_type来映射
    const typeMap: Record<number, string> = {
        1: '视频生成',
        2: '音频克隆',
        3: '数字人克隆',
        4: '其他'
    };
    return typeMap[row.productType] || '未知类型';
}
</script>

<template>
    <base-table v-loading="loading" v-model="tableData">
        <base-table-column label="订单编号" prop="orderNo" />
        <base-table-column label="商品类型" prop="productType" :formatter="formatProductType" />
        <base-table-column label="商品名称" prop="productName" />
        <base-table-column label="消耗钻石个数" prop="consumptionAmount" />
        <base-table-date label="创建时间" prop="createTime" />
    </base-table>
</template>

<style lang="scss" scoped>

</style>