<script setup lang="ts">
import { getData, cleanData } from './useDetail';
import * as CONST from './const';
import { createDebounceFunc } from '@/assets/utils/eventFn';

const { id } = useRoute().params;
const dataFromList = getData();

if (!dataFromList) {
    // 单个接口存在则更替为单个接口获取
    ElMessage.error('请从模特市场页面进入');
}

const data = shallowReadonly(dataFromList!);

// 视频相关
const videoInfo = shallowRef<Partial<TsModelMarket.VideoType>>();

const changeSence = (sence: TsModelMarket.SenceData) => {
    videoInfo.value = {
        sceneName: sence.sceneName,
        sceneCode: sence.sceneCode,
        proportion: sence.proportion,
        duration: sence.duration,
        videoUrl: sence.exampleUrl ?? undefined,
        coverUrl: sence.coverUrl,
        coverMattingUrl: sence.coverMattingUrl,
        sceneType: sence.sceneType as 0 | 1 | undefined,
    }
}

changeSence(data.sceneList?.[0]!);

// 基础信息
const basicInfo = shallowRef({
    name: data.robotName,
    sex: CONST.genderMap.get((data.gender ?? 1)as 1 | 2),
    tag: data.labelBaseDTOList?.map(i => i.labelName) ?? [],
    age: `${data.age}岁`,
    starSign: CONST.starSignsMap.get(data.starSigns as 1 | 2 | 3 | 4 | 5 | 6 | 7 | 8 | 9 | 10 | 11 | 12),
    desc: data.robotDesc,
    hot: data.popularity ?? 0,
});

// 右侧选择场景区域
const sceneList = shallowRef<TsModelMarket.SenceData[]>(data.sceneList ?? []);

const proportion = ref<string | undefined>(undefined);
const proportionList = shallowRef(CONST.proportionOptions);

const clone = ref<string | undefined>;
const cloneList = shallowRef(CONST.cloneOption);

const listShow = shallowRef<TsModelMarket.SenceData[]>([]);
const listNow = shallowRef<TsModelMarket.SenceData>();

const searchSet = shallowRef<TsInput.Sets>({
  placeholder: '搜索场景关键词',
  prefixIcon: markRaw(IconSolarMagniferLinear),
})
const searchValue = ref('');

const search = () => {
    const val = searchValue.value;
    const list = sceneList.value.filter(i => i.sceneName.includes(val));
    listShow.value = list;
};

const searchDebounce = createDebounceFunc(search, 500);

const selectItem = (item: TsModelMarket.SenceData) => {
    listNow.value = item;
    changeSence(item);
}

// 操作
const gotoEdit = () => {
    null
}
defineExpose({
    name: 'ModelDetail',
})
</script>

<template>
    <div class="detail">
        <main>
            <div class="info">
                <div class="video">
                    <digital-model-deatil-video
                        :video-info="videoInfo!"
                    />
                </div>
                <div class="info">
                    <div class="name-hot">
                        <span class="name">
                            {{ basicInfo.name }}
                        </span>
                        <span class="hot">
                            <base-icon class="icon favorite-not-active" icon="solar:heart-linear" />
                            {{ basicInfo.hot }}
                        </span>
                    </div>
                    <div class="tag">
                        <span class="label">标签：</span>
                        <el-tag v-for="item in basicInfo.tag" :key="item">{{ item }}</el-tag>
                    </div>
                    <div class="age-starSign">
                        <div class="age">
                            <span class="label">年龄：</span>
                            {{ basicInfo.age }}
                        </div>
                        <div class="starSign">
                            <span class="label">星座：</span>
                            {{ basicInfo.starSign }}
                        </div>
                    </div>
                    <div class="desc">
                        <span class="label">描述：</span>
                        {{ basicInfo.desc }}
                    </div>
                </div>
            </div>
            <div class="operation">
                <div class="top">
                    <div>
                        <h3>模特分身</h3>
                        <div class="select">
                            <span class="label">
                                分身类型:
                            </span>
                            <base-select
                                v-model="clone"
                                :options="cloneList"
                            />
                        </div>
                        <div class="select">
                            <span class="label">
                                比例:
                            </span>
                            <base-select
                                v-model="proportion"
                                :options="proportionList"
                            />
                        </div>
                    </div>
                    <div class="search">
                        <base-input
                            v-model="searchValue"
                            class="search-input"
                            :sets="searchSet"
                            @keyup.enter="searchDebounce"
                        />
                    </div>
                </div>
                <div class="select-container">
                    <div
                        v-for="item in listShow"
                        class="select-item"
                        :class="{'active': item.id === listNow?.id}"
                        :key="item.id"
                        @click="() => selectItem(item)"
                    >
                        <img
                            :src="item.coverUrl"
                            :alt="item.sceneName"
                        />
                        <span class="name">{{ item.sceneName }}</span>
                    </div>
                </div>
            </div>
        </main>
        <div class="button-group">
            <base-button @click="gotoEdit">
                去制作
            </base-button>
        </div>
    </div>
</template>

<style lang="scss" scoped>
.detail {
    height: 100%;

    .button-group {
        display: flex;
        justify-content: flex-end;
        height: 72px;
        background-color: var(--el-bg-color);
        align-items: center;
        border-radius: var(--el-border-radius-round);
        margin-top: 4vh;
    }
}

main {
    width: 100%;
    display: flex;
    gap: var(--el-gap);
    height: calc(100% - 72px - 4vh);
    padding: var(--el-gap);
    box-sizing: border-box;
}

.info {
    flex-basis: 40%;
    background-color: var(--el-bg-color);
    border-radius: var(--el-border-radius-round);

    .video {

    }
}

.operation {
    flex: 1;
    background-color: var(--el-bg-color);
    border-radius: var(--el-border-radius-round);
}
</style>
