<script setup lang="ts">
import { getData, cleanData } from './useDetail';

const { id } = useRoute().params;
const dataFromList = getData();

if (!dataFromList) {
    // 单个接口存在则更替为单个接口获取
    ElMessage.error('请从模特市场页面进入');
}

const data = shallowReadonly(dataFromList!);

defineExpose({
    name: 'ModelDetail',
})
</script>

<template>
    <div class="detail">
        <main>
            <div class="info">

            </div>
            <div class="operation">

            </div>
        </main>
        <div class="button-group">

        </div>
    </div>
</template>

<style lang="scss" scoped>
.detail {
    height: 100%;

    .button-group {
        display: flex;
        justify-content: flex-end;
        height: 72px;
        background-color: var(--el-bg-color);
        align-items: center;
        border-radius: var(--el-border-radius-round);
        margin-top: 4vh;
    }
}

main {
    width: 100%;
    display: flex;
    gap: var(--el-gap);
    height: calc(100% - 72px - 4vh);
}

.info {
    flex-basis: 40%;
    background-color: var(--el-bg-color);
    border-radius: var(--el-border-radius-round);
}

.operation {
    flex: 1;
    background-color: var(--el-bg-color);
    border-radius: var(--el-border-radius-round);
}
</style>
