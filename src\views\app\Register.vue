<script setup lang="ts">
// 项目标题
const title = import.meta.env.VITE_BASE_TITLE;

const countDown = ref(0);
let countDownTimeoutId:number;
// 表单引用
const formRef = useTemplateRef<TsForm.El>('formRef');

// 注册表单
const {form, loading: isRegistering, send: sendRegister} = apiApp.register();

// 注册设置
const setsRegister = computed((): TsButton.Sets=>({
    block: true,
    disabled: !form.value.code || !form.value.nickname || !form.value.username || !form.value.password || !form.value.phonenumber,
    loading: isRegistering.value,
    nativeType: 'submit',
}))

// 回登录设置
const setsLogin = shallowRef<TsButton.Sets>({
    type: 'default',
})

// 昵称设置
const setsNickname = shallowRef<TsInput.Sets>({
    placeholder: '请输入昵称',
    clearable: true,
    prefixIcon: markRaw(IconSolarUserHandUpLinear),
});

// 账号设置
const setsAccount = shallowRef<TsInput.Sets>({
    placeholder: '请输入账号',
    clearable: true,
    prefixIcon: markRaw(IconSolarUserIdLinear),
});

// 密码设置
const setsPassword = shallowRef<TsInput.Sets>({
    type: 'password',
    placeholder: '请输入密码',
    clearable: true,
    prefixIcon: markRaw(IconSolarLockLinear),
    showPassword: true,
    autocomplete: 'new-password',
});

// 手机号设置
const setsPhone = shallowRef<TsInput.Sets>({
    placeholder: '请输入手机号',
    clearable: true,
    prefixIcon: markRaw(IconSolarIphoneLinear),
});

// 验证码设置
const setsVerificationCode = shallowRef<TsInput.Sets>({
    placeholder: '请输入验证码',
    clearable: true,
    maxlength: 6,
});

// 表单设置
const setsForm = shallowRef<TsForm.Sets>({
    size: 'large',
    rules: {
        nickname: [{ required: true, message: '请输入昵称', trigger: 'blur' }],
        username: [{ required: true, message: '请输入账号', trigger: 'blur' }],
        password: [{ required: true, message: '请输入密码', trigger: 'blur' }, { min: 6, message: '密码长度至少为6位', trigger: 'blur' }],
        phonenumber: [{ required: true, message: '请输入手机号', trigger: 'blur' }, { pattern: REGEXP.phone, message: '请输入正确的手机号格式', trigger: 'blur' }],
        code: [{ required: true, message: '请输入验证码', trigger: 'blur' }]
    }
});

// 更新VerificationCode倒计时
const updateVerificationCodeCountDown = (sec = 60) => {
    clearTimeout(countDownTimeoutId);

    if (sec <= 0) {
        countDown.value = 0;
        lowPriorityFn(() => {
            localStorage.removeItem('VerificationCodeLessTime');
        })
        return;
    }

    countDown.value = sec;

    countDownTimeoutId = setTimeout(() => {
        updateVerificationCodeCountDown(sec - 1);
    }, 1000);

    localStorage.setItem('VerificationCodeLessTime', sec.toString(10));
};

// 防止刷新重复发送
onMounted(() => {
    const VerificationCodeLessTime = localStorage.getItem('VerificationCodeLessTime');
    if (VerificationCodeLessTime) {
        updateVerificationCodeCountDown(parseInt(VerificationCodeLessTime));
    }
})

// 获取验证码
const {send: sendPhoneCode} = apiApp.getPhoneCode();
function getVerificationCode() {
  // 验证手机号
  if (!form.value.phonenumber || !REGEXP.phone.test(form.value.phonenumber)) {
    ElMessage.error('请输入正确的手机号');
    return;
  }
  updateVerificationCodeCountDown();
  sendPhoneCode({phoneNumber: form.value.phonenumber});
}

// 注册接口
function onRegister(e: SubmitEvent) {
	e.preventDefault();
    sendRegister();
}

defineExpose({
    name: 'SystemRegister',
})
</script>
<template>
	<main>
		<div class="logo-box">
			<div class="logo">
				<img src="/vite.svg" alt="" />
			</div>
			<div class="logo-text">{{ title }}</div>
		</div>
		<div class="form-box">
			<div class="form-title">用户注册</div>
			<div class="form-cont">
                <base-form ref="formRef" v-model="form" :sets="setsForm" @submit="onRegister">
                    <base-form-item prop="nickname">
                        <base-input v-model="form.nickname" :sets="setsNickname" />
                    </base-form-item>
                    <base-form-item prop="username">
                        <base-input v-model="form.username" :sets="setsAccount" />
                    </base-form-item>
                    <base-form-item prop="password">
                        <base-input v-model="form.password" :sets="setsPassword" />
                    </base-form-item>
                    <base-form-item prop="phonenumber">
                        <base-input v-model="form.phonenumber" :sets="setsPhone">
                        </base-input>
                    </base-form-item>
                    <base-form-item class="VerificationCode" prop="code">
                        <base-input class="VerificationCode-input" v-model="form.code" :sets="setsVerificationCode" />
                        <el-button
                            class="VerificationCode-btn"
                            type="primary"
                            :disabled="countDown > 0 || !form.phonenumber"
                            @click="getVerificationCode"
                        >
                            {{ countDown > 0 ? `${countDown}s后重新获取` : '获取验证码' }}
                        </el-button>
                    </base-form-item>
                    <div class="button-group">
						<base-button :sets="setsLogin" @click="$router.push({ name: 'Login' })">返回登录</base-button>
                    	<base-button :sets="setsRegister">注册</base-button>
					</div>
                </base-form>
			</div>
		</div>
	</main>
</template>
<style scoped>
main {
	width: 100%;
	height: 100%;
	background: url("@/assets/image/bg-login.jpg") no-repeat center;
	background-size: cover;
	position: relative;
}

.logo-box {
	position: absolute;
	left: 10%;
	top: 10%;
	display: flex;
	align-items: center;
	gap: var(--el-gap);
}

.logo {
	width: 40px;
	height: 40px;
}

.logo-text {
	font: bold 28px "Microsoft YaHei UI";
	letter-spacing: 2px;
}

.form-box {
	width: 360px;
	height: auto;
	border-radius: var(--el-border-radius-round);
	overflow: hidden;
	box-shadow: var(--el-box-shadow);
	position: absolute;
	right: 14%;
	top: calc(50% - 280px);
	background-color: rgba(255, 255, 255, 0.35);
	backdrop-filter: blur(4px);
	padding: var(--el-gap) calc(var(--el-gap) * 2);
}

.form-title {
	padding: 10px 0 30px;
	text-align: center;
	font: bold 24px "Microsoft YaHei UI";
	letter-spacing: 3px;
}
.form-box :deep(.el-input-group__append) {
	margin-left: var(--el-gap-half);
	padding: 0;
}
.form-box :deep(.el-button) {
	height: 38px;
}

.VerificationCode {
    display: flex;
}

.VerificationCode-input {
    flex: 1;
}

.VerificationCode-btn {
    flex-basis: content;
    margin-left: 8px;
    height: 40px;
}

.button-group {
	display: flex;
	justify-content: space-between;
}
</style>
