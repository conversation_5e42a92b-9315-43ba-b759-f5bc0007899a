const Balace = {
    rechargeDialog: {
        tabs: [
            { label: '充值积分', value: 'recharge_point' },
            { label: '通用视频时长', value: 'general_video_duration' },
            { label: '音频时长', value: 'audio_duration' },
            // { label: '形象克隆S级', value: 'image_clone_s' },
            { label: '形象克隆E级', value: 'image_clone_e' },
            // { label: '形象克隆S级-超清版', value: 'image_clone_s_hd' },
            { label: '声音克隆E级', value: 'voice_clone_e' },
            // { label: '声音克隆S级', value: 'voice_clone_s' }
        ] as {
            label: string,
            value: TsBalace.RechargeDialog.tab
        }[],
    }
} as const;

Object.freeze(Balace);

export default Balace;
