<script setup lang="ts">
import './dialogInside.scss'
import pointGoodCard from './pointGoodCard.vue';

defineProps<{
    balance: string
}>();

const emit = defineEmits<{
    updateChoose: [point: number],
}>();

provide('unit', '分钟');

// 充值选项
const rechargeOptions = [
    { time: 30, price: 2.5, unit: '秒'},
    { time: 1, price: 5, unit: '分钟'},
    { time: 10, price: 50, unit: '分钟'},
    { time: 30, price: 150, unit: '分钟'},
    { time: 50, price: 250, unit: '分钟'},
    { time: 100, price: 500, unit: '分钟'},
    { time: 300, price: 1500, unit: '分钟'},
    { time: 500, price: 2500, unit: '分钟'},
    { time: 1000, price: 5000, unit: '分钟'}
].map((i, index) => ({
    ...i,
    id: index
}));

const remainingPoint = ref(rechargeOptions[0]);

// 选择充值时长
function handelClick(item: typeof rechargeOptions[number]) {
    remainingPoint.value = item;
    emit('updateChoose', item.price);
}

onMounted(() => {
    emit('updateChoose', remainingPoint.value.price);
})
</script>

<template>
    <!-- 信息显示 -->
    <div class="recharge-dialog-inside-info">
        <span>剩余时长：{{ balance }}</span>
    </div>

    <!-- 充值选项网格 -->
    <div class="recharge-dialog-inside-grid">
        <pointGoodCard
            v-for="option in rechargeOptions"
            :key="option.id"
            :price="option.price"
            :num="option.time"
            :is-active="remainingPoint.id == option.id"
            :unit="option.unit"
            @click="handelClick(option)"
        />
    </div>
</template>
