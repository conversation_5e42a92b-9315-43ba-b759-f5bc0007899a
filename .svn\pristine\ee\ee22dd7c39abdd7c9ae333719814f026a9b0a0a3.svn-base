export const dataRoutesDigital: TsStore.RouteDynamic[] = [
    {
        path: "/digital",
        name: "Digital",
        redirect: "/digital/works/video",
        viewPath: "/components/layout/layoutDigital.vue",
        meta: {
            label: "硅基数字",
            icon: "solar:users-group-rounded-linear",
        },
        children: [
            {
                path: "works",
                name: "Works",
                redirect: "/digital/works/video",
                viewPath: "/components/layout/layoutDigitalSub.vue",
                meta: {
                    label: "我的作品",
                    icon: "solar:box-minimalistic-bold-duotone",
                },
                children: [
                    {
                        path: "video",
                        name: "Video",
                        viewPath: "/views/pages/digital/works/Video.vue",
                        meta: {
                            label: "视频",
                            icon: "solar:video-library-bold-duotone",
                        }
                    }, {
                        path: "audio",
                        name: "Audio",
                        viewPath: "/views/pages/digital/works/Audio.vue",
                        meta: {
                            label: "音频",
                            icon: "solar:headphones-round-sound-bold-duotone",
                        }
                    },
                    // {
                    //     path: "card",
                    //     name: "Card",
                    //     viewPath: "/views/pages/digital/works/Card.vue",
                    //     meta: {
                    //         label: "名片",
                    //         icon: "solar:cardholder-bold-duotone",
                    //     }
                    // }
                ]
            },
            {
                path: "market",
                name: "Market",
                redirect: "/digital/market/model",
                viewPath: "/components/layout/layoutDigitalSub.vue",
                meta: {
                    label: "数字市场",
                    icon: "solar:archive-minimalistic-bold-duotone",
                },
                children: [
                    {
                        path: "model",
                        name: "Model",
                        viewPath: "/views/pages/digital/market/model/Model.vue",
                        meta: {
                            label: "模特市场",
                            icon: "solar:user-hands-bold-duotone",
                        }
                    }, {
                        path: "model/detail/:id",
                        name: "ModelDetail",
                        viewPath: "/views/pages/digital/market/model/ModelDetail.vue",
                        meta: {
                            label: "模特详情",
                            icon: "solar:user-hands-bold-duotone",
                            isDetail: true,
                        }
                    },
                    // {
                    //     path: "template",
                    //     name: "Template",
                    //     viewPath: "/views/pages/digital/market/Template.vue",
                    //     meta: {
                    //         label: "模板市场",
                    //         icon: "solar:clapperboard-open-bold-duotone",
                    //     }
                    // },
                    {
                        path: "marketAudio",
                        name: "marketAudio",
                        viewPath: "/views/pages/digital/market/Audio.vue",
                        meta: {
                            label: "声音市场",
                            icon: "solar:soundwave-square-bold-duotone",
                        }
                    },
                    // {
                    //     path: "marketCard",
                    //     name: "marketCard",
                    //     viewPath: "/views/pages/digital/market/Card.vue",
                    //     meta: {
                    //         label: "数字名片",
                    //         icon: "solar:card-2-line-duotone",
                    //     }
                    // }
                ]
            },
            {
                path: "my",
                name: "My",
                viewPath: "/components/layout/layoutDigitalSub.vue",
                redirect: "/digital/my/order",
                meta: {
                    label: "我的订单",
                    icon: "solar:home-angle-bold-duotone",
                },
                children: [
                    {
                        path: "order",
                        name: "Order",
                        viewPath: "/views/pages/my/order/Order.vue",
                        meta: {
                            label: "我的订单",
                            icon: "solar:user-bold-duotone",
                            isDetail: true,
                        }
                    }
                ]
            },
            {
                path: "recharge",
                name: "DiamondRecharge",
                viewPath: "/views/pages/digital/DiamondRecharge.vue",
                meta: {
                    label: "充值",
                    icon: "solar:card-bold-duotone",
                }
            },
            {
                path: "customized",
                name: "Customized",
                viewPath: "/views/pages/digital/edit/Customized.vue",
                meta: {
                    label: "定制模型",
                    icon: "solar:user-speak-bold-duotone",
                    noMenu: true,
                    isDetail: true
                }
            },
            {
                path: "modelEdit",
                name: "ModelEdit",
                viewPath: "/views/pages/digital/edit/ModelEdit.vue",
                meta: {
                    label: "模特编辑",
                    icon: "solar:headphones-square-sound-bold-duotone",
                    noMenu: true,
                    isDetail: true
                }
            },
            // {
            //     path: "cardEdit",
            //     name: "CardEdit",
            //     viewPath: "/views/pages/digital/edit/CardEdit.vue",
            //     meta: {
            //         label: "名片编辑",
            //         icon: "solar:card-bold-duotone",
            //         noMenu: true,
            //         isDetail: true
            //     }
            // },
            {
                path: "audioEdit",
                name: "AudioEdit",
                viewPath: "/views/pages/digital/edit/AudioEdit.vue",
                meta: {
                    label: "音频编辑",
                    icon: "solar:headphones-square-sound-bold-duotone",
                    noMenu: true,
                    isDetail: true
                }
            }, {
                path: "audioClone",
                name: "AudioClone",
                viewPath: "/views/pages/system/AudioClone.vue",
                meta: {
                    label: "音频编辑",
                    icon: "solar:headphones-square-sound-linear",
                    noMenu: true,
                }
            }, {
                path: "digitalSystem",
                name: "DigitalSystem",
                redirect: "/system",
                meta: {
                    label: "后台管理",
                    icon: "solar:settings-bold-duotone",
                    permissions: ["00"]
                },
                children: [],
            }
        ]
    }
]

const data: TsStore.RouteDynamic[] = [
    {
        path: "/system",
        name: "System",
        viewPath: "/components/layout/layoutPage.vue",
        redirect: "/system/user",
        meta: {
            label: "系统管理",
            icon: "solar:settings-linear",
            permissions: ["00"]
        },
        children: [
            {
                path: "user",
                name: "User",
                viewPath: "/views/pages/system/User.vue",
                meta: {
                    label: "用户",
                    icon: "solar:users-group-rounded-linear",
                    keepAlive: true,
                },
            },
            {
                path: "role",
                name: "Role",
                viewPath: "/views/pages/system/Role.vue",
                meta: {
                    label: "角色",
                    icon: "solar:user-id-linear",
                    keepAlive: true,
                },
            }, {
                path: "diamondConfig",
                name: "DiamondConfig",
                viewPath: "/views/pages/system/DiamondConfig.vue",
                meta: {
                    label: "钻石配置",
                    icon: "solar:user-id-linear",
                    keepAlive: true,
                },
            }
        ]
    },
]

export default [
    ...data,
    ...dataRoutesDigital
];