export default {
	// 用户可用字体
	getFont: () => {
		const method = Apis.general.post_aivatarapi_getfontcategorylist({
			name: "post_aivatarapi_getfontcategorylist",
			transform: (res) => {
				return res.data || [];
			},
		});
		return alova.useRequest(() => method, {
			immediate: true,
			initialData: {
				code: 0,
				msg: "",
				data: [],
			},
		});
	},
	// 文件上传
	uploadFile: () => {
		const method = (data: TsApis.ApiData<"general", "post_aivatarapi_gettemporaryuploadurl">) =>
			Apis.general.post_aivatarapi_gettemporaryuploadurl({
				name: "post_aivatarapi_gettemporaryuploadurl",
				data,
			});
        return alova.useRequest(method, { 
            immediate: false,
            middleware: alova.actionDelegationMiddleware("post_aivatarapi_gettemporaryuploadurl"),
        });
	},
};
