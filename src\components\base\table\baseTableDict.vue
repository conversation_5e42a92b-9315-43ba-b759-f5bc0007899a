<script setup lang="ts">
const { prop, options } = defineProps<{
	label: TsTableColumn.Label;
	prop: TsTableColumn.Prop;
	options: TsTableDict.Options;
	sets?: TsTableColumn.Sets<TsBase.Object>;
}>();

function text(row: TsBase.Object) {
    const item = options.find(item => item.dictValue === row[prop]);
	return {
		type: item?.listClass as TsElementPlus.TagProps['type'],
		text: item?.dictLabel ?? "--"
	};
}
</script>

<template>
	<el-table-column :prop="prop" :label="label" :min-width="label.length * 14 + 25" :align="'center'" v-bind="sets">
		<template #default="params">
			<slot v-bind="params">
                <el-tag v-if="params.row[prop]" :type="text(params.row).type">
                    {{ text(params.row).text }}
                </el-tag>
                <span v-else>--</span>
            </slot>
		</template>
	</el-table-column>
</template>
