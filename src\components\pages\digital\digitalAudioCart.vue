<template>
  <div class="template-list">
    <!-- 模板卡片列表循环 -->
    <div class="template-card">
      <div class="card-media">
        <video
          :poster="option.cover"
          controls
          loop
          muted
          preload="none"
          @mouseenter="playVideo($event)"
          @mouseleave="pauseVideo($event)"
        >
          <source :src="option.videoSrc" type="video/mp4" />
        </video>
      </div>
      <div class="card-info">
        <span class="title">{{ option.name }}</span>
        <span class="usage">使用</span>
      </div>
      <div class="stats">
        <base-icon icon="solar:fire-bold"></base-icon>
        <span>{{ option.views }}</span>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
// 鼠标进入时播放视频
const playVideo = (event: MouseEvent) => {
  const video = event.target as HTMLVideoElement;
  video.play();
};
// 鼠标离开时暂停视频并重置到开头
const pauseVideo = (event: MouseEvent) => {
  const video = event.target as HTMLVideoElement;
  video.pause();
  video.currentTime = 0; // 重置到视频开头
};

defineProps<{
  option: TsCard.cardItem;
}>();
</script>

<style scoped>
.template-list {
  display: flex;
  flex-wrap: wrap;
  gap: 16px;
  padding: 16px;
  justify-content: flex-start;
}
.template-card {
  width: 346px;
  height: 258px;
  background: var(--el-bg-color);
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  cursor: pointer;
  display: flex;
  flex-direction: column;
  align-items: center;
  transition: transform 0.2s ease;
}
.template-card:hover {
  transform: translateY(-4px);
}
.card-media {
  width: 100%;
  height: 194px;
  position: relative;
  overflow: hidden;
  border-top-left-radius: 8px;
  border-top-right-radius: 8px;
}

.card-media video {
  width: 100%;
  height: 100%;
  object-fit: cover;
}
.card-info {
  width: 100%;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 12px;
}
.usage {
  color: white;
  background-color: #409eff;
  cursor: pointer;
  font-size: 14px;
  padding: 4px 8px;
  border-radius: 4px;
}
.stats {
  font-size: 12px;
  color: #f56c6c;
  padding: 0 12px 8px;
  display: flex;
  align-items: center;
  gap: 4px;
  margin-right: auto;
}
</style>
