<script setup lang="ts">
const { audioEdit } = pinia.storeToRefs(storePassing());

// 文本内容
const textareaContent = ref("");
// 是否已合成
const isSynthesized = ref(false);
const audioUrl = ref<string>("");
// 合成接口
const { form, loading, send: sendAI } = apiDigitalAudio.synthesize();
const setsSend = computed(():TsButton.Sets => ({
	disabled: !textareaContent.value,
	loading: loading.value,
	type: "success"
}))
function onSend() {
	sendAI().then((res) => {
		audioUrl.value = res.data.ttsUrl;
		isSynthesized.value = true;
	});
}
</script>

<template>
	<div class="audio-editor-container">
		<div class="left-panel">
			<div class="text-editor-container">
				<el-input type="textarea" v-model="textareaContent" maxlength="20000" show-word-limit placeholder="请输入内容后，点击AI合成"
					class="full-height-input" />
			</div>
			<div class="audio-info">
				<span>{{ audioEdit?.ttsName }}</span>
				<base-button :sets="setsSend" @click="onSend">AI合成</base-button>
			</div>
			<div class="audio-controls-wrapper">
				<digital-audio-play :option="{ avatar: audioEdit?.ttsCover, audioSrc: audioUrl || audioEdit?.ttsAudition }" />
			</div>
		</div>
		<div class="right-panel">
			<div class="model-section">
				<div class="model-label">音频效果</div>
			</div>
			<div class="param-control">
				<div class="param-row">
					<span class="param-label">语速</span>
					<span class="param-value">{{ form.speechRate }}</span>
				</div>
				<el-slider v-model="form.speechRate" :min="0" :max="1" :step="0.1" class="param-slider" />
			</div>
			<div class="param-control">
				<div class="param-row">
					<span class="param-label">音量</span>
					<span class="param-value">{{ form.volume }}</span>
				</div>
				<el-slider v-model="form.volume" :min="0" :max="1" :step="0.1" class="param-slider" />
			</div>
			<div class="param-control">
				<div class="param-row">
					<span class="param-label">语调</span>
					<span class="param-value">{{ form.pitch }}</span>
				</div>
				<el-slider v-model="form.pitch" :min="0" :max="1" :step="0.1" class="param-slider" />
			</div>
		</div>
	</div>
</template>

<style scoped>
.audio-editor-container {
	display: flex;
	width: 100%;
	height: 100%;
	overflow: hidden;
	background-color: var(--el-bg-color);
	border-radius: var(--el-border-radius-middle);

	.left-panel {
		flex: 1;
		display: flex;
		flex-direction: column;
		padding: var(--el-gap);
		gap: var(--el-gap);

		.text-editor-container {
			flex: 1;
			position: relative;
			display: flex;
			flex-direction: column;
		}

		.audio-info {
			display: flex;
			align-items: center;
			justify-content: space-between;
			gap: var(--el-gap);
		}

		.audio-controls-wrapper {
			padding: 15px;
			background-color: #f9fafb;
			border-radius: 6px;
			display: flex;
			flex-direction: column;
			gap: 10px;
		}
	}

	.right-panel {
		width: 350px;
		padding: 20px;
		background-color: #f9fafb;
		overflow-y: auto;

		.model-section {
			margin-bottom: 10px;
			border-bottom: 1px solid #e5e7eb;
			padding-bottom: 20px;

			.model-label {
				font-size: 14px;
				margin-bottom: 4px;
				color: #949aa5;
			}

			.model-value {
				margin-top: 20px;
				font-size: 14px;
			}
		}

		.param-control {
			display: flex;
			flex-direction: column;
			margin-bottom: 20px;
			padding: 10px;
			background-color: #fff;
			border-radius: 6px;
			box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);

			.param-row {
				display: flex;
				justify-content: space-between;
				align-items: center;
				margin-top: 10px;
				margin-bottom: 8px;

				.param-label {
					font-size: 14px;
				}

				.param-value {
					font-size: 12px;
					color: #666;
				}
			}

			.param-slider {
				width: 100%;
			}

			.param-select {
				width: 100%;
			}
		}
	}
}

.full-height-input {
	flex: 1;
	display: flex;
	flex-direction: column;
}

.full-height-input :deep(.el-textarea) {
	flex: 1;
	display: flex;
	flex-direction: column;
}

.full-height-input :deep(.el-textarea__inner) {
	flex: 1;
	resize: none;
}
</style>
