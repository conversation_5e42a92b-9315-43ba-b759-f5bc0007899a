export default {
	// 充值详情
	recharge() {
		const method = (pageNum: number, pageSize: number) =>
			Apis.general.get_order_order_list({
				name: "get_order_order_list",
				params: {
					pageNum,
					pageSize,
				},
				transform: (res) => ({
					total: res.total ?? 0,
					data: res.rows ?? [],
				}),
			});
		return alova.usePagination(method, {
			immediate: true,
			middleware: alova.actionDelegationMiddleware("get_order_order_list"),
			initialData: {
				total: 0,
				data: [],
			},
		});
	},
	// 钻石消耗
	spend() {
		const method = (pageNum: number, pageSize: number) =>
			Apis.general.get_consumption_record_list({
				name: "get_consumption_record_list",
				params: {
					pageNum,
					pageSize,
				},
				transform: (res) => ({
					total: res.total ?? 0,
					data: res.rows ?? [],
				}),
			});
		return alova.usePagination(method, {
			immediate: true,
			middleware: alova.actionDelegationMiddleware("get_consumption_record_list"),
			initialData: {
				total: 0,
				data: [],
			},
		});
	},
	// 作品消耗
	work() {
		const method = (pageNum: number, pageSize: number) =>
			Apis.general.get_workconsumption_record_list({
				name: "get_workconsumption_record_list",
				params: {
					pageNum,
					pageSize,
				},
				transform: (res) => ({
					total: res.total ?? 0,
					data: res.rows ?? [],
				}),
			});
		return alova.usePagination(method, {
			immediate: true,
			middleware: alova.actionDelegationMiddleware("get_workconsumption_record_list"),
			initialData: {
				total: 0,
				data: [],
			},
		});
	},
};
