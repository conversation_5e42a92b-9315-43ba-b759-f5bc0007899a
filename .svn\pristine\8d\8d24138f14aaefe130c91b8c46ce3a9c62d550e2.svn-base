<script setup lang="ts">
import CONST from '@/assets/const/myOrder';

const {
    spend,
} = CONST;

defineProps<{
    loading: boolean,
}>();

const formData = ref<TsMyOrder.Spend.FormData>({
    productCategory: '',
    productName: '',
    spendSource: '',
    paymentStatus: '',
    orderTimeRange: [],
});

const emit = defineEmits<{
    search: [data: TsMyOrder.Spend.FormData];
    export: [data: TsMyOrder.Spend.FormData];
}>();

// 监听表单数据变化，自动执行搜索
watch(formData, () => {
    handleSearch();
}, { deep: true });


// 时间筛选
const timeFilter = ref('');
const timeSelectData = ref<string[]>([]);

// 监听时间筛选变化
const TimeSelectChange = () => {
    const newValue = timeFilter.value;
    timeSelectData.value = [];

    if (newValue === 'all') {
        formData.value.orderTimeRange = [];
        return;
    }
    // 清空日期选择器
    formData.value.orderTimeRange = [];
    // 根据选择的时间段设置日期范围
    const today = new Date();
    const endDate = today.toISOString().split('T')[0];
    let startDate = '';
    
    switch (newValue) {
        case 'today':
            startDate = endDate;
            break;
        case 'week':
            const weekAgo = new Date(today.getTime() - 7 * 24 * 60 * 60 * 1000);
            startDate = weekAgo.toISOString().split('T')[0];
            break;
        case 'month':
            const monthAgo = new Date(today.getTime() - 30 * 24 * 60 * 60 * 1000);
            startDate = monthAgo.toISOString().split('T')[0];
            break;
    }
    
    if (startDate) {
        formData.value.orderTimeRange = [startDate, endDate];
    }
}

// 处理日期范围变化
function handleDateRangeChange(value: [string, string]) {
    if (value) {
        // 清空时间筛选
        timeFilter.value = '';
        // 重新赋值
        formData.value.orderTimeRange = value;
    }
}

function handleSearch() {
    emit('search', formData.value);
}

function handelExport() {
    emit('export', formData.value);
}
</script>

<template>
    <base-form class="base-form" v-model="formData" :sets="{ inline: true }">
        <base-form-item class="select-input-item item">
            <base-select v-model="formData.productCategory" :options="spend.productCategoryOptions" :sets="{ placeholder: '请选择商品类别' }" />
        </base-form-item>
        <base-form-item class="select-input-item item">
            <base-input v-model="formData.productName" :clearable="false" placeholder="请输入商品名称" />
        </base-form-item>
        <base-form-item class="select-input-item item">
            <base-select v-model="formData.spendSource" :options="spend.spendSourceOptions" :sets="{ placeholder: '请选择消耗来源' }" />
        </base-form-item>
        <base-form-item class="select-input-item item">
            <base-select v-model="formData.paymentStatus" :options="spend.paymentStatusOptions" :sets="{ placeholder: '请选择支付状态' }" />
        </base-form-item>
        <base-form-item class="radio-item item">
            <el-radio-group v-model="timeFilter" @input="TimeSelectChange">
                <el-radio-button
                    v-for="item in spend.timeFilterOptions"
                    :key="item.value"
                    :value="item.value"
                    :disabled="item.disabled"
                >
                    {{ item.label }}
                </el-radio-button>
            </el-radio-group>
        </base-form-item>
        <base-form-item class="item">
            <base-date-picker
                v-model="timeSelectData"
                type="daterange"
                valueFormat="YYYY-MM-DD"
                @change="handleDateRangeChange"
            />
        </base-form-item>
        <base-button @click="handelExport">导出</base-button>
    </base-form>
</template>

<style lang="scss" scoped>
.base-form {
    width: 100%;
    display: flex;
    padding: var(--el-gap-half);
    background-color: var(--el-bg-color);
}

.item {
    margin-right: var(--el-gap);
}

.select-input-item {
    width: 180px;
}

.radio-item {
    width: 240px;
}
</style>
