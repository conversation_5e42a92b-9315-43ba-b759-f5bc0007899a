<script setup lang="ts">
const { data, page, pageSize, total } = apiDigitalModel.getWorksList();

</script>
<template>
	<layout-table>
		<template #table>
			<el-empty v-if="data.length == 0"></el-empty>
			<div class="video-container" v-else>
				<digital-video-part v-for="option in data" :key="option.id" :option="option" />
			</div>
		</template>
		<template #pagination>
			<base-pagination v-model:current-page="page" v-model:page-size="pageSize" :total="total" />
		</template>
	</layout-table>
</template>
<style scoped lang="scss">
.video-container {
	display: flex;
	gap: var(--el-gap);
	flex-wrap: wrap;
	padding: var(--el-gap);
	background-color: var(--el-bg-color);
	border-radius: var(--el-border-radius-round);
}
</style>
