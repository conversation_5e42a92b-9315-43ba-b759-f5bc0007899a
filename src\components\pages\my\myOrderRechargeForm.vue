<script setup lang="ts">
import CONST from '@/assets/const/myOrder';

const {
    recharge,
} = CONST;

defineProps<{
    loading: boolean,
}>();

const formData = ref<TsMyOrder.Recharge.FormData>({
    orderNo: '',
    rechargeSource: '',
    rechargeType: '',
    orderStatus: '',
    orderTimeRange: [],
});

const emit = defineEmits<{
    search: [data: TsMyOrder.Recharge.FormData];
    export: [data: TsMyOrder.Recharge.FormData];
}>();

// 监听表单数据变化，自动执行搜索
watch(formData, () => {
    handleSearch();
}, { deep: true });


// 时间筛选
const timeFilter = ref('');
const timeSelectData = ref<string[]>([]);

// 监听时间筛选变化
const TimeSelectChange = () => {
    const newValue = timeFilter.value;
    timeSelectData.value = [];

    if (newValue === 'all') {
        formData.value.orderTimeRange = [];
        return;
    }
    // 清空日期选择器
    formData.value.orderTimeRange = [];
    // 根据选择的时间段设置日期范围
    const today = new Date();
    const endDate = today.toISOString().split('T')[0];
    let startDate = '';
    
    switch (newValue) {
        case 'today':
            startDate = endDate;
            break;
        case 'week':
            const weekAgo = new Date(today.getTime() - 7 * 24 * 60 * 60 * 1000);
            startDate = weekAgo.toISOString().split('T')[0];
            break;
        case 'month':
            const monthAgo = new Date(today.getTime() - 30 * 24 * 60 * 60 * 1000);
            startDate = monthAgo.toISOString().split('T')[0];
            break;
    }
    
    if (startDate) {
        formData.value.orderTimeRange = [startDate, endDate];
    }
}

// 处理日期范围变化
function handleDateRangeChange(value: [string, string]) {
    if (value) {
        // 清空时间筛选
        timeFilter.value = '';
        // 重新赋值
        formData.value.orderTimeRange = value;
    }
}

function handleSearch() {
    emit('search', formData.value);
}

function handelExport() {
    emit('export', formData.value);
}

defineExpose({
    name: 'MyOrderRechargeForm',
})
</script>

<template>
    <base-form class="base-form" v-model="formData" :sets="{ inline: true }">
        <base-form-item class="select-input-item item">
            <base-select v-model="formData.rechargeSource" :options="recharge.rechargeSourceOptions" :sets="{ placeholder: '请选择充值来源' }" />
        </base-form-item>
        <base-form-item class="select-input-item item">
            <base-input v-model="formData.orderNo" :clearable="false" placeholder="请输入充值类型" />
        </base-form-item>
        <base-form-item class="select-input-item item">
            <base-select v-model="formData.orderStatus" :options="recharge.transactionStatusOptions" :sets="{ placeholder: '请选择交易状态' }" />
        </base-form-item>
        <base-form-item class="select-input-item item">
            <base-select v-model="formData.rechargeType" :options="recharge.paymentTypeOptions" :sets="{ placeholder: '请选择支付类型' }" />
        </base-form-item>
        <base-form-item class="radio-item item">
            <el-radio-group v-model="timeFilter" @input="TimeSelectChange">
                <el-radio-button
                    v-for="item in recharge.timeFilterOptions"
                    :key="item.value"
                    :value="item.value"
                    :disabled="item.disabled"
                >
                    {{ item.label }}
                </el-radio-button>
            </el-radio-group>
        </base-form-item>
        <base-form-item class="item">
            <base-date-picker
                v-model="timeSelectData"
                type="daterange"
                valueFormat="YYYY-MM-DD"
                @change="handleDateRangeChange"
            />
        </base-form-item>
        <base-button @click="handelExport">导出</base-button>
    </base-form>
</template>

<style lang="scss" scoped>
.base-form {
    width: 100%;
    display: flex;
    padding: var(--el-gap-half);
    background-color: var(--el-bg-color);
}

.item {
    margin-right: var(--el-gap);
}

.select-input-item {
    width: 180px;
}

.radio-item {
    width: 240px;
}
</style>
