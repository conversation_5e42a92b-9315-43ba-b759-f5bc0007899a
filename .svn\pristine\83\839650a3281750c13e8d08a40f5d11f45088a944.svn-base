const CONST = {
    recharge: {
        // 充值来源选项
        rechargeSourceOptions: [
            { label: '线上充值-PC', value: 'online_pc' },
            { label: '系统充值', value: 'system' },
            { label: '平台代充', value: 'platform' },
            { label: '平台代充-BOSS', value: 'platform_boss' },
            { label: '平台代充-代理商', value: 'platform_agent' },
            { label: '线上充值-H5', value: 'online_h5' },
            { label: '线上充值-钱包', value: 'online_wallet' },
            { label: '线上充值-课堂', value: 'online_classroom' }
        ] as TsSelect.Options,

        // 交易状态选项
        transactionStatusOptions: [
            { label: '待支付', value: 'pending' },
            { label: '支付成功', value: 'success' },
            { label: '支付失败', value: 'failed' },
            { label: '申请退款中', value: 'refund_applying' },
            { label: '退款失败', value: 'refund_failed' },
            { label: '退款成功', value: 'refund_success' }
        ] as TsSelect.Options,

        // 支付类型选项
        paymentTypeOptions: [
            { label: '支付宝', value: 'alipay' },
            { label: '微信', value: 'wechat' },
            { label: 'apple', value: 'apple' },
            { label: '系统赠送', value: 'system_gift' },
            { label: '线下充值', value: 'offline' },
            { label: 'paypal', value: 'paypal' },
            { label: '钻石', value: 'diamond' },
            { label: '兑换码', value: 'exchange_code' }
        ] as TsSelect.Options,

        // 时间筛选选项
        timeFilterOptions: [
            { label: '全部', value: 'all' },
            { label: '今日', value: 'today' },
            { label: '本周', value: 'week' },
            { label: '本月', value: 'month' }
        ] as TsRadio.Options,
    },

    spend: {
        // 消费商品类别选项
        productCategoryOptions: [
            { label: '视频模板', value: 'video_template' },
            { label: '台本', value: 'script' },
            { label: '专辑', value: 'album' },
            { label: '充值', value: 'recharge' },
            { label: '数字人购买', value: 'digital_human' },
            { label: '时长包', value: 'duration_package' },
            { label: '专业定制', value: 'custom_service' },
            { label: '名片模板', value: 'card_template' }
        ] as TsSelect.Options,

        // 消费渠道来源选项
        spendSourceOptions: [
            { label: 'PC', value: 'pc' },
            { label: 'APP', value: 'app' }
        ] as TsSelect.Options,

        // 支付状态选项
        paymentStatusOptions: [
            { label: '待支付', value: 'pending' },
            { label: '支付成功', value: 'success' },
            { label: '支付失败', value: 'failed' },
            { label: '申请退款中', value: 'refund_applying' },
            { label: '退款失败', value: 'refund_failed' },
            { label: '退款成功', value: 'refund_success' }
        ] as TsSelect.Options,

        // 时间筛选选项
        timeFilterOptions: [
            { label: '全部', value: 'all' },
            { label: '今日', value: 'today' },
            { label: '本周', value: 'week' },
            { label: '本月', value: 'month' }
        ] as TsRadio.Options,
    },

    rechargeDialog: {
        tabs: [
            { label: '充值积分', value: 'recharge_point' },
            { label: '通用视频时长', value: 'general_video_duration' },
            { label: '音频时长', value: 'audio_duration' },
            { label: '形象克隆S级', value: 'image_clone_s' },
            { label: '形象克隆E级', value: 'image_clone_e' },
            { label: '形象克隆S级-超清版', value: 'image_clone_s_hd' },
            { label: '声音克隆E级', value: 'voice_clone_e' },
            { label: '声音克隆S级', value: 'voice_clone_s' }
        ] as {
            label: string,
            value: TsMyOrder.Dialog.tab
        }[],
        fakeData: {
            map: {
                recharge_point: '0',
                general_video_duration: '5分钟',
                audio_duration: '5分钟',
                image_clone_s: '0次',
                image_clone_e: '0次',
                image_clone_s_hd: '0次',
                voice_clone_e: '0次',
                voice_clone_s: '0次',
            }
        }
     }
}

export default CONST;
