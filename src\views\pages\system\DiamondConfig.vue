<script setup lang="ts">
import type DialogDiamondConfigPut from '@/components/pages/diamondConfig/dialogDiamondConfigPut.vue';

// 表单ref
const formRef = useTemplateRef<TsForm.El>("formRef");
// 表单数据
const formData = reactive<TsApis.ApiParamsWithoutPage<"general", "get_diamond_config_list">>({
	diamondCount: undefined,
	amount: undefined,
	status: undefined,
});

// 获取状态字典数据
const { data: statusOptions } = apiDict.dict({ dictType: "sys_normal_disable" });

// 获取钻石配置列表数据
const {
    loading,
    data: diamondConfigData,
    page,
    pageSize,
    total,
    reload,
    remove
} = apiDiamondConfig.getList(formData);

// 重置按钮
function handleReset() {
	formRef.value?.resetFields();
	reload();
}

// 删除钻石配置
function handleDelete(row: (typeof diamondConfigData.value)[number]) {
	if (!row.id) return;
	ElMessageBox.confirm("确定要删除钻石配置【"+row.diamondCount+"钻石】吗？", "警告", {
		confirmButtonText: "确定",
		cancelButtonText: "取消",
		type: "warning",
	}).then(() => {
		remove(row);
	});
}

// 钻石配置新增弹框ref
const diamondConfigAddRef = useTemplateRef<{ open: () => void }>("diamondConfigAddRef");
// 打开新增钻石配置弹框
function handleAdd() {
	diamondConfigAddRef.value?.open();
}

// 钻石配置编辑弹框ref
const diamondConfigEditRef = useTemplateRef<InstanceType<typeof DialogDiamondConfigPut>>("diamondConfigEditRef");

// 打开编辑钻石配置弹框
function handleEdit(row: TsDiamondConfig.Row) {
	if (!row.id) return;
	diamondConfigEditRef.value?.open(row);
}
</script>

<template>
	<layout-table>
		<!-- 操作按钮 -->
		<template #handle>
			<base-button @click="handleAdd">新增</base-button>
		</template>

		<!-- 表格 -->
		<template #table>
			<base-table v-model="diamondConfigData" :sets="{ rowKey: 'id' }" v-loading="loading">
				<base-table-special type="selection" />
				<base-table-special type="index" />
				<base-table-column label="ID" prop="id" width="80" />
				<base-table-column label="钻石数量" prop="diamondCount" />
				<base-table-column label="金额" prop="amount" />
				<base-table-dict label="状态" prop="status" :options="statusOptions.response" />
				<base-table-column label="备注" prop="remark" show-overflow-tooltip />
				<base-table-column label="创建人" prop="createBy" />
				<base-table-date label="创建时间" prop="createTime">
                    <template #default="{ row }">
                        {{ row.createTime ? row.createTime : '--' }}
                    </template>
                </base-table-date>
				<base-table-column label="更新人" prop="updateBy" />
				<base-table-date label="更新时间" prop="updateTime">
                    <template #default="{ row }">
                        {{ row.updateTime ? row.updateTime : '--' }}
                    </template>
                </base-table-date>
				<base-table-special type="handle" width="180">
					<template #default="{ row }">
						<base-button @click="handleEdit(row)">编辑</base-button>
						<base-button @click="handleDelete(row)">删除</base-button>
					</template>
				</base-table-special>
			</base-table>
		</template>

		<!-- 分页 -->
		<template #pagination>
			<base-pagination v-model:current-page="page" v-model:page-size="pageSize" :total="total" />
		</template>
	</layout-table>

	<!-- 钻石配置新增弹框 -->
	<dialog-diamond-config-add ref="diamondConfigAddRef" />
	
	<!-- 钻石配置编辑弹框 -->
	<dialog-diamond-config-put ref="diamondConfigEditRef" />
</template>

<style scoped lang="scss">
</style>