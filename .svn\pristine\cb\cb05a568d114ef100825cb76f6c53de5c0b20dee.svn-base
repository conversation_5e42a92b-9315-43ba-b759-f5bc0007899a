declare namespace TsDigital {
    type State = 1 | 2 | 3;
    interface VideoOption {
        id: string;
        img: string;
        state: State;
        title: string;
        createTime: string;
    }
    interface VideoForm {
        title: string;
        state: State | "";
    }
    interface AudioOption {
        id: string;
        img: string;
        title: string;
        state: State;
        createTime: string;
    }
    interface AudioForm {
        title: string;
        state: State | "";
    }
    interface AudioMarketOption {
        id: string
        avatar: string
        name: string
        rating: number
        tags?: string[]
    }
    interface SoundOption {
        id: string
        avatar: string
        name: string
        rating: number
    }
    interface SoundForm {
        keyWord: string
        rating: number | ""
    }
}