<script setup lang="ts">
const props = defineProps<{
    modelValue: boolean;
    sets?: TsDialog.Sets
    fetchParam?: TsMyOrder.Dialog.fetchParam
}>()

const emits = defineEmits<{
    'update:modelValue': [show: boolean];
    cancel: []
    confirm: [orderNum: string]
}>()

// 对话框设置
const dialogSets = computed<TsDialog.Sets>(() => {
    return {
        title: '扫码支付',
        width: '400px',
        ...(props.sets),
    }
})

const show = computed({
    get: () => {
        return props.modelValue
    },
    set: (val) => {
        emits('update:modelValue', val)
    }
})

const qrCodeUrl = ref('data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAASwAAAEsAQAAAABRBrPYAAABWUlEQVR42u3aS5LCMAxFUe1MW/eSegchQT87iZJmfukqyoTDxC2jpxSy/efxJzAYDAbbhnwfuq/0+1KPRV2FNUz9ORcHrRewju1buV926CSuwt6YFaPWx2A/sGMpE4b1rE7xYdRse+pha/sY61/XZWCnuBINJHvHfaqB1fbaZlpgseXI+AJrmZ9c/+aL1pH7DLtlsan2NGxn81Ownm2W8bzjjmnGWE89bGXeLDRCnscW2COzAtRzOC4Lu2WeV2ogsyrdhsCemX/zVVL2cXatXtiZSbSP6X3LK+t/AXZlOYdlG7EACOtYiIornvXW7YVd6i0axxbVmcPZgLUsg7D32pj8YyiD3bMc/2Uqzbj5BGtZ3m7KkrQB1qsV1jGt0aLQlFdgDRvTzc3IKV6jCntj9Z7UhsNemfeOmDHqdMMaNmGRy91NWMMqm8w7fEnRsNO8wA+NYDAY7Jl9ANZ9uUdDWeFDAAAAAElFTkSuQmCC');
const loading = ref(false);
const orderNum = ref<string>('');

// 获取QrCode
async function getQrCode() {
    loading.value = true;
    try {
        const res = await apiPayDialog.getQrCode(props.fetchParam);
        qrCodeUrl.value = res.data.qrcode;
        orderNum.value = res.data.orderNo;
    } catch (error) {
        console.error('获取二维码失败:', error);
    } finally {
        loading.value = false;
    }
}

watch(() => props.modelValue, (val) => {
    if(val) {
        getQrCode()
    }
}, { immediate: false })

// 取消事件
function onCancel() {
    show.value = false;
    emits('cancel')
}

// 确认事件
function onConfirm() {
    show.value = false;
    emits('confirm', orderNum.value)
}

defineExpose({
    name: 'dialogRechargeScanQrCode',
})
</script>

<template>
    <base-dialog
        :model-value="show"
        :sets="dialogSets" 
        @cancel="onCancel" 
        @confirm="onConfirm"
    >
        <div class="qr-code-container">
            <div class="qr-code-wrapper">
                <img 
                    v-if="qrCodeUrl" 
                    :src="qrCodeUrl" 
                    alt="支付二维码" 
                    class="qr-code-image"
                />
                <div v-else class="qr-code-placeholder">
                    <base-icon icon="solar:qr-code-bold" size="80" color="#ddd" />
                    <p>二维码加载中...</p>
                </div>
                <div v-if="qrCodeUrl" class="wechat-icon">
                    <svg xmlns="http://www.w3.org/2000/svg" width="26" height="26" viewBox="0 0 24 24"><!-- Icon from Google Material Icons by Material Design Authors - https://github.com/material-icons/material-icons/blob/master/LICENSE --><path fill="currentColor" d="M15.85 8.14c.39 0 .77.03 1.14.08C16.31 5.25 13.19 3 9.44 3c-4.25 0-7.7 2.88-7.7 6.43c0 2.05 1.15 3.86 2.94 5.04L3.67 16.5l2.76-1.19c.59.21 1.21.38 1.87.47c-.09-.39-.14-.79-.14-1.21c-.01-3.54 3.44-6.43 7.69-6.43M12 5.89a.96.96 0 1 1 0 1.92a.96.96 0 0 1 0-1.92M6.87 7.82a.96.96 0 1 1 0-1.92a.96.96 0 0 1 0 1.92"/><path fill="currentColor" d="M22.26 14.57c0-2.84-2.87-5.14-6.41-5.14s-6.41 2.3-6.41 5.14s2.87 5.14 6.41 5.14c.58 0 1.14-.08 1.67-.2L20.98 21l-1.2-2.4c1.5-.94 2.48-2.38 2.48-4.03m-8.34-.32a.96.96 0 1 1 .96-.96c.01.53-.43.96-.96.96m3.85 0a.96.96 0 1 1 0-1.92a.96.96 0 0 1 0 1.92"/></svg>
                </div>
            </div>
            <div class="qr-code-tips">
                <p>请使用微信扫描二维码完成支付</p>
            </div>
        </div>
    </base-dialog>
</template>

<style scoped lang="scss">
$wechat-green: #44b549;

.qr-code-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 20px;
}

.qr-code-wrapper {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    width: 200px;
    height: 200px;
    border: 1px solid var(--el-border-color);
    border-radius: 8px;
    margin-bottom: 16px;
    background: var(--el-bg-color);
    position: relative;

    .wechat-icon {
        position: absolute;
        width: 28px;
        height: 28px;
        background: $wechat-green;
        border-radius: 4px;
        color: #fff;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        display: flex;
        align-items: center;
        justify-content: center;
    }
}

.qr-code-image {
    width: 180px;
    height: 180px;
    object-fit: contain;
}

.qr-code-placeholder {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    color: var(--el-text-color-placeholder);
    
    p {
        margin-top: 8px;
        font-size: 14px;
    }
}

.qr-code-tips {
    text-align: center;
    color: var(--el-text-color-regular);
    font-size: 14px;
    
    p {
        margin: 0;
    }
}
</style>