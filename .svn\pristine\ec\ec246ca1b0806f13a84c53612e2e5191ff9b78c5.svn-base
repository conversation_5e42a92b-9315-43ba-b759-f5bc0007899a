declare namespace TsMyOrder {
    namespace Recharge {
        interface Data {
            orderNo: string;
            rechargeSource: string;
            rechargeType: string;
            productName: string;
            rechargeAmount: string;
            paymentAmount: number;
            contractAmount: number;
            orderStatus: string;
            paymentMethod: string;
            orderTime: string;
            paymentTime: string;
            rechargeAccount: string;
            currentBalance: string;
            [key: string]: any;
        }
        interface FormData {
            orderNo: string;
            rechargeSource: string;
            rechargeType: string;
            orderStatus: string;
            orderTimeRange: string[];
            [key: string]: any;
        }
    }
    namespace Spend {
        interface Data {
            id: number,
            orderNo: string;
            productCategory: string;
            productName: string;
            productAmount: number;
            productPrice: number;
            orderStatus: string;
            orderTime: string;
        }
        interface FormData {
            productName: string;
            productCategory: string;
            spendSource: string;
            paymentStatus: string;
            orderTimeRange: string[];
        }
    }
    namespace Dialog {
        type tab = 'recharge_point' | 'general_video_duration' | 'audio_duration' | 'image_clone_s' | 'image_clone_e' | 'image_clone_s_hd' | 'voice_clone_e' | 'voice_clone_s';
        // @ts-ignore
        type insideComponents = DefineComponent<{
            balance: string;
        }, {
            updateChoose?: [point: number],
        }>
        type fetchParam = TsApis.ApiParams<'general', 'post_api_wechatpay_createorder'>
    }
}