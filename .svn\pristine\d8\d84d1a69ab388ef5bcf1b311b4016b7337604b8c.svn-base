namespace TsMyOrder {
    namespace Recharge {
        interface Data {
            orderNo: string;
            rechargeSource: string;
            rechargeType: string;
            productName: string;
            rechargeAmount: string;
            paymentAmount: number;
            contractAmount: number;
            orderStatus: string;
            paymentMethod: string;
            orderTime: string;
            paymentTime: string;
            rechargeAccount: string;
            currentBalance: string;
            [key: string]: any;
        }
        interface FormData {
            orderNo: string;
            rechargeSource: string;
            rechargeType: string;
            orderStatus: string;
            orderTimeRange: string[];
            [key: string]: any;
        }
    }
    namespace Spend {
        interface Data {
            orderNo: string;
            productCategory: string;
            productName: string;
            productAmount: number;
            productPrice: number;
            orderStatus: string;
            orderTime: string;
            [key: string]: any;
        }
        interface FormData {
            productName: string;
            productCategory: string;
            spendSource: string;
            paymentStatus: string;
            orderTimeRange: string[];
        }
    }
}