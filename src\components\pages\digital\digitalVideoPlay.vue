<script setup lang="ts">
const props = withDefaults(
	defineProps<{
		videoSrc: TsDigitalVideo.VideoSrc;
		coverUrl?: TsDigitalVideo.CoverUrl;
		config?: TsDigitalVideo.Config;
	}>(),
	{
		src: "",
		config: () => ({
			width: 200,
			height: 350,
			autoplay: false,
			controls: true,
			muted: false,
		}),
	}
);

const showVideo = ref(false);
const videoRef = ref<HTMLVideoElement>();
const isPlaying = ref(false);
const isLoading = ref(false);
const hasError = ref(false);

// 计算视频容器样式
const videoStyle = computed(() => {
	const width = typeof props.config.width === "number" ? `${props.config.width}px` : props.config.width;
	const height = typeof props.config.height === "number" ? `${props.config.height}px` : props.config.height;

	return {
		width,
		height,
	};
});
function onShow() {
	showVideo.value = true
}
// 播放/暂停切换
const togglePlay = () => {
	if (!videoRef.value) return;

	if (isPlaying.value) {
		videoRef.value.pause();
	} else {
		videoRef.value.play();
	}
};

// 视频事件处理
const onLoadStart = () => {
	isLoading.value = true;
	hasError.value = false;
};

const onLoadedData = () => {
	isLoading.value = false;
	hasError.value = false;
};
	
// 监听视频是否可以播放
const onCanPlay = () => {
	isLoading.value = false;
};

const onPlay = () => {
	isLoading.value = false;
	isPlaying.value = true;
};

const onPause = () => {
	isPlaying.value = false;
};

const onError = () => {
	isLoading.value = false;
	hasError.value = true;
	console.error("视频加载失败");
};
</script>

<template>
	<div class="video-player" :style="videoStyle" @mouseenter="onShow" @mouseleave="showVideo = false">
		<template v-if="!showVideo && coverUrl">
			<base-icon icon="solar:play-bold-duotone"></base-icon>
			<img :src="coverUrl" alt="封面图片" />
		</template>
		<template v-else>
			<!-- 视频元素 -->
			<video ref="videoRef" :src="props.videoSrc" :poster="props.config.poster" :autoplay="props.config.autoplay" :controls="props.config.controls" :muted="props.config.muted" :style="videoStyle" @loadstart="onLoadStart" @loadeddata="onLoadedData" @canplay="onCanPlay" @play="onPlay" @pause="onPause" @error="onError" @click="togglePlay" class="video-element" />
			<!-- 加载状态 -->
			<div v-if="isLoading" class="loading-overlay">
				<div class="loading-spinner">
					<i class="loading-icon"></i>
					<span>视频加载中...</span>
				</div>
			</div>

			<!-- 错误状态 -->
			<div v-if="hasError" class="error-overlay">
				<div class="error-content">
					<i class="error-icon">❌</i>
					<span>视频加载失败</span>
					<p class="error-tip">请检查视频链接是否正确</p>
				</div>
			</div>
		</template>
	</div>
</template>

<style scoped lang="scss">
.video-player {
	position: relative;
	overflow: hidden;
	background-color: var(--el-bg-color);
	display: block;

	img {
		display: block;
		width: 100%;
		height: 100%;
		object-fit: cover;
		transition: transform 0.3s;
	}
	.base-icon {
		position: absolute;
		font-size: 52px;
		left: calc(50% - 26px);
		top: calc(50% - 26px);
		z-index: 10;
		color: var(--el-color-white);
		opacity: 0.55;
	}
}

.video-element {
	width: 100%;
	height: 100%;
	object-fit: cover;
	cursor: pointer;
}

.loading-overlay,
.error-overlay {
	position: absolute;
	top: 0;
	left: 0;
	width: 100%;
	height: 100%;
	display: flex;
	align-items: center;
	justify-content: center;
	background-color: rgba(0, 0, 0, 0.7);
	color: white;
}

.loading-spinner {
	display: flex;
	flex-direction: column;
	align-items: center;
	gap: 12px;
}

.loading-icon {
	width: 24px;
	height: 24px;
	border: 3px solid rgba(255, 255, 255, 0.3);
	border-top: 3px solid #409eff;
	border-radius: 50%;
	animation: spin 1s linear infinite;
}

@keyframes spin {
	0% {
		transform: rotate(0deg);
	}
	100% {
		transform: rotate(360deg);
	}
}

.error-content {
	display: flex;
	flex-direction: column;
	align-items: center;
	gap: 8px;
	text-align: center;
}

.error-icon {
	font-size: 32px;
}

.error-tip {
	font-size: 12px;
	color: #ccc;
	margin: 4px 0 0 0;
}

/* 响应式设计 */
@media (max-width: 768px) {
	.video-player {
		max-width: 100%;
		height: auto;
	}

	.video-element {
		max-width: 100%;
		height: auto;
	}
}
</style>
