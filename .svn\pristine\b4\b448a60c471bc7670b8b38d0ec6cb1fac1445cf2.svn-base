export default {
    // 声音克隆列表查询（分页）
    getCloneList: () => {
        const method = (page: number, pageSize: number) =>
            Apis.general.post_aivatarapi_clonepagelist({
                name: "post_aivatarapi_clonepagelist",
                params: {
                    page,
                    size: pageSize,
                },
                transform: (res) => ({
                    total: res.data.totalRecord ?? 0,
                    data: res.data.records ?? [],
                }),
            });

        return alova.usePagination(method, {
            watchingStates: [],
            initialPage: 1,
            initialPageSize: 10,
            immediate: true,
            middleware: alova.actionDelegationMiddleware("post_aivatarapi_clonepagelist"),
            initialData: {
                total: 0,
                data: [],
            },
        });
    },
    
    // 声音克隆详情查询
    getCloneDetail: (id: string) => {
        const method = Apis.general.post_aivatarapi_clonedetail({
            name: "post_aivatarapi_clonedetail",
            params: {
                id
            }
        });
        
        return alova.useRequest(() => method, {
            immediate: true,
            middleware: alova.actionDelegationMiddleware("post_aivatarapi_clonedetail"),
            initialData: {}
        });
    },

    // 声音克隆音频合成接口
    synthesizeAudio: (params: TsApis.ApiData<"general","post_aivatarapi_fasttts">) => {
        const method = Apis.general.post_aivatarapi_fasttts({
            name: "post_aivatarapi_fasttts",
            data: {
                ...params
            },
        });

        return alova.useRequest(() => method, {
            immediate: false,
            middleware: alova.actionDelegationMiddleware("post_aivatarapi_fasttts"),
            initialData: {
                code: 0,
                msg: "",
                data: ""
            }
        });
    },

    // 声音市场数据列表
    getSpeakerList: () => {
        const method = (page: number, pageSize: number) => Apis.general.post_aivatarapi_speakerlist({
            name: "post_aivatarapi_speakerlist",
            params: {
                page,
                size: pageSize,
            },
            transform: (res) => ({
                total: res.total ?? 0,
                data: (res.rows ?? []).map(item => ({
                    ...item,
                    tags: item.ttsIntroduction.split("|")
                })),
            }),
        });

        return alova.usePagination(method, {
            immediate: true,
            initialPage: 1,
            initialPageSize: 10,
            initialData: {
                total: 0,
                data: [],
            }
        })
    },
    // 极速声音克隆
    fastClone: () => {
        const method = (params: TsApis.ApiData<"general","post_aivatarapi_fasttts">) => Apis.general.post_aivatarapi_fasttts({
            name: "post_aivatarapi_fasttts",
            data: params,
        })
        return alova.useRequest(method, {
            immediate: false,
            middleware: alova.actionDelegationMiddleware("post_aivatarapi_fasttts"),
        })
    },
    // 声音合成
    synthesize: () => {
        const method = (data: TsApis.ApiData<"general","post_aivatarapi_ttssynthesis">) => Apis.general.post_aivatarapi_ttssynthesis({
            name: "post_aivatarapi_ttssynthesis",
            data: data,
        })
        return alova.useForm(method, {
            immediate: false,
            middleware: alova.actionDelegationMiddleware("post_aivatarapi_ttssynthesis"),
            initialForm: {
                speakerId: "", // 声音id
                volume: 1, // 音量
                speechRate: 0.4, // 语速
                pitch: 0.6, // 语调
                content: "", // 文本内容
                srtFlag: "0", // 是否生成srt文件
                sampleRate: "", // 采样率
                async: false, // 是否异步
            }
        })
    },
    // 声音合成列表
    synthesizeList: () => {
        const method = (page: number, pageSize: number) => Apis.general.post_aivatarapi_ttspagelist({
            name: "post_aivatarapi_ttspagelist",
            params: {
                page: page,
                size: pageSize
            },
            transform: (res) => {
                return {
                    data: res.rows ?? [],
                    total: res.total ?? 0,
                };
            }
        })
        return alova.usePagination(method, {
            immediate: true,
            initialPage: 1,
            initialPageSize: 10,
            initialData: {
                total: 0,
                data: [],
            }
        })
    },
    // 声音合成详情
    synthesizeDetail: (id: number) => {
        const method = () =>Apis.general.post_aivatarapi_ttsdetail({
            name: "post_aivatarapi_ttsdetail",
            params: {
                id,
            }
        })
        return alova.useRequest(method, {
            immediate: true,
            middleware: alova.actionDelegationMiddleware("post_aivatarapi_ttsdetail"),
        })
    }
}