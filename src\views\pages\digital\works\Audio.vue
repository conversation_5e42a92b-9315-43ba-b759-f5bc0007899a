<script setup lang="ts">
// 使用synthesizeList接口获取数据
const { data, loading, page, pageSize, total } = apiDigitalAudio.synthesizeList();
function onEdit(id: number) {
    router.push({
        name: "AudioEdit",
        query: {
            id,
        }
    })
}
</script>
<template>
    <layout-table>
        <template #table>
            <div class="audio-container">
                <digital-audio-part v-for="option in data" :key="option.id" :option="option" :loading="loading" @edit="onEdit" />
            </div>
        </template>
        <template #pagination>
            <base-pagination v-model:current-page="page" v-model:page-size="pageSize" :total="total" />
        </template>
    </layout-table>
</template>
<style scoped lang="scss">
.audio-container {
    display: flex;
    gap: var(--el-gap);
    flex-wrap: wrap;
    background-color: var(--el-bg-color);
    border-radius: var(--el-border-radius-base);
}
</style>