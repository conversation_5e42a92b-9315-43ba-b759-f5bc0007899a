import fakeRechargeList from './fakeMyOrderRechargeGetList.json';
import fakeSpendList from './fakeMyOrderSpendList.json';

const API = {
    recharge: {
        getList(...args:any) {
            const mentod = (page: number, size: number) => {
                return new Promise<{
                    data: TsMyOrder.Recharge.Data[];
                    total: number;
                }>(res => {
                    setTimeout(() => {
                        res({
                            data: fakeRechargeList,
                            total: fakeRechargeList.length,
                        });
                    }, 1000)
                })
            }

            return mentod;
        }
    },
    spend: {
        getList(...args:any) {
            const mentod = (page: number, size: number) => {
                return new Promise<{
                    data: TsMyOrder.Spend.Data[];
                    total: number;
                }>(res => {
                    setTimeout(() => {
                        res({
                            data: fakeSpendList,
                            total: fakeSpendList.length,
                        });
                    }, 1000)
                })
            }

            return mentod;
        }
    }
}

export default API;
