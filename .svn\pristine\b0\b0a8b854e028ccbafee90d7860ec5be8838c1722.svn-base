<script setup lang="ts">
// 弹框显示状态
const visible = ref(false);
// 表单ref
const formRef = useTemplateRef<TsForm.El>("formRef");
// 获取部门数据
const { data: deptOptions } = apiDept.getTree();
// 获取状态字典数据
const { data: statusOptions } = apiDict.dict({ dictType: "sys_normal_disable" });
// 获取性别字典数据
const { data: sexOptions } = apiDict.dict({ dictType: "sys_user_sex" });
// 获取角色和岗位数据
const { data: userInitInfo } = apiUser.getInitInfo();

// 表单校验规则
const rules = reactive<TsElementPlus.FormRules>({
	userName: [{ required: true, message: "用户名不能为空", trigger: "blur" }],
	nickName: [{ required: true, message: "用户昵称不能为空", trigger: "blur" }],
	password: [{ required: true, message: "用户密码不能为空", trigger: "blur" }],
	phonenumber: [
		{
			pattern: REGEXP.phone,
			message: "请输入正确的手机号码",
			trigger: "blur",
		},
	],
	email: [
		{
			type: "email",
			message: "请输入正确的邮箱地址",
			trigger: "blur",
		},
	],
});

// 表单API
const { form:formData, send: addUser, loading: loadingAdd, reset: resetAdd, onSuccess } = apiUser.add();
onSuccess(() => {
	ElementPlus.ElMessage.success("新增用户成功");
	resetAdd();
	visible.value = false;
});

// 提交表单
function handleSubmit() {
	if (!formRef.value) return;
	formRef.value.validate().then(() => {
		addUser();
	});
}

// 取消
function handleCancel() {
	resetAdd();
	visible.value = false;
}

// 打开弹框
function open() {
	visible.value = true;
	resetAdd();
}

// 向父组件暴露方法
defineExpose({
	open,
});
</script>

<template>
	<base-dialog v-model="visible" title="新增用户" width="650px" @confirm="handleSubmit" @cancel="handleCancel">
		<base-form ref="formRef" :model-value="formData" :rules="rules" label-width="100px" v-loading="loadingAdd">
			<el-row :gutter="20">
				<el-col :span="12">
					<base-form-item label="用户昵称" prop="nickName">
						<base-input v-model="formData.nickName" placeholder="请输入用户昵称" />
					</base-form-item>
				</el-col>
				<el-col :span="12">
					<base-form-item label="归属部门" prop="deptId">
						<base-tree-select v-model="formData.deptId" :options="deptOptions" />
					</base-form-item>
				</el-col>
				<el-col :span="12">
					<base-form-item label="手机号码" prop="phonenumber">
						<base-input v-model="formData.phonenumber" placeholder="请输入手机号码" />
					</base-form-item>
				</el-col>
				<el-col :span="12">
					<base-form-item label="邮箱" prop="email">
						<base-input v-model="formData.email" placeholder="请输入邮箱" />
					</base-form-item>
				</el-col>
				<el-col :span="12">
					<base-form-item label="用户名称" prop="userName">
						<base-input v-model="formData.userName" placeholder="请输入用户名称" />
					</base-form-item>
				</el-col>
				<el-col :span="12">
					<base-form-item label="密码" prop="password">
						<base-input v-model="formData.password" type="password" placeholder="请输入密码" />
					</base-form-item>
				</el-col>
				<el-col :span="12">
					<base-form-item label="用户性别" prop="sex">
						<base-select v-model="formData.sex" :options="sexOptions.options" />
					</base-form-item>
				</el-col>
				<el-col :span="12">
					<base-form-item label="用户状态" prop="status">
						<base-radio v-model="formData.status" :options="statusOptions.options" />
					</base-form-item>
				</el-col>
				<el-col :span="12">
					<base-form-item label="岗位" prop="postIds">
						<base-select v-model="formData.postIds" :options="userInitInfo.posts" multiple placeholder="请选择岗位" />
					</base-form-item>
				</el-col>
				<el-col :span="12">
					<base-form-item label="角色" prop="roleIds">
						<base-select v-model="formData.roleIds" :options="userInitInfo.roles" multiple placeholder="请选择角色" />
					</base-form-item>
				</el-col>
				<el-col :span="24">
					<base-form-item label="备注" prop="remark">
						<base-input v-model="formData.remark" type="textarea" placeholder="请输入备注" />
					</base-form-item>
				</el-col>
			</el-row>
		</base-form>
	</base-dialog>
</template>
