/// <reference types='./globals.d.ts' />
/* tslint:disable */
/* eslint-disable */
/**
 * 数字人 - version 1.0.0
 *
 *
 *
 * OpenAPI version: 3.0.0
 *
 *
 * NOTE: This file is auto generated by the alova's vscode plugin.
 *
 * https://alova.js.org/devtools/vscode
 *
 * **Do not edit the file manually.**
 */
export default {
  'general.get_captchaimage': ['GET', '/captchaImage'],
  'general.post_login': ['POST', '/login'],
  'token.post_login': ['POST', '/login'],
  'general.post_logout': ['POST', '/logout'],
  'general.get_getinfo': ['GET', '/getInfo'],
  'general.get_system_user_list': ['GET', '/system/user/list'],
  'general.get_system_user_list': ['GET', '/system/user/list'],
  'general.post_system_user': ['POST', '/system/user'],
  'general.post_system_user': ['POST', '/system/user'],
  'general.put_system_user': ['PUT', '/system/user'],
  'general.put_system_user': ['PUT', '/system/user'],
  'general.get_system_user_userid': ['GET', '/system/user/{userId}'],
  'general.get_system_user_userid': ['GET', '/system/user/{userId}'],
  'general.delete_system_user_userid': ['DELETE', '/system/user/{userId}'],
  'general.delete_system_user_userid': ['DELETE', '/system/user/{userId}'],
  'general.get_system_user': ['GET', '/system/user/'],
  'general.put_system_user_resetpwd': ['PUT', '/system/user/resetPwd'],
  'general.put_system_user_changestatus': ['PUT', '/system/user/changeStatus'],
  'general.get_system_user_profile': ['GET', '/system/user/profile'],
  'general.put_system_user_profile': ['PUT', '/system/user/profile'],
  'general.get_system_user_authrole_userid': ['GET', '/system/user/authRole/{userId}'],
  'general.post_system_user_importdata': ['POST', '/system/user/importData'],
  'general.get_system_user_depttree': ['GET', '/system/user/deptTree'],
  'general.get_system_dict_data_type_dicttype': ['GET', '/system/dict/data/type/{dictType}'],
  'general.post_aivatarapi_speakerlist': ['POST', '/aiVatarApi/speakerList'],
  'general.post_aivatarapi_fasttts': ['POST', '/aiVatarApi/fastTts'],
  'general.post_aivatarapi_ttssynthesis': ['POST', '/aiVatarApi/ttsSynthesis'],
  'general.post_aivatarapi_ttspagelist': ['POST', '/aiVatarApi/ttsPageList'],
  'general.post_aivatarapi_ttsdetail': ['POST', '/aiVatarApi/ttsDetail'],
  'general.post_aivatarapi_clone': ['POST', '/aiVatarApi/clone'],
  'general.post_aivatarapi_clonepagelist': ['POST', '/aiVatarApi/clonePageList'],
  'general.post_aivatarapi_clonedetail': ['POST', '/aiVatarApi/cloneDetail'],
  'general.post_aivatarapi_fastclone': ['POST', '/aiVatarApi/fastClone'],
  'general.post_aivatarapi_creatdigitalcard': ['POST', '/aiVatarApi/creatDigitalCard'],
  'general.post_aivatarapi_getdigitalcard': ['POST', '/aiVatarApi/getDigitalCard'],
  'general.post_aivatarapi_robotlist': ['POST', '/aiVatarApi/robotList'],
  'general.post_aivatarapi_createvideobytext': ['POST', '/aiVatarApi/createVideoByText'],
  'general.post_aivatarapi_video': ['POST', '/aiVatarApi/video'],
  'general.post_aivatarapi_videotaskinfo': ['POST', '/aiVatarApi/videoTaskInfo'],
  'general.post_aivatarapi_getvideolist': ['POST', '/aiVatarApi/getVideoList'],
  'general.post_aivatarapi_getmessioninfo': ['POST', '/aiVatarApi/getMessionInfo'],
  'general.post_aivatarapi_freerobotlistgetall': ['POST', '/aiVatarApi/freeRobotListGetAll'],
  'general.post_aivatarapi_dzrobotlist': ['POST', '/aiVatarApi/dzRobotList'],
  'general.post_aivatarapi_creat3dvideobytext': ['POST', '/aiVatarApi/creat3DVideoByText'],
  'general.post_aivatarapi_creat3dvideobyaudio': ['POST', '/aiVatarApi/creat3DVideoByAudio'],
  'general.post_aivatarapi_createvideobyaudio': ['POST', '/aiVatarApi/createVideoByAudio'],
  'general.post_aivatarapi_simplecreate': ['POST', '/aiVatarApi/simpleCreate'],
  'general.post_aivatarapi_getuserinfo': ['POST', '/aiVatarApi/getUserInfo'],
  'general.post_aivatarapi_getmemberinfo': ['POST', '/aiVatarApi/getMemberInfo'],
  'general.post_aivatarapi_getuserlist': ['POST', '/aiVatarApi/getUserList'],
  'general.post_aivatarapi_getsucai': ['POST', '/aiVatarApi/getsucai'],
  'general.post_aivatarapi_getfontcategorylist': ['POST', '/aiVatarApi/getFontCategoryList'],
  'general.post_aivatarapi_gettextstylelist': ['POST', '/aiVatarApi/getTextStyleList'],
  'general.post_aivatarapi_digitalcardstyle': ['POST', '/aiVatarApi/digitalCardStyle'],
  'general.post_aivatarapi_gettemporaryuploadurl': ['POST', '/aiVatarApi/getTemporaryUploadUrl'],
  'general.post_aivatarapi_sendmsg': ['POST', '/aiVatarApi/sendMsg'],
  'general.post_register': ['POST', '/register'],
  'general.post_aivatarapi_gettraininglist': ['POST', '/aiVatarApi/getTrainingList'],
  'general.post_aivatarapi_gettrainingbyid': ['POST', '/aiVatarApi/getTrainingById'],
  'general.post_aivatarapi_training': ['POST', '/aiVatarApi/training'],
  'general.get_tabaccount_tabaccount_list': ['GET', '/tabAccount/tabAccount/list'],
  'general.post_api_wechatpay_createorder': ['POST', '/api/wechatPay/createOrder'],
  'general.post_api_wechatpay_checkpaystatus': ['POST', '/api/wechatPay/checkPayStatus'],
  'general.post_api_wechatpay_wechatpaynotify': ['POST', '/api/wechatPay/wechatPayNotify'],
  'general.post_diamond_config_add': ['POST', '/diamond/config/add'],
  'general.delete_diamond_config_id': ['DELETE', '/diamond/config/{id}'],
  'general.get_diamond_config_id': ['GET', '/diamond/config/{id}'],
  'general.put_diamond_config': ['PUT', '/diamond/config'],
  'general.get_diamond_config_list': ['GET', '/diamond/config/list'],
  'general.post_otherconfig_otherconfig_add': ['POST', '/otherConfig/otherConfig/add'],
  'general.get_otherconfig_otherconfig_list': ['GET', '/otherConfig/otherConfig/list'],
  'general.get_otherconfig_otherconfig_id': ['GET', '/otherConfig/otherConfig/{id}'],
  'general.put_otherconfig_otherconfig': ['PUT', '/otherConfig/otherConfig'],
  'general.delete_otherconfig_otherconfig_ids': ['DELETE', '/otherConfig/otherConfig/{ids}'],
  'general.post_tabaccount_tabaccount_exchange': ['POST', '/tabAccount/tabAccount/exchange']
};
