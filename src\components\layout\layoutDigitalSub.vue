<script setup lang="ts">
interface SegmentOption {
  label: string;
  value: string;
  icon?: string;
}

const {keepAlive} = pinia.storeToRefs(storeApp());
const route = useRoute();
const value = ref(route.name as string)
watch(() => route.name as string, (val) => value.value = val);
const options = computed(() => utilSegmentOptionsByName(route.name as string, dataRoutesDigital[0]!.children!) as SegmentOption[]);
function onChange(value: string) {
  router.push({
    name: value
  })
}
</script>
<template>
  <div class="digital-works">
    <div class="works-segmented" v-show="!route?.meta?.isDetail">
      <el-segmented
          v-model="value"
          :options="options"
          direction="horizontal"
          @change="onChange"
      >
        <template #default="scope">
          <base-icon :icon="(scope.item as SegmentOption).icon || ''" v-if="(scope.item as SegmentOption).icon"/>
          <div>{{ (scope.item as SegmentOption).label }}</div>
        </template>
      </el-segmented>
    </div>
    <div class="works-view" :class="{'no-segmented': route?.meta?.isDetail}">
      <router-view v-slot="{ Component }">
        <keep-alive :include="keepAlive">
          <component :is="Component"/>
        </keep-alive>
      </router-view>
    </div>
  </div>
</template>
<style scoped>
.digital-works {
  height: 100%;
  --var-segmented-height: 44px;
}

.works-segmented {
  background-color: var(--el-bg-color);
  border-radius: var(--el-border-radius-base);
  padding: var(--el-gap-half);
}

.digital-works :deep(.el-segmented__item) {
  padding: var(--el-gap-half) var(--el-gap);
}

.digital-works :deep(.el-segmented__item-label) {
  display: flex;
  align-items: center;
  gap: var(--el-gap-half);
}

.digital-works :deep(.base-icon) {
  font-size: var(--el-font-size-large);
}

.works-view {
  margin-top: var(--el-gap-half);
  height: calc(100% - var(--var-segmented-height) - var(--el-gap-half) * 3);
  overflow: auto;

  &.no-segmented {
      height: inherit;
      margin: 0;
  }
}
</style>