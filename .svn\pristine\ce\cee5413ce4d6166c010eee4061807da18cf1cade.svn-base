<script setup lang="ts">
const {sets = {}} = defineProps<{
    label?: TsFormItem.Label;
    prop?: TsFormItem.Prop;
    sets?: TsFormItem.Sets;
}>()

const setsForm = inject<TsBase.Object>("setsForm", {});
const setsFormItem = computed(() => {
    const res = JSON.parse(JSON.stringify({...setsForm,...sets}));
    if(res.labelPosition === 'inner') delete res.labelPosition;
    return res;
})
const isLabelInner = computed(() => {
    return {...setsForm,...sets}.labelPosition === "inner"
});
</script>
<template>
    <el-form-item
        :class="{'base-form-item':true,'label-inner': isLabelInner}"
        :label="isLabelInner ? '' : label"
        :prop="prop"
        v-bind="setsFormItem"
    >
        <slot></slot>
        <fieldset class="base-form-fieldset" v-if="isLabelInner">
            <legend class="base-form-legend">
                <span class="base-form-legend-label">{{ label }}</span>
            </legend>
        </fieldset>
    </el-form-item>
</template>
<style scoped>
.base-form-item :deep(.el-form-item__content) {
    position: relative;
}
.base-form-item:has(.base-form-fieldset) :deep(.el-form-item__content) {
    padding: 5px 0;
}
.base-form-fieldset {
    --top: -0.5em;
    position: absolute;
    top: var(--top);
    width: 100%;
    height: calc(100% - var(--top));
    padding: 0 6px;
    line-height: 1em;
    border-radius: var(--el-input-border-radius, var(--el-border-radius-base));
    border: 1px solid var(--el-input-border-color, var(--el-border-color));
    cursor: text;
    pointer-events: none;
}

.base-form-item.is-error .base-form-fieldset {
    border-color: var(--el-color-danger);
}

.base-form-legend-label {
    display: block;
    white-space: nowrap;
    transform: translate(0, 0);
    font-size: 0.86em;
    transition: transform var(--el-transition-duration);
    padding: 0 3px;
}

.base-form-legend {
    color: var(--el-text-color-primary);
}

.base-form-item :deep(.el-form-item__content:has(.is-focus)) .base-form-fieldset,
.base-form-item :deep(.el-form-item__content:has(.is-active)) .base-form-fieldset,
.base-form-item :deep(.el-form-item__content:has(.is-focused)) .base-form-fieldset {
    border-color: var(--el-color-primary);
}
.label-inner :deep(.el-input__wrapper),
.label-inner.is-error :deep(.el-input__wrapper),
.label-inner.is-error :deep(.el-input__wrapper:hover),
.label-inner :deep(.el-select__wrapper),
.label-inner :deep(.el-select__wrapper.is-hovering),
.label-inner.is-error :deep(.el-select__wrapper),
.label-inner.is-error :deep(.el-select__wrapper:hover),
.label-inner :deep(.el-date-editor),
.label-inner :deep(.el-date-editor:hover),
.label-inner.is-error :deep(.el-date-editor),
.label-inner.is-error :deep(.el-date-editor:hover),
.label-inner :deep(.el-input-group__append){
    box-shadow: none;
    background-color: transparent;
}
</style>