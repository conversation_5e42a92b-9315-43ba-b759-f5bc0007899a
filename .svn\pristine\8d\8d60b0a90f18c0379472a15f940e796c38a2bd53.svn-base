<template>
	<div class="audio-editor-container">
		<div class="left-panel">
			<!-- 文本编辑区域 -->
			<div class="text-editor-container">
				<el-input type="textarea" v-model="textareaContent" maxlength="20000" show-word-limit class="full-height-input" />
			</div>
			<!-- 音频播放控制区域 -->
			<div class="audio-player">
				<div class="audio-info">
					<base-input v-model="audioParams.title" placeholder="请输入音频标题" />
					<base-button @click="onSend">AI合成</base-button>
				</div>
				<div class="audio-controls">
					<div class="avatar-container">
						<img :src="audioParams.avatar" alt="avatar" class="avatar" />
					</div>
					<el-slider v-model="currentTime" :max="duration" :disabled="!isSynthesized && !audioParams.audition" @change="onProgressChange" class="progress-slider" />
					<span class="time-display">{{ formatTime(currentTime) }} / {{ formatTime(duration) }}</span>
					<base-button size="small" @click="togglePlay" :disabled="!textareaContent || !audioParams.audition" :sets="listenSets" class="listen-button">{{ isPlaying ? "暂停" : "播放" }}</base-button>
				</div>
				<!-- 隐藏的音频元素 -->
				<audio ref="audioRef" :src="audioParams.audition" @loadedmetadata="onLoadedMetadata" @timeupdate="onTimeUpdate" @ended="onAudioEnded" preload="metadata"></audio>
			</div>
		</div>

		<!-- 右侧区域：音频参数控制 -->
		<div class="right-panel">
			<div class="model-section">
				<div class="model-label">模型</div>
				<div class="model-value">文姐</div>
			</div>
			<div class="param-control">
				<div class="model-label">音频效果</div>
				<div class="param-row">
					<span class="param-label">语速</span>
					<span class="param-value">{{ audioParams.speed.toFixed(1) }}</span>
				</div>
				<el-slider v-model="audioParams.speed" :min="0.5" :max="2" :step="0.1" class="param-slider" />
			</div>
			<div class="param-control">
				<div class="param-row">
					<span class="param-label">音量</span>
					<span class="param-value">{{ audioParams.volume }}</span>
				</div>
				<el-slider v-model="audioParams.volume" :min="0" :max="100" class="param-slider" />
			</div>
			<div class="param-control">
				<div class="param-row">
					<span class="param-label">语调</span>
					<span class="param-value">{{ audioParams.pitch.toFixed(1) }}</span>
				</div>
				<el-slider v-model="audioParams.pitch" :min="0.5" :max="2" :step="0.1" class="param-slider" />
			</div>
			<div class="param-control">
				<span class="param-label">采样率</span>
				<el-select v-model="audioParams.sampleRate" class="param-select">
					<el-option label="16K" value="16000" />
					<el-option label="28K" value="28000" />
					<el-option label="48K" value="48000" />
				</el-select>
			</div>

			<div class="param-control">
				<span class="param-label">变声</span>
				<el-select v-model="audioParams.voiceChange" class="param-select">
					<el-option label="原声" value="original" />
					<el-option label="清新女生" value="male" />
					<el-option label="醇厚男生" value="female" />
					<el-option label="活力男生" value="child" />
					<el-option label="甜美女生" value="robot" />
				</el-select>
			</div>
		</div>
	</div>
</template>

<script setup lang="ts">
const { soundEdit } = pinia.storeToRefs(storePassing());
console.log(123,soundEdit.value);
// 文本内容
const textareaContent = ref("");
// 是否已合成
const isSynthesized = ref(false);
// 音频播放状态
const isPlaying = ref(false);
// 音频相关状态
const currentTime = ref(0);
const duration = ref(0);
const audioRef = ref<HTMLAudioElement>();

// 按钮设置
const listenSets = computed(
	(): TsButton.Sets => ({
    disabled: !isSynthesized.value || !textareaContent.value,
		icon: isPlaying.value ? markRaw(IconSolarPauseLinear) : markRaw(IconSolarPlayLinear),
	})
);
// 音频参数
const audioParams = reactive<TsDigitalSound.AudioParams>({
  avatar: soundEdit.value?.ttsCover ?? "", // 头像
  audition: soundEdit.value?.ttsAudition ?? "", // 音频文件
  title: "未命名标题", // 标题
	speed: 1, // 语速
	volume: 80, // 音量
	pitch: 1, // 语调
	sampleRate: "48K", // 采样率
	voiceChange: "original", // 变声
});

// 合成接口
const { send: sendAI } = apiDigitalAudio.fastClone();
function onSend() {
  sendAI({
      speakerId: soundEdit.value!.id.toString(),
      content: textareaContent.value,
      speechRate: audioParams.pitch,
  }).then(() => {
    isSynthesized.value = true;
  })
}
// 格式化时间显示
const formatTime = (seconds: number): string => {
	const mins = Math.floor(seconds / 60);
	const secs = Math.floor(seconds % 60);
	return `${mins.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
};

// 音频元数据加载完成
const onLoadedMetadata = () => {
	if (audioRef.value) {
		duration.value = audioRef.value.duration;
	}
};

// 音频时间更新
const onTimeUpdate = () => {
	if (audioRef.value && !isDragging.value) {
		currentTime.value = audioRef.value.currentTime;
	}
};

// 音频播放结束
const onAudioEnded = () => {
	isPlaying.value = false;
	currentTime.value = 0;
};

// 进度条拖拽状态
const isDragging = ref(false);

// 进度条改变事件
const onProgressChange = () => {
	if (audioRef.value) {
		audioRef.value.currentTime = currentTime.value;
	}
};

// 播放/暂停切换
const togglePlay = () => {
	if (!audioRef.value || !audioParams.audition) return;

	if (isPlaying.value) {
		audioRef.value.pause();
	} else {
		audioRef.value.play();
	}
	isPlaying.value = !isPlaying.value;
};
</script>

<style scoped>
.audio-editor-container {
	display: flex;
	width: 100%;
	height: 100%;
	overflow: hidden;
	background-color: var(--el-bg-color);
	border-radius: var(--el-border-radius-middle);

	.left-panel {
		flex: 1;
		display: flex;
		flex-direction: column;
		padding: var(--el-gap);

		.text-editor-container {
			flex: 1;
			position: relative;
			margin-bottom: 20px;
			min-height: 0;
			display: flex;
			flex-direction: column;

			.text-editor {
				width: 100%;
				height: 100%;
				border: none;
				outline: none;
				padding: 10px;
				font-size: 14px;
				line-height: 1.6;
				background-color: transparent;
				box-sizing: border-box;
			}

			.word-count {
				position: absolute;
				bottom: 10px;
				right: 10px;
				padding: 5px 10px;
				color: #666;
				font-size: 12px;
				pointer-events: none;
			}
		}

		.audio-player {
			padding: 15px;
			background-color: #f9fafb;
			border-radius: 6px;
			display: flex;
			flex-direction: column;
			gap: 10px;

			.audio-info {
				display: flex;
				align-items: center;
				gap: var(--el-gap);
			}

			.audio-controls {
				display: flex;
				align-items: center;
				gap: var(--el-gap);

				.avatar-container {
					width: 60px;
					height: 60px;

					.avatar {
						width: 100%;
						height: 100%;
						border-radius: 50%;
						object-fit: cover;
					}
				}

				.play-button {
					width: 40px;
					height: 40px;
					display: flex;
					align-items: center;
					justify-content: center;
					padding: 0;
					border: none;

					.play-icon {
						width: 100%;
						height: 100%;
						font-size: 40px;
					}
				}

				.progress-slider {
					flex: 1;
				}

				.time-display {
					width: 80px;
					text-align: center;
					font-size: 12px;
					color: #666;
				}

				.listen-button {
					width: 120px;
					height: 40px;
					font-size: 16px;
					background-color: #409eff;
					color: white;
					border: none;
				}
			}
		}
	}

	.right-panel {
		width: 350px;
		padding: 20px;
		background-color: #f9fafb;
		overflow-y: auto;

		.model-section {
			margin-bottom: 10px;
			border-bottom: 1px solid #e5e7eb;
			padding-bottom: 20px;

			.model-label {
				font-size: 14px;
				margin-bottom: 4px;
				color: #949aa5;
			}

			.model-value {
				margin-top: 20px;
				font-size: 14px;
			}
		}

		.param-control {
			display: flex;
			flex-direction: column;
			margin-bottom: 20px;
			padding: 10px;
			background-color: #fff;
			border-radius: 6px;
			box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);

			.param-row {
				display: flex;
				justify-content: space-between;
				align-items: center;
				margin-top: 10px;
				margin-bottom: 8px;

				.param-label {
					font-size: 14px;
				}

				.param-value {
					font-size: 12px;
					color: #666;
				}
			}

			.param-slider {
				width: 100%;
			}

			.param-select {
				width: 100%;
			}
		}
	}
}

.full-height-input {
	flex: 1;
	display: flex;
	flex-direction: column;
}

.full-height-input :deep(.el-textarea) {
	flex: 1;
	display: flex;
	flex-direction: column;
}

.full-height-input :deep(.el-textarea__inner) {
	flex: 1;
	resize: none;
}

:deep(.el-slider__button) {
	background-color: #409eff;
	border-color: #409eff;
}

:deep(.el-slider__bar) {
	background-color: #409eff;
}
</style>
