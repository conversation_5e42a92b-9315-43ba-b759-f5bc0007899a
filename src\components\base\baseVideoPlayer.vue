<script setup lang="ts">
interface VideoPlayerProps {
  videoSrc: string
  width?: string | number
  height?: string | number
  poster?: string
  autoplay?: boolean
  controls?: boolean
  muted?: boolean
}

const props = withDefaults(defineProps<VideoPlayerProps>(), {
  width: '200px',
  height: '350px',
  autoplay: false,
  controls: true,
  muted: false
})

const videoRef = ref<HTMLVideoElement>()
const isPlaying = ref(false)
const isLoading = ref(true)
const hasError = ref(false)

// 计算视频容器样式
const videoStyle = computed(() => {
  const width = typeof props.width === 'number' ? `${props.width}px` : props.width
  const height = typeof props.height === 'number' ? `${props.height}px` : props.height
  
  return {
    width,
    height
  }
})

// 播放/暂停切换
const togglePlay = () => {
  if (!videoRef.value) return
  
  if (isPlaying.value) {
    videoRef.value.pause()
  } else {
    videoRef.value.play()
  }
}

// 视频事件处理
const onLoadStart = () => {
  isLoading.value = true
  hasError.value = false
}

const onLoadedData = () => {
  isLoading.value = false
}

const onPlay = () => {
  isPlaying.value = true
}

const onPause = () => {
  isPlaying.value = false
}

const onError = () => {
  isLoading.value = false
  hasError.value = true
  console.error('视频加载失败')
}
</script>

<template>
  <div class="video-player" :style="videoStyle">
    <!-- 视频元素 -->
    <video
      ref="videoRef"
      :src="videoSrc"
      :poster="poster"
      :autoplay="autoplay"
      :controls="controls"
      :muted="muted"
      :style="videoStyle"
      @loadstart="onLoadStart"
      @loadeddata="onLoadedData"
      @play="onPlay"
      @pause="onPause"
      @error="onError"
      @click="togglePlay"
      class="video-element"
    />
    
    <!-- 加载状态 -->
    <div v-if="isLoading" class="loading-overlay">
      <div class="loading-spinner">
        <i class="loading-icon"></i>
        <span>视频加载中...</span>
      </div>
    </div>
    
    <!-- 错误状态 -->
    <div v-if="hasError" class="error-overlay">
      <div class="error-content">
        <i class="error-icon">❌</i>
        <span>视频加载失败</span>
        <p class="error-tip">请检查视频链接是否正确</p>
      </div>
    </div>
  </div>
</template>

<style scoped>
.video-player {
  position: relative;
  border-radius: 8px;
  overflow: hidden;
  background-color: #000;
  display: inline-block;
}

.video-element {
  width: 100%;
  height: 100%;
  object-fit: cover;
  cursor: pointer;
}

.loading-overlay,
.error-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: rgba(0, 0, 0, 0.7);
  color: white;
}

.loading-spinner {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 12px;
}

.loading-icon {
  width: 24px;
  height: 24px;
  border: 3px solid rgba(255, 255, 255, 0.3);
  border-top: 3px solid #409eff;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.error-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8px;
  text-align: center;
}

.error-icon {
  font-size: 32px;
}

.error-tip {
  font-size: 12px;
  color: #ccc;
  margin: 4px 0 0 0;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .video-player {
    max-width: 100%;
    height: auto;
  }
  
  .video-element {
    max-width: 100%;
    height: auto;
  }
}
</style>