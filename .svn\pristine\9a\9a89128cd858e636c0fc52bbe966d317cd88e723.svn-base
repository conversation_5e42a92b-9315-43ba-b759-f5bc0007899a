<script setup lang="ts">
// 弹框显示状态
const visible = ref(false);
// 表单ref
const formRef = useTemplateRef<TsForm.El>("formRef");
// 用户信息
const userInfo = reactive({
    userId: 0 as TsUser.Id,
    userName: "",
    nickName: ""
});

// 表单校验规则
const rules = reactive({
    password: [
        { required: true, message: "密码不能为空", trigger: "blur" },
        { min: 6, message: "密码长度不能小于6个字符", trigger: "blur" }
    ],
});

// 重置密码
const { form: formData, send: resetPassword, loading: loadingReset, onSuccess } = apiUser.resetPassword();

onSuccess(() => {
    visible.value = false;
});

// 提交表单
function handleSubmit() {
    if (!formRef.value) return;
    formRef.value.validate().then(() => {
        formData.value.userId = userInfo.userId;
        resetPassword();
    });
}

// 取消
function handleCancel() {
    visible.value = false;
    formData.value.password = "";
}

// 打开弹框
function open(row: { userId: TsUser.Id; userName: string; nickName: string }) {
    visible.value = true;
    formData.value.password = "";
    userInfo.userId = row.userId;
    userInfo.userName = row.userName;
    userInfo.nickName = row.nickName;
}

// 向父组件暴露方法
defineExpose({
    open,
});
</script>

<template>
    <base-dialog v-model="visible" title="重置密码" width="500px" @confirm="handleSubmit" @cancel="handleCancel">
        <base-form ref="formRef" :model-value="formData" :rules="rules" label-width="100px" v-loading="loadingReset">
            <el-row>
                <el-col :span="12">
                    <base-form-item label="用户名称">
                        <span>{{ userInfo.userName }}</span>
                    </base-form-item>
                </el-col>
                <el-col :span="12">
                    <base-form-item label="用户昵称">
                        <span>{{ userInfo.nickName }}</span>
                    </base-form-item>
                </el-col>
            </el-row>
            <base-form-item label="新密码" prop="password">
                <base-input v-model="formData.password" type="password" placeholder="请输入新密码" show-password />
            </base-form-item>
        </base-form>
    </base-dialog>
</template> 