<script setup lang="ts">
defineProps<{
    label: TsTableSwitch.Label,
    prop: TsTableSwitch.Prop,
    sets?: TsTableSwitch.Sets<TsBase.Object>,
}>()

const emits = defineEmits<{
    change: [val:TsSwitch.Change,row:TsTable.Row],
}>();
function onChange(val:TsSwitch.Change, row:TsTable.Row) {
    emits("change",val,row);
}

</script>
<template>
    <base-table-column :label="label" :prop="prop" width="76" :sets="sets">
        <template #default="scope">
            <base-switch
                v-bind="$attrs"
                v-model="scope.row[prop]"
                :sets="{
                    colorOn:'var(--el-color-success)',
                    colorOff:'var(--el-color-danger)',
                    ...sets
                }"
                @change="(val:TsSwitch.Change) => onChange(val,scope.row)"
                v-if="scope.row[prop] != undefined"
            ></base-switch>
            <span v-else>--</span>
        </template>
    </base-table-column>
</template>