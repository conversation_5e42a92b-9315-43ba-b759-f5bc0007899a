<script setup lang="ts">
const {sets = {}} = defineProps<{
    options: TsBreadcrumb.Options,
    sets?: TsBreadcrumb.Sets
}>()
</script>
<template>
    <el-breadcrumb v-bind="sets" class="base-breadcrumb">
        <el-breadcrumb-item v-for="item in options" :key="item.name" :to="{name:item.name}">
            <slot :name="item.name">
                <div class="base-breadcrumb-item">
                    <base-icon :icon="item.icon" v-if="item.icon"/>
                    <span>{{ item.label }}</span>
                </div>
            </slot>
        </el-breadcrumb-item>
    </el-breadcrumb>
</template>
<style scoped>
.base-breadcrumb-item {
    display: flex;
    align-items: center;
    gap: 5px;
}

.base-breadcrumb :deep(.el-breadcrumb__inner.is-link svg path) {
    stroke-width: 2px;
}
</style>