<script setup lang="ts">
const props = defineProps<{
	modelValue: string;
    type: TsDigital.UploadFileType
}>();
const uploadRef = useTemplateRef<TsElementPlus.UploadInstance>("uploadRef");
const emits = defineEmits(["update:modelValue"]);
const model = vueuse.useVModel(props, "modelValue", emits);

// 根据props.type确定accept类型和api fileType参数
const fileConfig = computed(() => {
    switch(props.type) {
        case "image":
            return {
                accept: "image/*",
                apiType: "png" // 默认使用png，API支持的图片类型包括png/jpeg/jpg/gif
            };
        case "video":
            return {
                accept: "video/*",
                apiType: "mp4" // 默认使用mp4，API支持的视频类型包括mp4/mov
            };
        case "audio":
            return {
                accept: "audio/*",
                apiType: "mp3" // 默认使用mp3，API支持的音频类型包括wav/mp3
            };
        default:
            return {
                accept: "",
                apiType: "mp4"
            };
    }
});

const fileList = ref<TsElementPlus.UploadUserFile[]>([]);
const previewUrl = ref<string|null>(null);
const currentFile = ref<File|null>(null);
const uploadLoading = ref(false);

// 手动上传文件
const manualUpload = async () => {
    if (!currentFile.value) return;
    uploadLoading.value = true;
    try {
        // 先获取上传地址
        const uploadRequest = apiDigital.uploadFile({fileType: fileConfig.value.apiType});
        const uploadResult = await uploadRequest.send();
        const { temporaryUrl, objectUrl } = uploadResult.data;
        
        // 使用fetch API手动上传文件
        const response = await fetch(temporaryUrl, {
            method: 'PUT',
            headers: {
                'Content-Type': 'application/octet-stream'
            },
            body: currentFile.value
        });
        
        if (response.ok) {
            model.value = objectUrl;
            ElMessage.success('上传成功');
        } else {
            ElMessage.error('上传失败');
        }
    } catch (error) {
        ElMessage.error('上传出错');
        console.error('上传出错:', error);
    } finally {
        uploadLoading.value = false;
    }
};

const uploadChange: TsElementPlus.UploadProps["onChange"] = (uploadFile, uploadFiles) => {
    console.log(uploadFile, uploadFiles);
    // 保存当前文件引用
    if (uploadFile.raw) {
        currentFile.value = uploadFile.raw;
        console.log("当前文件已保存", currentFile.value);
        
        // 生成预览URL
        previewUrl.value = URL.createObjectURL(uploadFile.raw);
    }
    // 更新文件列表
    fileList.value = [...uploadFiles];
};

// 清除预览
const clearPreview = () => {
    if (previewUrl.value) {
        URL.revokeObjectURL(previewUrl.value);
        previewUrl.value = null;
    }
    currentFile.value = null;
    fileList.value = [];
};

// 组件卸载时清理预览URL
onUnmounted(() => {
    clearPreview();
});

// 暴露给模板使用的变量和方法
defineExpose({
    uploadLoading,
    currentFile,
    manualUpload
});
</script>
<template>
	<el-upload
        ref="uploadRef"
        v-model:file-list="fileList"
        :auto-upload="false"
        :show-file-list="true"
        :accept="fileConfig.accept"
        :limit="1"
        :on-change="uploadChange"
        :on-remove="clearPreview"
    >
		<base-button>选择{{ props.type === 'image' ? '图片' : props.type === 'video' ? '视频' : '音频' }}</base-button>
		<template #tip>
			<div class="el-upload__tip">{{ fileConfig.accept }} files with a size less than 500KB.</div>
		</template>
	</el-upload>
    
    <!-- 预览区域 -->
    <div v-if="previewUrl" class="preview-container">
        <!-- 图片预览 -->
        <div v-if="props.type === 'image'" class="preview-image">
            <img :src="previewUrl" alt="预览图片" style="max-width: 100%; max-height: 200px;" />
        </div>

        <!-- 视频预览 -->
        <div v-else-if="props.type === 'video'" class="preview-video">
            <video :src="previewUrl" controls style="max-width: 100%; max-height: 200px;"></video>
        </div>

        <!-- 音频预览 -->
        <div v-else-if="props.type === 'audio'" class="preview-audio">
            <audio :src="previewUrl" controls style="width: 100%;"></audio>
        </div>
    </div>
    
    <!-- 上传按钮 -->
    <div v-if="previewUrl" style="margin-top: 15px;">
        <base-button 
            type="primary" 
            :loading="uploadLoading"
            :disabled="!currentFile || uploadLoading"
            @click="manualUpload"
        >
            {{ uploadLoading ? '上传中...' : '上传文件' }}
        </base-button>
        <base-button 
            style="margin-left: 10px;"
            @click="clearPreview"
        >
            清除
        </base-button>
    </div>
</template>
<style scoped lang="scss">
.preview-container {
    margin-top: 15px;
    display: flex;
    justify-content: center;
    align-items: center;
    min-height: 100px;
    border: 1px dashed #d9d9d9;
    border-radius: 6px;
    padding: 10px;
}
</style>