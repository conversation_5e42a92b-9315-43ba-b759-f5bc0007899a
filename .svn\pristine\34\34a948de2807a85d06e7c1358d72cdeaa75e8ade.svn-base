/* eslint-disable */
// @ts-nocheck
// Generated by unplugin-vue-components
// Read more: https://github.com/vuejs/core/pull/3399
// biome-ignore lint: disable
export {}

/* prettier-ignore */
declare module 'vue' {
  export interface GlobalComponents {
    AudioDurationInside: typeof import('./src/components/dialog/subRechargeComponents/audioDurationInside.vue')['default']
    AutoTooltip: typeof import('./src/components/base/autoTooltip.vue')['default']
    BaseAffix: typeof import('./src/components/base/baseAffix.vue')['default']
    BaseAlert: typeof import('./src/components/base/baseAlert.vue')['default']
    BaseBreadcrumb: typeof import('./src/components/base/baseBreadcrumb.vue')['default']
    BaseButton: typeof import('./src/components/base/baseButton.vue')['default']
    BaseDatePicker: typeof import('./src/components/base/baseDatePicker.vue')['default']
    BaseDialog: typeof import('./src/components/base/baseDialog.vue')['default']
    BaseForm: typeof import('./src/components/base/form/baseForm.vue')['default']
    BaseFormItem: typeof import('./src/components/base/form/baseFormItem.vue')['default']
    BaseIcon: typeof import('./src/components/base/baseIcon.vue')['default']
    BaseInfiniteScroll: typeof import('./src/components/base/baseInfiniteScroll.vue')['default']
    BaseInput: typeof import('./src/components/base/input/baseInput.vue')['default']
    BaseInputNumber: typeof import('./src/components/base/input/baseInputNumber.vue')['default']
    BaseLock: typeof import('./src/components/base/baseLock.vue')['default']
    BaseMenu: typeof import('./src/components/base/menu/baseMenu.vue')['default']
    BaseMenuItem: typeof import('./src/components/base/menu/baseMenuItem.vue')['default']
    BasePagination: typeof import('./src/components/base/basePagination.vue')['default']
    BaseRadio: typeof import('./src/components/base/baseRadio.vue')['default']
    BaseSelect: typeof import('./src/components/base/select/baseSelect.vue')['default']
    BaseShort: typeof import('./src/components/base/baseShort.vue')['default']
    BaseSwitch: typeof import('./src/components/base/baseSwitch.vue')['default']
    BaseTable: typeof import('./src/components/base/table/baseTable.vue')['default']
    BaseTableColumn: typeof import('./src/components/base/table/baseTableColumn.vue')['default']
    BaseTableDate: typeof import('./src/components/base/table/baseTableDate.vue')['default']
    BaseTableDict: typeof import('./src/components/base/table/baseTableDict.vue')['default']
    BaseTableSpecial: typeof import('./src/components/base/table/baseTableSpecial.vue')['default']
    BaseTableSwitch: typeof import('./src/components/base/table/baseTableSwitch.vue')['default']
    BaseTabs: typeof import('./src/components/base/baseTabs.vue')['default']
    BaseTree: typeof import('./src/components/base/baseTree.vue')['default']
    BaseTreeSelect: typeof import('./src/components/base/select/baseTreeSelect.vue')['default']
    BaseVideoPlayer: typeof import('./src/components/base/baseVideoPlayer.vue')['default']
    copy: typeof import('./src/components/pages/diamondConfig/dialogDiamondConfigAdd copy.vue')['default']
    DialogDiamondConfigAdd: typeof import('./src/components/pages/diamondConfig/dialogDiamondConfigAdd.vue')['default']
    DialogDiamondConfigPut: typeof import('./src/components/pages/diamondConfig/dialogDiamondConfigPut.vue')['default']
    DialogRecharge: typeof import('./src/components/dialog/dialogRecharge.vue')['default']
    DialogRechargeScanQrCode: typeof import('./src/components/dialog/dialogRechargeScanQrCode.vue')['default']
    DialogRechargeScanQRCode: typeof import('./src/components/dialog/dialogRechargeScanQRCode.vue')['default']
    DigitalAudioCart: typeof import('./src/components/pages/digital/digitalAudioCart.vue')['default']
    DigitalAudioMarket: typeof import('./src/components/pages/digital/digitalAudioMarket.vue')['default']
    DigitalAudioPart: typeof import('./src/components/pages/digital/digitalAudioPart.vue')['default']
    DigitalAudioPlay: typeof import('./src/components/pages/digital/digitalAudioPlay.vue')['default']
    DigitalBusinessCard: typeof import('./src/components/pages/digital/digitalBusinessCard.vue')['default']
    DigitalModelCard: typeof import('./src/components/pages/digital/digitalModelCard.vue')['default']
    DigitalModelDeatilVideo: typeof import('./src/components/pages/digital/digitalModelDeatilVideo.vue')['default']
    DigitalTemplateMarket: typeof import('./src/components/pages/digital/digitalTemplateMarket.vue')['default']
    DigitalUpload: typeof import('./src/components/pages/digital/digitalUpload.vue')['default']
    DigitalVideoPart: typeof import('./src/components/pages/digital/digitalVideoPart.vue')['default']
    DigitalVideoPlay: typeof import('./src/components/pages/digital/digitalVideoPlay.vue')['default']
    DigitalWorksPart: typeof import('./src/components/pages/digital/digitalWorksPart.vue')['default']
    ElAlert: typeof import('element-plus/es')['ElAlert']
    ElAutoResizer: typeof import('element-plus/es')['ElAutoResizer']
    ElAvatar: typeof import('element-plus/es')['ElAvatar']
    ElBreadcrumb: typeof import('element-plus/es')['ElBreadcrumb']
    ElBreadcrumbItem: typeof import('element-plus/es')['ElBreadcrumbItem']
    ElButton: typeof import('element-plus/es')['ElButton']
    ElCol: typeof import('element-plus/es')['ElCol']
    ElConfigProvider: typeof import('element-plus/es')['ElConfigProvider']
    ElDatePicker: typeof import('element-plus/es')['ElDatePicker']
    ElDialog: typeof import('element-plus/es')['ElDialog']
    ElEmpty: typeof import('element-plus/es')['ElEmpty']
    ElForm: typeof import('element-plus/es')['ElForm']
    ElFormItem: typeof import('element-plus/es')['ElFormItem']
    ElImage: typeof import('element-plus/es')['ElImage']
    ElInput: typeof import('element-plus/es')['ElInput']
    ElInputNumber: typeof import('element-plus/es')['ElInputNumber']
    ElMenu: typeof import('element-plus/es')['ElMenu']
    ElMenuItem: typeof import('element-plus/es')['ElMenuItem']
    ElOption: typeof import('element-plus/es')['ElOption']
    ElPagination: typeof import('element-plus/es')['ElPagination']
    ElPopover: typeof import('element-plus/es')['ElPopover']
    ElRadio: typeof import('element-plus/es')['ElRadio']
    ElRadioButton: typeof import('element-plus/es')['ElRadioButton']
    ElRadioGroup: typeof import('element-plus/es')['ElRadioGroup']
    ElRow: typeof import('element-plus/es')['ElRow']
    ElSegmented: typeof import('element-plus/es')['ElSegmented']
    ElSelect: typeof import('element-plus/es')['ElSelect']
    ElSlider: typeof import('element-plus/es')['ElSlider']
    ElSubMenu: typeof import('element-plus/es')['ElSubMenu']
    ElTable: typeof import('element-plus/es')['ElTable']
    ElTableColumn: typeof import('element-plus/es')['ElTableColumn']
    ElTabPane: typeof import('element-plus/es')['ElTabPane']
    ElTabs: typeof import('element-plus/es')['ElTabs']
    ElTag: typeof import('element-plus/es')['ElTag']
    ElTree: typeof import('element-plus/es')['ElTree']
    ElTreeSelect: typeof import('element-plus/es')['ElTreeSelect']
    GenetalVideoDurationInside: typeof import('./src/components/dialog/subRechargeComponents/genetalVideoDurationInside.vue')['default']
    ImageCloneEInside: typeof import('./src/components/dialog/subRechargeComponents/imageCloneEInside.vue')['default']
    ImageCloneSHdInside: typeof import('./src/components/dialog/subRechargeComponents/imageCloneSHdInside.vue')['default']
    ImageCloneSInside: typeof import('./src/components/dialog/subRechargeComponents/imageCloneSInside.vue')['default']
    LayoutDigital: typeof import('./src/components/layout/layoutDigital.vue')['default']
    LayoutDigitalSub: typeof import('./src/components/layout/layoutDigitalSub.vue')['default']
    LayoutPage: typeof import('./src/components/layout/layoutPage.vue')['default']
    LayoutTable: typeof import('./src/components/layout/layoutTable.vue')['default']
    MyOrderRechargeForm: typeof import('./src/components/pages/my/myOrderRechargeForm.vue')['default']
    MyOrderRechargeTable: typeof import('./src/components/pages/my/myOrderRechargeTable.vue')['default']
    MyOrderSpendForm: typeof import('./src/components/pages/my/myOrderSpendForm.vue')['default']
    MyOrderSpendTable: typeof import('./src/components/pages/my/myOrderSpendTable.vue')['default']
    PointGoodCard: typeof import('./src/components/dialog/subRechargeComponents/pointGoodCard.vue')['default']
    RechargeDiamondInside: typeof import('./src/components/dialog/subRechargeComponents/rechargeDiamondInside.vue')['default']
    RechargePoingInside: typeof import('./src/components/dialog/subRechargeComponents/rechargePoingInside.vue')['default']
    RouterLink: typeof import('vue-router')['RouterLink']
    RouterView: typeof import('vue-router')['RouterView']
    TheBreadcrumb: typeof import('./src/components/the/theBreadcrumb.vue')['default']
    TheHandle: typeof import('./src/components/the/theHandle.vue')['default']
    TheHeader: typeof import('./src/components/the/theHeader.vue')['default']
    TheMenu: typeof import('./src/components/the/theMenu.vue')['default']
    TheMenuDigital: typeof import('./src/components/the/theMenuDigital.vue')['default']
    TheTabs: typeof import('./src/components/the/theTabs.vue')['default']
    TheTreeDept: typeof import('./src/components/the/theTreeDept.vue')['default']
    UserAdd: typeof import('./src/components/pages/user/userAdd.vue')['default']
    UserEdit: typeof import('./src/components/pages/user/userEdit.vue')['default']
    UserResetPassword: typeof import('./src/components/pages/user/userResetPassword.vue')['default']
    VoiceCloneEInside: typeof import('./src/components/dialog/subRechargeComponents/voiceCloneEInside.vue')['default']
    VoiceCloneSInside: typeof import('./src/components/dialog/subRechargeComponents/voiceCloneSInside.vue')['default']
  }
  export interface GlobalDirectives {
    vLoading: typeof import('element-plus/es')['ElLoadingDirective']
  }
}
