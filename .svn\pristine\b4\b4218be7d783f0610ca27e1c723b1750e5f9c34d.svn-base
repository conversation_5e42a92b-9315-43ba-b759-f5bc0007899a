<script setup lang="ts">
const { modelDetail } = pinia.storeToRefs(storePassing());

if (!modelDetail.value) {
    // 单个接口存在则更替为单个接口获取
    ElMessage.error('请从模特市场页面进入');
}

const data = shallowReadonly(modelDetail.value!);

// 视频相关
const videoInfo = shallowRef<Partial<TsModelMarket.VideoType>>();

const changeSence = (sence: TsModelMarket.SenceData) => {
    videoInfo.value = {
        sceneName: sence.sceneName,
        sceneCode: sence.sceneCode,
        proportion: sence.proportion,
        duration: sence.duration,
        videoUrl: sence.exampleUrl ?? undefined,
        coverUrl: sence.coverUrl,
        coverMattingUrl: sence.coverMattingUrl,
        sceneType: sence.sceneType as 0 | 1 | undefined,
    }
}

changeSence(data.sceneList?.[0]!);

// 基础信息
const basicInfo = shallowRef({
    name: data.robotName,
    sex: digitalMarketModel.map.gender.get((data.gender ?? 1)as 1 | 2),
    tag: data.labelBaseDTOList?.map(i => i.labelName) ?? [],
    age: `${data.age}岁`,
    starSign: digitalMarketModel.map.starSign.get(data.starSigns as 1 | 2 | 3 | 4 | 5 | 6 | 7 | 8 | 9 | 10 | 11 | 12),
    desc: data.robotDesc,
    hot: data.popularity ?? 0,
});

// 右侧选择场景区域
// const sceneList = shallowRef<TsModelMarket.SenceData[]>(data.sceneList ?? []);

// const proportion = ref<'all' | 'vertical' | 'horizontal'>('all');
// const proportionList = shallowRef(digitalMarketModel.option.proportion);

// const clone = ref<string>('all');
// const cloneList = shallowRef(digitalMarketModel.option.clone);

const listShow = shallowRef<TsModelMarket.SenceData[]>(data.sceneList);
const listNow = shallowRef<TsModelMarket.SenceData>();

// const searchSet = shallowRef<TsInput.Sets>({
//   placeholder: '搜索场景关键词',
//   prefixIcon: markRaw(IconSolarMagniferLinear),
// })
// const searchValue = ref('');

// 获取是否为竖版
// const getVertical = (i: TsModelMarket.SenceData) => {
//     const [width, height] = i.proportion.split(':').map(i => parseInt(i));
//     return width < height ? 'vertical' : 'horizontal';
// }
const isSearching = ref(false);

// const searchList = (searchInfo: {
//     clone: string,
//     proportion: typeof proportion['value'],
//     searchVal: string,
// }) => new Promise<TsModelMarket.SenceData[]>((res) => {
//     const cloneVal = (() => {
//         if (searchInfo.clone === 'all') {
//             return null;
//         }
//         return searchInfo.clone === 'green_screen';
//     })();

//     const list = sceneList.value.filter(i => {
//         if (cloneVal !== null && !cloneVal && i.sceneType === 0) {
//             return false;
//         }

//         if (searchInfo.proportion !== 'all' && getVertical(i) !== searchInfo.proportion) {
//             return false;
//         }
        
//         return i.sceneName.includes(searchInfo.searchVal);
//     });

//     res(list);
// });

// const search = async () => {
//     isSearching.value = true;
//     listShow.value = await searchList({
//         clone: clone.value,
//         proportion: proportion.value,
//         searchVal: searchValue.value,
//     });
//     isSearching.value = false;
// };

// const searchDebounce = createDebounceFunc(search, 500);

const selectItem = (item: TsModelMarket.SenceData) => {
    listNow.value = item;
    changeSence(item);
}

const closeVideo = () => {
    listNow.value = undefined;
}

// 操作
const gotoEdit = () => {
    router.push({
        name: "ModelEdit",
    })
}
defineExpose({
    name: 'ModelDetail',
})
</script>

<template>
    <div class="detail">
        <main>
            <div class="info">
                <div class="video">
                    <digital-model-deatil-video
                        :video-info="videoInfo!"
                        @close="closeVideo"
                    />
                </div>
                <div class="info">
                    <div class="name-hot">
                        <span class="name">
                            {{ basicInfo.name }}
                            <span class="sex">{{ basicInfo.sex }}</span>
                        </span>
                        <span class="hot">
                            <base-icon class="icon favorite-not-active" icon="solar:fire-bold" />
                            <span>{{ basicInfo.hot }}</span>
                        </span>
                    </div>
                    <div class="tag">
                        <span class="label">标签：</span>
                        <el-tag v-for="item in basicInfo.tag" :key="item">{{ item }}</el-tag>
                        <span v-if="basicInfo.tag.length === 0">暂无</span>
                    </div>
                    <div class="age-starSign">
                        <div class="age">
                            <span class="label">年龄：</span>
                            <span>{{ basicInfo.age }}</span>
                        </div>
                        <div class="starSign">
                            <span class="label">星座：</span>
                            <span>{{ basicInfo.starSign }}</span>
                        </div>
                    </div>
                    <div class="desc">
                        <span class="label">描述：</span>
                        <span>{{ basicInfo.desc }}</span>
                    </div>
                </div>
            </div>
            <div class="operation">
                <div class="top">
                    <div class="left">
                        <h3>模特分身</h3>
                        <!-- <div>
                            <span class="label">
                                分身类型:
                            </span>
                            <base-select
                                class="select"
                                v-model="clone"
                                :options="cloneList"
                                @change="() => search()"
                            />
                        </div>
                        <div>
                            <span class="label">
                                比例:
                            </span>
                            <base-select
                                class="select"
                                v-model="proportion"
                                :options="proportionList"
                                @change="() => search()"
                            />
                        </div> -->
                    </div>
                    <base-button size="large" @click="gotoEdit">去制作</base-button>
                    <!-- <div class="search">
                        <base-input
                            v-model="searchValue"
                            class="search-input"
                            :sets="searchSet"
                            @input="searchDebounce"
                        />
                    </div> -->
                </div>
                <div class="select-container" v-loading="isSearching">
                    <div
                        v-for="item in listShow"
                        class="select-item"
                        :class="{'active': item.id === listNow?.id}"
                        :key="item.id"
                        @click="() => selectItem(item)"
                    >
                        <img
                            :src="item.coverUrl"
                            :alt="item.sceneName"
                        />
                        <span class="name">{{ item.sceneName }}</span>
                    </div>
                </div>
            </div>
        </main>
        <!-- <div class="button-group">
            <base-button size="large" @click="gotoEdit">
                去制作
            </base-button>
        </div> -->
    </div>
</template>

<style lang="scss" scoped>
$fire-color: rgb(255, 79, 79);

.detail {
    height: 100%;

    .button-group {
        display: flex;
        justify-content: flex-end;
        height: 72px;
        background-color: var(--el-bg-color);
        align-items: center;
        border-radius: var(--el-border-radius-middle);
        margin-top: 4vh;
        margin: 0 var(--el-gap);
        padding-right: var(--el-gap);
    }
}

main {
    width: 100%;
    display: flex;
    gap: var(--el-gap);
    height: 100%;
    padding: var(--el-gap);
    box-sizing: border-box;
}

.info {
    flex-basis: 40%;
    background-color: var(--el-bg-color);
    border-radius: var(--el-border-radius-middle);

    .video {
        height: 414px;
        width: 100%;
    }

    .info {
        display: flex;
        margin: var(--el-gap);
        flex-direction: column;
        gap: var(--el-gap-half);

        .name-hot {
            height: 34px;
            line-height: var(--el-font-size-extra-large);
            display: flex;
            justify-content: space-between;

            .name {
                font-size: var(--el-font-size-extra-large);
            }

            .hot {
                display: flex;
                color: $fire-color;
                gap: var(--el-gap-half);
            }
        }

        .age-starSign {
            display: flex;
            gap: var(--el-gap);
        }
    }
}

.operation {
    flex: 1;
    background-color: var(--el-bg-color);
    border-radius: var(--el-border-radius-middle);

    .top {
        height: 32px;
        margin: var(--el-gap);
        display: flex;
        justify-content: space-between;
        align-items: center;

        .left {
            display: flex;
            gap: var(--el-gap);
            .select {
                width: 140px;
            }
        }
    }

    .select-container {
        height: calc(100% - 32px - 3 * var(--el-gap));
        overflow: scroll;
        border-radius: var(--el-border-radius-middle);
        background-color: var(--el-bg-color-page);
        margin: var(--el-gap);
        padding: var(--el-gap);
        display: grid;
        grid-template-columns: repeat(3, 1fr);
        gap: var(--el-gap);

        .select-item {
            height: 180px;
            width: 100%;
            border-radius: var(--el-border-radius-middle);
            background-color: var(--el-bg-color);
            position: relative;
            cursor: pointer;

            .name {
                position: absolute;
                bottom: -20px;
                left: 50%;
                transform: translateX(-50%);
                font-size: var(--el-font-size-small);
            }

            &.active {
                border: 2px solid var(--el-color-primary);
            }
        }
    }
}
</style>
