export default pinia.defineStore("storePassing", {
    state: (): TsStore.Passing => ({
        audioEdit: null,
        cardEdit: null,
        modelEdit: null,
        modelDetail: null,
    }),
    getters: {},
    actions: {},
    // 缓存配置
    persist: {
        // 存储的名称
        key: 'store_passing',
        // 存储到localStorage
        storage: localStorage,
        // 指定状态缓存，不配置则缓存所有状态
        // pick: [],
    }
})