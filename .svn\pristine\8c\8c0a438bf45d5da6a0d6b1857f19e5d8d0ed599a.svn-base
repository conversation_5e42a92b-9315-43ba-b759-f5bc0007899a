const api = {
    submit() {
        const method = (data: TsAudioClone.SubmitData) => Apis.general.post_aivatarapi_clone({ data });

        return alova.useRequest(method, {
            immediate: false,
        })
    },

    getUpload(fileType = 'wav') {
        const method = () => Apis.general.post_aivatarapi_gettemporaryuploadurl({
            data: {
                fileType,
            }
        });

        return alova.useRequest(method, {
            immediate: false,
        })
    },

    upload(file: File, url: string) {
        return new Promise<Response>((res, rej) => {
            fetch(url, {
                method: 'PUT',
                body: file,
                headers: {
                    'Content-Type': file.type,
                }
            }).then(response => {
                // if (response.status === 200) {
                //     console.log(r)
                // }
                console.log(response);
                res(response)
            }).catch(err => {
                rej(err)
            });
        })
    }
}

export default api
