<script setup lang="ts">
type TemplateItem = {
  id: string;
  title: string;
  cover: string;
  videoSrc: string;
  views: number;
};

defineProps<{
  item: TemplateItem;
}>();

const emit = defineEmits<{
  (e: "cardClick", item: TemplateItem): void;
  (e: "useTemplate", item: TemplateItem): void;
}>();

const isCover = ref(false);

const handleCardClick = (item: TemplateItem) => {
  emit("cardClick", item);
};

const handleUse = (item: TemplateItem, event: MouseEvent) => {
  event.stopPropagation();
  emit("useTemplate", item);
};

const video = useTemplateRef<HTMLVideoElement>("videoRef");

const mouseMoveIn = () => {
  isCover.value = true;
  video.value!.play().catch(null);
};

const mouseMoveOut = () => {
  isCover.value = false;
  video.value!.pause();
  lowPriorityFn(() => {
    video.value!.currentTime = 0;
  });
};
</script>

<template>
    <div class="template-card" @click="handleCardClick(item)">
      <div
        class="card-media"
        @mouseenter="() => mouseMoveIn()"
        @mouseleave="() => mouseMoveOut()"
      >
        <img
          v-show="!isCover"
          :alt="item.title ?? ''"
          :src="item.videoSrc"
        />
        <video
          ref="videoRef"
          :poster="item.cover"
          controls
          loop
          muted
          preload="none"
          disablepictureinpicture
        >
          <source :src="item.videoSrc" type="video/mp4" />
        </video>
      </div>
      <div class="card-info">
        <span class="title">{{ item.title }}</span>
        <span class="usage" @click.stop="handleUse(item, $event)">使用</span>
      </div>
      <div class="stats">
        <base-icon icon="uil:fire"></base-icon>
        <span>{{ item.views }}</span>
      </div>
    </div>
</template>

<style scoped lang="scss">
.template-card {
  width: 280px;
  height: 353px;
  background: var(--el-bg-color-page);
  border-radius: 8px;
  /* box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1); */
  cursor: pointer;
  display: flex;
  flex-direction: column;
  align-items: center;
  transition: transform 0.2s ease;
}
.template-card:hover {
  transform: translateY(-4px);
}
.new-card {
  background: var(--el-bg-color-page);
  justify-content: center;
  align-items: center;
}
.card-content {
  text-align: center;
  padding: 24px 0;
}
.plus-icon {
  font-size: 32px;
  color: #999;
  margin-bottom: 8px;
}
.card-media {
  width: 100%;
  height: 350px;
  position: relative;
  overflow: hidden;
  border-top-left-radius: 8px;
  border-top-right-radius: 8px;
}

.card-media video {
  width: 100%;
  height: 100%;
  object-fit: cover;
}
.cover-img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}
.video-mask {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  background: rgba(0, 0, 0, 0.4);
  color: var(--el-text-color-primary);
  padding: 8px 12px;
  border-radius: 4px;
  cursor: pointer;
}
.card-info {
  width: 100%;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 12px;
}
.title {
  font-size: 14px;
  color: var(--el-text-color-primary);
}
.usage {
  color: white;
  background-color: #409eff;
  cursor: pointer;
  font-size: 14px;
  padding: 4px 8px;
  border-radius: 4px;
  transition: background-color 0.3s;
}
.stats {
  font-size: 12px;
  color: #f56c6c;
  padding: 0 12px 8px;
  display: flex;
  align-items: center;
  gap: 4px;
  margin-right: auto; /* 将元素推向左侧 */
}
</style>
