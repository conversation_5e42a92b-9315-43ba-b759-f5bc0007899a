<script setup lang="ts">
const props = defineProps<{
    data: TsMyOrder.Spend.Data[],
    loading: boolean,
}>();

const emit = defineEmits<{
    sort: [params: any];
}>();

const tableData = computed<TsMyOrder.Spend.Data[]>({
    get() {
        return props.data;
    },
    set(newData: TsMyOrder.Spend.Data[]) {
        // 这里可以添加数据更新逻辑，如果需要的话
    }
});

// 处理表格排序变化
function handleSortChange(params: any) {
    emit('sort', params);
}

defineExpose({
    name: 'MyOrderSpendTable',
})
</script>

<template>
    <base-table
        v-loading="loading"
        v-model="tableData"
        :sets="{ border: true }"
        @sort-change="handleSortChange"
    >
        <base-table-column label="订单编号" prop="orderNo" />
        <base-table-column label="订单来源" prop="orderSource" />
        <base-table-column label="商品类别" prop="productCategory" />
        <base-table-column label="商品名称" prop="productName" />
        <base-table-column label="消费额度" prop="consumeAmount" />
        <base-table-column label="订单状态" prop="orderStatus" />
        <base-table-date label="下单时间" prop="orderTime" sortable />
        <base-table-date label="付款时间" prop="paymentTime" sortable />
        <base-table-column label="交易流水号" prop="transactionNo" />
        <base-table-column label="分配账户" prop="assignedAccount" />
        <base-table-column label="操作" prop="actions" />
    </base-table>
</template>

<style lang="scss" scoped>

</style>