<script setup lang="ts">
    const {selectData,sets={},modelValue} = defineProps<{
        modelValue: TsTable.Model;
        selectData?: TsTable.Model;
        sets?: TsTable.Sets<any>;
    }>();
    const isTableIn = ref<boolean>(true);
    provide("isTableIn",isTableIn.value)
    const emits = defineEmits(["update:selectData", "select", "selectAll"]);
    const tableEl = useTemplateRef<InstanceType<typeof ElementPlus.ElTable>>("tableEl");
    const rowKey = (sets.rowKey as string) ?? "id";
    function onSelect(selection: TsTable.Model, row: TsTable.Row) {
        if (selectData) {
            if (selection.length && selection.indexOf(row) >= 0) {
                emits("update:selectData", utilUniqueObj([...selectData, ...selection], rowKey));
            } else {
                emits(
                    "update:selectData",
                    selectData.filter((item) => item[rowKey] != row[rowKey])
                );
            }
        }
        emits("select", selection, row);
    }

    function onSelectAll(selection: TsTable.Model) {
        if (selectData) {
            if (selection.length == 0) {
                emits(
                    "update:selectData",
                    selectData.filter((item) => modelValue.findIndex((i) => i[rowKey] == item[rowKey]) < 0)
                );
            } else {
                emits("update:selectData", utilUniqueObj([...selectData, ...selection], rowKey));
            }
        }
        emits("select", selection);
        emits("selectAll", selection);
    }
    function reSelection() {
        nextTick(() => {
            tableEl.value?.clearSelection();
            if (!tableEl.value || !selectData || selectData.length == 0) return;
            let rows = modelValue.filter((item) => selectData!.findIndex((i) => i[rowKey] == item[rowKey]) >= 0);
            rows.forEach((row) => {
                tableEl.value?.toggleRowSelection(row, undefined);
            });
        });
    }
    defineExpose({
        reSelection,
    })
</script>
<template>
    <el-table
        :data="modelValue"
        stripe
        border
        height="100%"
        ref="tableEl"
        @select="onSelect"
        @select-all="onSelectAll"
        v-bind="sets"
    >
        <slot></slot>
        <template v-if="$slots.empty" #empty="params">
            <slot name="empty" v-bind="params"></slot>
        </template>
    </el-table>
</template>
<style scoped>

</style>