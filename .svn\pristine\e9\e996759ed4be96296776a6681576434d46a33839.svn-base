export default {
    // 数字名片分页列表
    getDigitalCard: () => {
        const method = (page: number, pageSize: number) => Apis.general.post_aivatarapi_getdigitalcard({
            name: "post_aivatarapi_getdigitalcard",
            params: {
                page: page,
                size: pageSize
            },
            transform: (res) => {
                return {
                    data: res.rows ?? [],
                    total: res.total ?? 0,
                };
            }
        })
        return alova.usePagination(method, {
            immediate: true,
            initialPage: 1,
            initialPageSize: 10,
            initialData: {
                rows: [],
                total: 0
            }
        })
    }
}