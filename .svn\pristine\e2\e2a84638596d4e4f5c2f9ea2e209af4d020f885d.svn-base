<script setup lang="ts">
const {sets={}} = defineProps<{
    sets?: TsDialog.Sets
}>()
const emits = defineEmits<{
    cancel: [];
    confirm: []
}>()
// 取消按钮设置
const setsCancel:TsButton.Sets = {
    plain: true,
    type: "info"
}
// 取消
function onCancel() {
    emits("cancel")
}
// 确定
function onConfirm() {
    emits("confirm")
}
</script>
<template>
    <el-dialog v-bind="sets" class="base-dialog">
        <slot></slot>
        <template #footer>
            <div class="dialog-footer">
                <slot name="footer">
                    <base-button @click="onConfirm">确定</base-button>
                    <base-button :sets="setsCancel" @click="onCancel">取消</base-button>
                </slot>
            </div>
        </template>
    </el-dialog>
</template>
<style>
.base-dialog .el-dialog__body:has(.base-form) {
    padding-right: 1em;
}
</style>