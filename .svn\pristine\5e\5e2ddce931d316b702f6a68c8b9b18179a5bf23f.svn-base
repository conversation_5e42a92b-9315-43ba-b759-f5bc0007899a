<script setup lang="ts">
// 配置信息
const { routesDynamic, collapse } = pinia.storeToRefs(storeApp());
const { userType } = pinia.storeToRefs(storeUser());
const options = computed(() => {
	return utilRouteDynamic([routesDynamic.value.find(item => item.name == 'System')!], {forMenu: true, userType: userType.value});
})


const route = useRoute();
const name = computed<TsMenu.Model>(() => route.name as string);
</script>
<template>
	<div class="the-menu">
		<base-menu v-model="name" v-model:collapse="collapse" :options="options" />
	</div>
</template>
<style scoped>
.the-menu {
	height: 100%;
	display: flex;
	flex-direction: column;
}
.base-menu-input {
	flex: 0 0 40px;
}
.base-menu {
	flex: auto;
	overflow: auto;
}
.base-menu-handle {
	flex: 0 0 40px;
}
</style>
