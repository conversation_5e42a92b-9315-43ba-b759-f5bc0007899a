<script setup lang="ts">
defineProps<{
    options: TsSelect.Options;
    sets?: TsSelect.Sets;
}>()
</script>
<template>
    <el-select clearable v-bind="sets" class="base-select">
        <el-option
            v-for="item in options"
            :key="item.value"
            :label="item.label"
            :value="item.value"
            :disabled="item.disabled"
        ></el-option>
    </el-select>
</template>
<style scoped>

</style>