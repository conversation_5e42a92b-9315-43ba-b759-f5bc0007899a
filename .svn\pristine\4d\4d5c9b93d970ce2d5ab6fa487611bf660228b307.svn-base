<script setup lang="ts">
// 表单ref
const formRef = useTemplateRef<TsForm.El>("formRef");

// 表单校验规则
const rules = reactive({
	customType: [{ required: true, message: "请选择定制类型", trigger: "change" }],
	name: [{ required: true, message: "模型名称不能为空", trigger: "blur" }],
	audioUrl: [{ required: true, message: "请上传音频", trigger: "blur" }],
	sex: [{ required: true, message: "请选择性别", trigger: "change" }],
});

// 性别选项
const genderOptions = [
	{ label: "男", value: 1 },
	{ label: "女", value: 2 },
];

// 提交表单
const { form:formData, loading, send } = apiAudioClone.submit();

const handleSubmit = async (e: SubmitEvent) => {
	e.preventDefault();
	if (!formRef.value) return;

	await formRef.value.validate();

	// 构建最终的表单数据
	const submitData: TsAudioClone.SubmitData = {
		name: formData.value.name,
		sex: formData.value.sex as any,
		audioUrl: formData.value.audioUrl || "",
	};

	try {
		await send(submitData);
	} catch (error) {
		ElMessage.error("声音克隆任务提交失败");
		return;
	}

	ElMessage.success("声音克隆任务已提交");
};

// 同意协议
const agreeTerms = ref(false);

// 音频URL计算属性
const audioUrlModel = computed({
	get: () => formData.value.audioUrl || "",
	set: (value) => {
		formData.value.audioUrl = value;
	},
});
</script>

<template>
	<div class="audio-clone-container">
		<base-form ref="formRef" class="form-card" :model-value="formData" :rules="rules" label-position="left" label-width="auto" @submit="handleSubmit">
			<!-- 定制类型 -->
			<base-form-item label="定制类型">
                <span>声音克隆-E级</span>
			</base-form-item>

			<!-- 模型名称 -->
			<base-form-item label="模型名称" prop="name">
				<base-input v-model="formData.name" placeholder="请输入人物模型名称" :maxlength="10" show-word-limit />
			</base-form-item>

			<!-- 性别选择 -->
			<base-form-item label="性别" prop="sex">
				<el-radio-group v-model="formData.sex" class="gender-group">
					<el-radio v-for="option in genderOptions" :key="option.value" :value="option.value">
						{{ option.label }}
					</el-radio>
				</el-radio-group>
			</base-form-item>

			<!-- 音频上传区域 -->
			<base-form-item label="音频" prop="audioUrl">
				<digital-upload v-model="audioUrlModel" type="audio" />
			</base-form-item>

			<!-- 小贴士 -->
			<el-alert title="小贴士" type="warning" :closable="false">
				<template #title>
					<div class="title">
						<base-icon icon="solar:info-circle-outline" />
						<span>小贴士</span>
					</div>
				</template>
				<div>
					<p>1. 录音时上传单条，时长在1-5分钟内；</p>
					<p>2. 录音语料支持16K及以下采样率；</p>
					<!-- <p>3. 录音时文字体现正确性上传，支持zip和rar格式</p> -->
				</div>
			</el-alert>

			<!-- 提交按钮 -->
			<div class="submit-section">
				<base-button type="primary" native-type="submit" size="large" :disabled="!agreeTerms" :loading="loading"> 上传资料 </base-button>

				<!-- 协议同意 -->
				<div class="agreement">
					<el-checkbox v-model="agreeTerms">
						我已阅读并同意
						<a href="#">《声音克隆协议》</a>
					</el-checkbox>
				</div>
			</div>
		</base-form>
	</div>
</template>

<style scoped lang="scss">
.audio-clone-container {
	max-width: 600px;
	margin: 0 auto;
	padding: 20px;
}

.form-card {
	background: var(--el-bg-color);
	border-radius: 8px;
	padding: 24px;
}

.upload-section {
	display: flex;
	gap: 20px;
	align-items: flex-start;
}

.avatar-upload {
	.avatar-placeholder {
		width: 80px;
		height: 80px;
		border: 2px dashed #dcdfe6;
		border-radius: 8px;
		display: flex;
		flex-direction: column;
		align-items: center;
		justify-content: center;
		cursor: pointer;
		transition: border-color 0.3s;

		&:hover {
			border-color: #409eff;
		}

		.avatar-text {
			font-size: 12px;
			color: #999;
			margin-top: 4px;
		}
	}
}

.audio-upload {
	flex: 1;

	.audio-uploader {
		width: 100%;

		:deep(.el-upload) {
			width: 100%;
		}

		:deep(.el-upload-dragger) {
			width: 100%;
			height: 120px;
		}
	}

	.upload-content {
		display: flex;
		flex-direction: column;
		align-items: center;
		justify-content: center;
		height: 100%;

		.upload-text {
			margin-top: 12px;
			text-align: center;

			p {
				margin: 4px 0;

				&.upload-tip {
					font-size: 12px;
					color: var(--el-text-color-placeholder);
				}
			}
		}
	}

	.audio-preview {
		display: flex;
		align-items: center;
		gap: 8px;
		padding: 12px;

		.audio-name {
			flex: 1;
			font-size: 14px;
			color: var(--el-text-color-primary);
		}
	}

	.audio-player {
		width: 100%;
		margin-top: 12px;
	}
}

.gender-group {
	display: flex;
	gap: 16px;
}

.description-input {
	width: 100%;
}

.title {
	display: flex;
	align-items: center;
	gap: 8px;
	font-size: var(--el-font-size-medium);
	margin-bottom: var(--el-gap-half);
}

.submit-section {
	margin-top: var(--el-gap);
	text-align: center;

	.agreement {
		margin-top: 16px;
		font-size: 14px;
		color: #666;
	}
}
</style>
