<script lang="ts" setup>
/**
 * 自动tooltip
 * 当`prop.content`的显示宽度超过容器宽度时，才显示tooltip
 * 宽度限制由该组件父容器决定
 * 不要为父容器设置`overflow: hidden`
 */
import { ElTooltip } from 'element-plus';

defineProps<{
    content: string;
    displayText?: string;
    placement?: 'top' | 'right' | 'bottom' | 'left';
}>();

const isShow = ref(false);

// 检查内容宽度是否超过maxWidth
function checkContentWidth(e: MouseEvent) {
    const div = e.target as HTMLDivElement;
    const span = div.children[0] as HTMLSpanElement;

    if (span.offsetWidth > div.offsetWidth) {
        isShow.value = true;
    }
}

defineExpose({
    name: 'AutoTooltip',
})
</script>

<template>
    <el-tooltip
        :content="content"
        :visible="isShow"
        :placement="placement || 'top'"
    >
        <div 
            class="father"
            @mouseenter="checkContentWidth"
            @mouseleave="isShow = false"
        >
            <span class="son">{{ displayText || content }}</span>
        </div>
    </el-tooltip>
</template>

<style lang="scss" scoped>
.father {
    width: 100%;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
}
</style>
