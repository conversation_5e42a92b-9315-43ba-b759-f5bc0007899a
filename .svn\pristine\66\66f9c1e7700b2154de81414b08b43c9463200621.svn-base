<script lang="ts">
export default defineComponent({
    name: 'PointGoodCard',
    props: {
        point: {
            type: Number,
            required: true
        },
        num: {
            type: Number,
            required: true
        },
        isActive: {
            type: Boolean,
            required: true
        },
        unit: {
            type: String,
            required: false
        },
        isPointRecharge: {
            type: Boolean,
            default: false,
        }
    },
    setup(props) {
        const unit = ref<string>(
            props.unit || inject('unit') || '次'
        )

        return {
            props,
            unit,
        }
    }
});
</script>

<template>
    <div class="recharge-good-crad" :class="{ active: props.isActive }">
        <div class="num">
            <div class="value">{{ props.num }}</div>
            <div v-if="!props.isPointRecharge" class="unit">
                {{ unit }}
            </div>
            <div v-else class="value" style="margin-left: 4px;">
                <base-icon icon="solar:verified-check-line-duotone" />
            </div>
        </div>
        <div class="price" v-if="!isPointRecharge">
            <span class="price-value">{{ props.point }}</span>
            <base-icon icon="solar:verified-check-line-duotone" class="icon" />
        </div>
        <div class="price" v-else>
            <span class="price-value">￥{{ props.point.toFixed(2) }}</span>
        </div>
    </div>
</template>

<style scoped lang="scss">
.recharge-good-crad {
    --background-color: 255, 255, 255;
    background: rgba(var(--background-color), 0.1);
    border-radius: 12px;
    padding: 16px;
    cursor: pointer;
    transition: all 0.3s ease;
    color: #fff;
    border: 2px solid transparent;

    &:hover {
        transform: translateY(-2px);
    }

    &.active {
        border-color: rgba(var(--background-color), 0.2);
    }

    .num {
        display: flex;
        align-items: end;
        width: 100%;
        height: 20px;
        margin-bottom: var(--el-gap-half);

        .value {
            font-size: var(--el-font-size-large);
            font-weight: bold;
            line-height: 20px;
        }

        .unit {
            margin-left: 4px;
            font-size: var(--el-font-size-small);
        }

    }

    .price {
        display: flex;
        align-items: center;
        justify-content: end;
        gap: 4px;
    }

    .price-value {
        font-size: 14px;
        font-weight: bold;
    }

    .icon {
        font-size: 16px;
    }
}

.dark .recharge-good-crad {
    --background-color: 0,0,0;
}
</style>
