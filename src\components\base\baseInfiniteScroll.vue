<script setup lang="ts">
const props = withDefaults(
    defineProps<{
        modelValue: TsInfiniteScroll.Model;
        total: TsInfiniteScroll.Total;
        load: TsInfiniteScroll.Load;
        sets?: TsInfiniteScroll.Sets;
    }>(),
    {
        sets: () => {
            return {}
        }
    }
)
// 组件实例
const elRef = useTemplateRef<HTMLDivElement>("elRef");
// 无限滚动
const {reset, isLoading} = vueuse.useInfiniteScroll(elRef, async () => {
    await props.load();
}, {
    distance: 60,
    canLoadMore: () => props.modelValue.length < props.total
})
defineExpose({
    reset
})
</script>
<template>
    <div class="base-infinite-scroll" ref="elRef">
        <div class="infinite-item" v-for="(item, index) in modelValue" :key="index">
            <slot :item="item"></slot>
        </div>
        <div class="infinite-scroll-tip loading" v-if="isLoading">加载中...</div>
        <div class="infinite-scroll-tip finish" v-else>没有更多了</div>
    </div>
</template>
<style scoped>
.base-infinite-scroll {
    overflow: auto;
    max-height: 100%;
}

.infinite-scroll-tip {
    text-align: center;
    font-size: 14px;
    color: var(--el-text-color-secondary);
    padding: var(--el-gap);
}
</style>