export default pinia.defineStore("storeUser", {
    state: (): TsStore.User => ({
        userId: "", // 用户ID
        userType: "", // 用户类型
        avatar: "", // 头像
        userName: "", // 用户名
        nickName: "", // 昵称
        sex: "", // 性别
        phonenumber: "", // 手机号
        admin: false, // 是否为管理员
        deptId: "", // 部门ID
        deptName: "", // 部门名称
        permissions: [], // 权限
        roles: [], // 角色
        isAdmin: false, // 是否为管理员
        balace: {
            userId: 0,
            diamondBalance: 0,
            videoDuration: 0,
            audioDuration: 0,
            avatarCloneS: 0,
            avatarCloneE: 0,
            voiceCloneE: 0,
        }
    }),
    getters: {},
    actions: {
        updateAccount(isAdmin: boolean, info: TsBalace.Info) {
            this.isAdmin = isAdmin;
            this.balace = info;
        }
    },
    // 缓存配置
    persist: {
        // 存储的名称
        key: 'store_user',
        // 存储到localStorage
        storage: localStorage,
        // 指定状态缓存，不配置则缓存所有状态
        // pick: [],
    }
})