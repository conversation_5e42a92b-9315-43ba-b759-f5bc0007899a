* {
	margin: 0;
	padding: 0;
	list-style: none;
	text-decoration: none;
	outline: none;
	box-sizing: border-box;
	font-family: Inter, 'Helvetica Neue', Helvetica, 'PingFang SC',	'Hiragino Sans GB', 'Microsoft YaHei', '微软雅黑', Arial, sans-serif;
}

html,
body,
#app {
	width: 100%;
	height: 100%;
}

img {
	display: block;
	width: 100%;
	height: 100%;
	object-fit: contain;
}

::-webkit-scrollbar {
	width: var(--el-scroll-width);
	height: var(--el-scroll-width);
	background-color: transparent
}

::-webkit-scrollbar-track-piece,
::-webkit-scrollbar-track,
::-webkit-scrollbar-corner {
	background-color: transparent
}

::-webkit-scrollbar-thumb {
	border-radius: var(--el-scroll-width);
	background-color: var(--el-scroll-color)
}

::-webkit-scrollbar-thumb:hover,
::-webkit-scrollbar-thumb:active {
	background-color: var(--el-scroll-color-active);
}
@font-face {
	font-family: "<PERSON><PERSON> Neue";
	src: url(../../fonts/BebasNeue-Regular.ttf);
}
.el-avatar img {
	pointer-events: none;
}