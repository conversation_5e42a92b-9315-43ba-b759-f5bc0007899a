<script setup lang="ts">
defineProps<{
  option: TsApis.ApiResponseRowsItem<"post_aivatarapi_ttspagelist">
}>()
</script>
<template>
  <div class="digital-audio-part">
    <digital-audio-play :option="{ avatar: 'https://picsum.photos/100/100?random=' + Math.random(), audioSrc: option.ttsUrl }" />
  </div>
</template>
<style lang="scss" scoped>
.digital-audio-part {
  width: 500px;
  display: flex;
  flex-direction: column;
  gap: var(--el-gap-half);
}
</style>