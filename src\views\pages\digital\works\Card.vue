<script setup lang="ts">
const cardList: TsCard.cardItem[] = [
	{
		id: 1,
		name: "马凯文",
		cover: "https://cdn.guiji.cn/video-server/png/1535186932695932930.png",
		videoSrc: "https://cdn.guiji.cn/video-server/mp4/1535186989675552770.mp4",
		views: 999,
	},
	{
		id: 2,
		name: "梁晨",
		cover: "https://cdn.guiji.cn/video-server/png/1530148100795080705.png",
		videoSrc: "https://cdn.guiji.cn/video-server/mp4/1530148143329517569.mp4",
		views: 988,
	},
	{
		id: 3,
		name: "<PERSON>",
		cover: "https://cdn.guiji.cn/video-server/png/1530148335316963329.png",
		videoSrc: "https://cdn.guiji.cn/video-server/mp4/1530148368879783937.mp4",
		views: 970,
	},
	{
		id: 4,
		name: "安培明",
		cover: "https://cdn.guiji.cn/video-server/png/1530147792878641154.png",
		videoSrc: "https://cdn.guiji.cn/video-server/mp4/1530147827915231233.mp4",
		views: 969,
	},
	{
		id: 5,
		name: "宋明",
		cover: "https://cdn.guiji.cn/video-server/png/1530147510417432577.png",
		videoSrc: "https://cdn.guiji.cn/video-server/mp4/1530147567774920706.mp4",
		views: 950,
	},
	{
		id: 6,
		name: "孟庆红",
		cover: "https://cdn.guiji.cn/video-server/png/1530147094381793282.png",
		videoSrc: "https://cdn.guiji.cn/video-server/mp4/1530147143580979202.mp4",
		views: 940,
	},
];
const page = ref(1);
const pageSize = ref(10);
const total = ref(6);
</script>
<template>
	<layout-table>
		<template #table>
			<div class="card-container">
				<digital-audio-cart v-for="option in cardList" :key="option.id" :option="option"></digital-audio-cart>
			</div>
		</template>
		<template #pagination>
			<base-pagination v-model:current-page="page" v-model:page-size="pageSize" :total="total" />
		</template>
	</layout-table>
</template>
<style scoped>
.card-container {
	display: flex;
	flex-wrap: wrap;
	justify-content: flex-start;
}
</style>
