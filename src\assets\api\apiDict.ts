export default {
	// 获取字典
	dict: (params: TsApis.ApiPathParams<"general", "get_system_dict_data_type_dicttype">) => {
		const method = Apis.general.get_system_dict_data_type_dicttype({
			name: "get_system_dict_data_type_dicttype",
			pathParams: params,
			transform: (res) => {
				return {
					options: (res.data || []).map((item) => ({
						label: item.dictLabel || "",
						value: item.dictValue || "",
					})),
					response: res.data || [],
				};
			},
		});
		return alova.useRequest(() => method, {
			immediate: true,
			middleware: alova.actionDelegationMiddleware("get_system_dict_data_type_dicttype"),
			initialData: {
				options: [],
				response: [],
			},
		});
	},

	// 获取字典列表（分页查询）
	getDictList: (params: TsApis.ApiParamsWithoutPage<"general", "get_system_dict_type_list">) => {
		return alova.usePagination(
			(page: number, pageSize: number) =>
				Apis.general.get_system_dict_type_list({
					name: "get_system_dict_type_list",
					params: {
						pageNum: page,
						pageSize,
						...params,
					},
					transform: (res) => {
						return {
							total: res.total || 0,
							rows: res.rows || [],
							code: res.code,
							msg: res.msg,
						};
					},
				}),
			{
				watchingStates: [],
				initialPage: 1,
				initialPageSize: 10,
				initialData: {
					total: 0,
					rows: [],
					code: 200,
					msg: "",
				},
				middleware: alova.actionDelegationMiddleware("get_system_dict_type_list"),
			}
		);
	},

	// 新增字典
	addDict: () => {
		const methodRequest = alova.useForm(
			(data: TsApis.ApiData<"general", "post_system_dict_type">) =>
				Apis.general.post_system_dict_type({
					name: "system_dict_type",
					data,
				}),
			{
				immediate: false,
				middleware: alova.actionDelegationMiddleware("post_system_dict_type"),
				initialForm: {
					dictName: "",
					dictType: "",
					status: "",
					remark: "",
				},
			}
		);

		const { onSuccess, onError } = methodRequest;
		onSuccess((res) => {
			if (res.data.code === 200) {
				ElMessage.success(res.data.msg || "新增成功");
				alova.accessAction("get_system_dict_type_list", (api) => api.refresh());
			}
		});
		onError((err) => {
			ElMessage.error(err.error.msg || "新增失败");
		});

		return methodRequest;
	},

	// 修改字典
	updateDict: () => {
		const methodRequest = alova.useForm(
			(data: TsApis.ApiData<"general", "put_system_dict_type">) =>
				Apis.general.put_system_dict_type({
					data,
					transform: (res) => res,
				}),
			{
				immediate: false,
				middleware: alova.actionDelegationMiddleware("put_system_dict_type"),
				initialForm: {
					dictId: 0,
					dictName: "",
					dictType: "",
					status: "0",
					remark: "",
				},
			}
		);

		const { onSuccess, onError } = methodRequest;
		onSuccess((res) => {
			if (res.data.code === 200) {
				ElMessage.success(res.data.msg || "修改成功");
				alova.accessAction("get_system_dict_type_list", (api) => api.refresh());
			}
		});
		onError((err) => {
			ElMessage.error(err.error.msg || "修改失败");
		});

		return methodRequest;
	},

	// 删除字典
	deleteDict: () => {
		const methodRequest = alova.useRequest(
			(dictId: number) =>
				Apis.general.delete_system_dict_type_dictid({
					name: "delete_system_dict_type_dictid",
					pathParams: { dictId },
				}),
			{
				immediate: false,
				middleware: alova.actionDelegationMiddleware("delete_system_dict_type_dictid"),
			}
		);

		const { onSuccess, onError } = methodRequest;
		onSuccess((res) => {
			if (res.data.code === 200) {
				ElMessage.success(res.data.msg || "删除成功");
				alova.accessAction("get_system_dict_type_list", (api) => api.refresh());
			}
		});
		onError((err) => {
			ElMessage.error(err.error.msg || "删除失败");
		});

		return methodRequest;
	},

	// 获取字典详情
	getDictDetail: () => {
		return alova.useRequest(
			(dictId: number) =>
				Apis.general.get_system_dict_type_dictid({
					pathParams: { dictId },
					transform: (res) => res.data || {},
				}),
			{
				immediate: false
			}
		);
	}
};
