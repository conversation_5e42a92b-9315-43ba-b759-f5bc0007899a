<script setup lang="ts">
const {modelValue,sets = {}} = defineProps<{
    modelValue: TsForm.Model;
    sets?: TsForm.Sets;
}>()
provide("modelValue", modelValue);
provide("setsForm",sets);
const setsForm = computed(() => {
    const res = JSON.parse(JSON.stringify(sets));
    if(res.labelPosition === "inner") delete res.labelPosition;
    return res;
})
const formEL = useTemplateRef<InstanceType<typeof ElementPlus.ElForm>>("formEl");
function resetFields() {
    if(!formEL.value) return;
    formEL.value.resetFields();
}
function validate() {
    return new Promise(function(resolve,reject) {
        if (!formEL.value) return;
        formEL.value.validate((valid:boolean, fields: any) => {
            if (valid) {
                resolve(true);
            } else {
                if(sets && sets.errorAlert != false) {
                    for (const key in fields) {
                        const item = fields[key][0];
                        ElMessage({
                            type: "error",
                            message: item.message,
                        });
                    }
                }
                reject(fields);
            }
        })
    })
}
function validateField(params: string) {
    return new Promise(function(resolve,reject) {
        if (!formEL.value) return;
        formEL.value.validateField(params,(valid:boolean, fields: any) => {
            if (valid) {
                resolve(true);
            } else {
                reject(fields[params]);
            }
        })
    })
}
function clearValidate() {
    if(!formEL.value) return;
    formEL.value.clearValidate();
}
defineExpose({
    resetFields,
    validate,
    validateField,
    clearValidate,
})
</script>
<template>
    <el-form :model="modelValue" v-bind="setsForm" class="base-form" ref="formEl">
        <slot></slot>
    </el-form>
</template>
<style scoped>
.base-form.el-form--inline :deep(.el-form-item .el-form-item__content) {
    width: 220px;
}
</style>