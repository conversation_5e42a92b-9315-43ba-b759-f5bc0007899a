<script lang="ts" setup>
const { name, shortType: originShortType = undefined } = defineProps<{
  name: string
  shortType: TsBaseShortTypes.SortType
}>();
const emits = defineEmits<{
  'update:shortType': [val: TsBaseShortTypes.SortType, name: string];
}>();

const shortType = ref<TsBaseShortTypes.SortType>(originShortType);

const handleSortChange = (type: TsBaseShortTypes.SortType) => {
  if (type === shortType.value) {
    shortType.value = undefined;
  } else {
    shortType.value = type;
  }
  emits('update:shortType', shortType.value, name);
};
</script>

<template>
  <div class="base-short">
    <span class="name">{{ name }}</span>
    <div class="icon">
      <base-icon 
        icon="solar:alt-arrow-up-bold"
        :class="{ active: shortType === 'asc' }"
        @click="handleSortChange('asc')"
      />
      <base-icon 
        icon="solar:alt-arrow-down-bold"
        :class="{ active: shortType === 'desc' }"
        @click="handleSortChange('desc')"
      />
    </div>
  </div>
</template>

<style lang="scss" scoped>
.base-short {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 100%;
  font-size: 14px;
  white-space: nowrap;
  color: var(--el-text-color-primary);

  .icon {
    display: flex;
    flex-direction: column;
    margin-left: 4px;
    color: var(--el-text-color-placeholde);
    cursor: pointer;

    .active {
      color: var(--el-color-primary);
    }
  }
}
</style>
