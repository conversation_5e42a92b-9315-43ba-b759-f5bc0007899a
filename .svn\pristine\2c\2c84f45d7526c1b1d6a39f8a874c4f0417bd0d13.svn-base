<script setup lang="ts">
import * as FAKE from './fake';

// 整体
const isRechage = ref(true);
const loading = ref(false);
let name = 'first';
const activeName = computed({
  get() {
    return name;
  },
  set(val) {
    isRechage.value = val === 'first';
    name = val;
  }
});

// 充值记录
const orderData = shallowRef<TsMyOrder.Recharge.Data[]>([]);

// 排序参数
interface SortParams {
  prop: string;
  order: 'ascending' | 'descending' | null;
}

const sortParams = ref<SortParams | null>(null);

// 获取订单列表
function getOrderList(params: Partial<TsMyOrder.Recharge.FormData> = {}) {
  loading.value = true;
  // 模拟API请求
  setTimeout(() => {
    let data = [...FAKE.mockDataLog];
    
    // 如果有排序参数，进行排序
    if (sortParams.value && sortParams.value.prop) {
      const { prop, order } = sortParams.value;
      if (order) {
        data.sort((a, b) => {
          const valueA = a[prop];
          const valueB = b[prop];
          // 升序
          if (order === 'ascending') {
            return valueA < valueB ? -1 : valueA > valueB ? 1 : 0;
          }
          // 降序
          return valueA > valueB ? -1 : valueA < valueB ? 1 : 0;
        });
      }
    }
    
    orderData.value = data;
    total.value = data.length;
    loading.value = false;
  }, 500);
}

// 搜索处理
function handleSearch(formData: TsMyOrder.Recharge.FormData) {
  getOrderList(formData);
}

// 重置处理
function handleReset() {
  getOrderList();
}

// 处理表格排序
function handleSort(params: SortParams) {
  sortParams.value = params;
  getOrderList();
}

// 

// 分页相关
const page = computed({
  get() {
    return isRechage.value ? 1 : 1;
  },
  set(val) {
    if (isRechage.value) {
      null
    } else {
      null
    }
  }
});
const pageSize = computed({
  get() {
    return isRechage.value ? 10 : 10;
  },
  set(val) {
    if (isRechage.value) {
      null
    } else {
      null
    }
  }
});
const total = computed({
  get() {
    return isRechage.value ? 1 : 1;
  },
  set(val) {
    if (isRechage.value) {
      null
    } else {
      null
    }
  }
});

defineExpose({
    name: 'MyOrder',
})
</script>

<template>
	<layout-table>
        <template #static>
            <div class="balance">
                占位符
            </div>
        </template>
        <template #form>
            <el-tabs v-model="activeName" type="card" class="tabs">
              <el-tab-pane label="充值详情" name="first">
                <my-order-recharge-form 
                    :loading="loading" 
                    @search="handleSearch" 
                    @reset="handleReset"
                />
              </el-tab-pane>
              <el-tab-pane label="消费详情" name="second">

              </el-tab-pane>
            </el-tabs>
        </template>
        <template #table>
            <my-order-recharge-table 
                :data="orderData" 
                :loading="loading"
                @sort="handleSort" 
            />
        </template>
        <template #pagination>
            <base-pagination 
                v-model:current-page="page" 
                v-model:page-size="pageSize" 
                :total="total"
            />
        </template>
	</layout-table>
</template>
<style scoped lang="scss">
.balance {
    height: 96px;
    margin-bottom: var(--el-gap-half);
    background: #856;
}

.tabs {
  padding: var(--el-gap);
}

:deep(.layout-form .part) {
  padding: 0 !important;
}
</style>
