declare namespace TsDigital {
    type State = 1 | 2 | 3;
    type UploadFileType = "image" | "video" | "audio";
    interface VideoOption {
        id: string;
        img: string;
        state: State;
        title: string;
        createTime: string;
    }
    interface VideoForm {
        title: string;
        state: State | "";
    }
    interface AudioOption {
        id: string;
        img: string;
        title: string;
        state: State;
        createTime: string;
    }
    interface AudioMarketOption {
        id: string | number
        avatar: string
        name: string
        rating: number
        tags?: string[]
    }
    interface AudioOption {
        id: string
        avatar: string
        name: string
        rating: number
    }
}