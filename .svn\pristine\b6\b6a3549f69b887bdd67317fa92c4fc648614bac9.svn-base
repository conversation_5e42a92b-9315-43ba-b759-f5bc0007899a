const api = {
    getList(params: TsApis.ApiParamsWithoutPage<"general", "get_diamond_config_list">) {
        const method = (page: number, size: number) => Apis.general.get_diamond_config_list({
            name: "get_diamond_config_list",
            params: {
                ...params,
                pageNum: page.toString(10),
                pageSize: size.toString(10),
            },
            transform: (res) => {
                return {
                    data: res.rows.map(item => ({
                        ...item,
                        createTime: item.createTime ? new Date(item.createTime) : null,
                        updateTime: item.updateTime ? new Date(item.updateTime) : null,
                        amount: (item.amount || 0).toFixed(2),
                    })),
                    total: res.total,
                }
            },
            action: {
                remove: this.remove,
            },
            hitSource: ['delete_diamond_config_id', 'post_diamond_config_add', 'put_diamond_config_id'],
        });

        return alova.usePagination(method, {
			watchingStates: [params],
			debounce: [300],
			initialPage: 1,
			initialPageSize: 10,
			immediate: true,
            preloadNextPage: true,
			middleware: alova.actionDelegationMiddleware("get_system_user_list"),
			initialData: {
				total: 0,
				data: [],
			},
		});
    },

    async remove (row: unknown) {
        const data = row as TsDiamondConfig.Data['rows'][number];

        await Apis.general.delete_diamond_config_id({
            name: "delete_diamond_config_id",
            pathParams: {
                id: data.id.toString(),
            },
        });
    },

    get(id: Ref<number>) {
        const method = () => Apis.general.get_diamond_config_id({
            name: "get_diamond_config_id",
            pathParams: {
                id: id.value,
            },
        });

        return alova.useRequest(method, {
            immediate: true,
            middleware: alova.actionDelegationMiddleware("get_diamond_config_id"),
            initialData: {
                code: 0,
                msg: "",
                data: {
                     createBy: null,
                    createTime: '',
                    updateBy: null,
                    updateTime: '',
                    remark: null,
                    id: 0,
                    diamondCount: 0,
                    amount: 0,
                    status: 1   
                } as TsDiamondConfig.Data['rows'][number],
            },
        });
    },

    // 新增钻石配置
    add: () => {
        const methodRequest = alova.useForm(
            (data: TsApis.ApiData<"general", "post_diamond_config_add">) =>
                Apis.general.post_diamond_config_add({
                    name: "post_diamond_config_add",
                    data,
                }),
            {
                resetAfterSubmiting: true,
                middleware: alova.actionDelegationMiddleware("post_diamond_config_add"),
                initialForm: {
                     diamondCount: 0,
                     amount: "0",
                 },
            }
        );

        const { onSuccess, onError } = methodRequest;
        onSuccess(() => {
             ElMessage.success("添加钻石配置成功");
             alova.accessAction("get_diamond_config_list", (api) => api.refresh());
         });

        onError((error) => {
            ElMessage.error(error.error.msg || "添加钻石配置失败");
        });

        return methodRequest;
    },

    // 更新钻石配置
    update: () => {
        const methodRequest = alova.useForm(
            (data: TsApis.ApiData<"general", "put_diamond_config_id">) =>
                Apis.general.put_diamond_config_id({
                    name: "put_diamond_config_id",
                    pathParams: {
                        id: data.id.toString(),
                    },
                    data,
                }),
            {
                immediate: false,
                middleware: alova.actionDelegationMiddleware("put_diamond_config_id"),
                initialForm: {
                     id: 0,
                     diamondCount: 0,
                     amount: 0,
                     status: 0,
                 },
            }
        );

        const { onSuccess, onError } = methodRequest;
        onSuccess(() => {
             ElMessage.success("更新钻石配置成功");
             alova.accessAction("get_diamond_config_list", (api) => api.refresh());
         });

        onError((error) => {
            ElMessage.error(error.error.msg || "更新钻石配置失败");
        });

        return methodRequest;
    },

    // 删除钻石配置
    delete: (id: Ref<number>) => {
        const methodRequest = alova.useRequest(
            () =>
                Apis.general.delete_diamond_config_id({
                    name: "delete_diamond_config_id",
                    pathParams: { id: id.value.toString() },
                }),
            {
                immediate: false,
                middleware: alova.actionDelegationMiddleware("delete_diamond_config_id"),
                initialData: {
                    code: 0,
                    msg: "",
                },
            }
        );

        const { onSuccess, onError } = methodRequest;
        onSuccess(() => {
             ElMessage.success("删除钻石配置成功");
             alova.accessAction("get_diamond_config_list", (api) => api.refresh());
         });

        onError((error) => {
            ElMessage.error(error.error.msg || "删除钻石配置失败");
        });

        return methodRequest;
    },
} as const;

Object.freeze(api);

export default api;
