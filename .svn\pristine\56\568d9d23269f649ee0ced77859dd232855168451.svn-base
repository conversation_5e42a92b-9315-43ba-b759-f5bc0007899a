namespace TsStore {
    interface Tab {
        label: string;
        name: string;
        icon?: string;
    }
    interface User {
        userId: number | "";
        avatar: string;
        userName: string;
        userType: "00" | "01" | "";
        nickName: string;
        sex: string;
        phonenumber: number | "";
        admin: boolean;
        deptId: number | "";
        deptName: string;
        permissions: string[];
        roles: string[];
    }
    interface RouteDynamic {
        path: string;
        name: string;
        redirect?: string;
        viewPath: string;
        meta: {
            label: string;
            icon?: string;
            keepAlive?: boolean;
            noTab?: boolean;
            noMenu?: boolean;
            isDetail?: boolean;
        },
        children?: RouteDynamic[];
    }
    interface RouteInfo {
        params: RouteParamsRawGeneric | null;
        query: LocationQueryRaw  | null;
    }
    interface App {
        token: string;
        tabs: Tab[];
        tabsShowIcon: boolean;
        keepAlive: string[];
        collapse: boolean;
        theme: "light" | "dark";
        isLock: boolean;
        routesDynamic: RouteDynamic[];
        breadcrumbShowIcon: boolean;
        routeInfo: Record<string, RouteInfo>;
    }
    interface Passing {
        audioEdit: TsApis.ApiResponseRowsItem<"post_aivatarapi_speakerlist"> | null;
        cardEdit: TsApis.ApiResponseRowsItem<"post_aivatarapi_getdigitalcard"> | null;
        modelEdit: TsApis.ApiResponseRowsItem<"post_aivatarapi_robotlist"> | null;
        modelDetail: TsApis.ApiResponseRowsItem<"post_aivatarapi_robotlist"> | null;
    }
}