<script setup lang="ts">
const router = useRouter();
const { modelDetail, modelEdit } = pinia.storeToRefs(storePassing());
// 获取列表数据
const { loading, data, page, pageSize, total } = apiDigitalModel.getMarketList();
// 点击使用
function clickUse (item: TsApis.ApiResponse<"general", "post_aivatarapi_robotlist">["rows"][number]) {
	modelEdit.value = item;
	router.push({
		name: "ModelEdit",
	});
}
// 点击详情
function clickDetail(data: TsModelMarket.ModleData) {	
	modelDetail.value = data;
	router.push({
		name: "ModelDetail",
		params: {
			id: data.id,
		}
	});
}
</script>
<template>
	<layout-table>
		<template #table>
			<div style="height:100%;" v-loading="loading" element-loading-text="加载中...">
				<div class="grid-container" v-if="data.length > 0">
					<digital-model-card v-for="item in data" :key="item.id" :data="item" @click-use="clickUse" @click-detail="clickDetail" />
				</div>
				<el-empty v-else />
			</div>
		</template>
		<template #pagination>
			<base-pagination v-model:current-page="page" v-model:page-size="pageSize" :total="total" />
		</template>
	</layout-table>
</template>
<style scoped lang="scss">
.market-container {
	height: 100%;
	display: flex;
	flex-direction: column;
	background: var(--el-bg-color);
}

.market-header {
	display: flex;
	align-items: center;
	justify-content: space-between;
	padding: 0 var(--el-gap);
	.category-tabs {
		flex-basis: auto;
		--el-border-color-light: var(--el-bg-color);

		:deep(.el-tabs__header) {
			margin: 0;
		}
	}

	.header-actions {
		display: flex;
		gap: var(--el-gap-half);
		align-items: center;
		width: 400px;

		.sort {
			display: flex;
			gap: var(--el-gap-half);
			width: min-content;
		}
	}

	.search-input {
		flex: 1;
	}
}

.grid-container {
	min-height: 100%;
	display: grid;
	grid-template-columns: repeat(auto-fill, 200px);
	grid-auto-rows: 354px;
	row-gap: var(--el-gap);
	column-gap: 25px;
	overflow-y: auto;
	flex: 1;
	padding: var(--el-gap);
}

.pagination-container {
	padding: var(--el-gap-half);
	display: flex;
	justify-content: center;
	border-top: 1px solid var(--el-border-color);
}
</style>
