<script lang="ts" setup>
// 表达设置
const setsForm: TsForm.Sets = {
	labelWidth: "6em",
};
// 视频名称设置
const setsName: TsInput.Sets = {
	placeholder: "请输入视频名称",
};
// 讲解内容设置
const setsTextarea: TsInput.Sets = {
	type: "textarea",
	rows: 7,
	maxlength: 20000,
	showWordLimit: true,
	placeholder: "请输入讲解内容",
};
// 表单实例
const formRef = useTemplateRef<TsForm.El>("formRef");
// 表单验证
const rules: TsElementPlus.FormRules = {
	videoName: [{ required: true, message: "请输入视频名称", trigger: "blur" }],
	text: [{ required: true, message: "请输入讲解内容", trigger: "blur" }],
};
// 传值对象
const { modelEdit } = pinia.storeToRefs(storePassing());
// 接口数据
const { form, loading, send } = apiDigitalModel.synthesizeVideo();
// 场景激活
const sceneActive = ref<number>(0);
// 表单赋值
function getIds() {
	form.value.speakerId = modelEdit.value!.speakerId;
	form.value.sceneId = modelEdit.value!.sceneList[sceneActive.value].id.toString();
}
getIds();
// 提交按钮设置
const setsSend = computed(
	(): TsButton.Sets => ({
		disabled: !form.value.text,
		loading: loading.value,
		type: "success",
	})
);
// 视频地址
const videoUrl = computed((): string => {
	return modelEdit.value?.sceneList?.[sceneActive.value]?.exampleUrl ?? "";
});
// 场景选择
function onScene(index: number) {
	sceneActive.value = index;
	getIds();
}
// 创建
function onCreate() {
	formRef.value.validate().then(() => {
		send();
	});
}
</script>
<template>
	<div class="container">
		<div class="show-cont">
			<div class="scene-list">
				<div class="scene-title">场景列表</div>
				<div class="scene-cont">
					<div :class="{ 'scene-item': true, active: index == sceneActive }" v-for="(item, index) in modelEdit?.sceneList" :key="item.id" @click="onScene(index)">
						<img :src="item.coverUrl" alt="" />
						<div class="scene-name">{{ item.sceneName }}</div>
					</div>
				</div>
			</div>
			<div class="image-section">
				<div class="image-wrapper">
					<video v-if="videoUrl" :src="videoUrl" class="vertical-video" controls />
					<img v-else :src="modelEdit?.coverUrl" class="vertical-img" />
				</div>
			</div>
		</div>
		<!-- 文本域区域 -->
		<div class="textarea-section">
			<base-form v-model="form" :sets="setsForm" :rules="rules" ref="formRef">
				<base-form-item label="视频名称" prop="videoName">
					<base-input v-model="form.videoName" :sets="setsName" />
				</base-form-item>
				<base-form-item label="讲解内容" prop="text">
					<base-input v-model="form.text" :sets="setsTextarea" />
				</base-form-item>
				<base-form-item label=" ">
					<base-button :sets="setsSend" @click="onCreate"> AI合成 </base-button>
				</base-form-item>
			</base-form>
		</div>
	</div>
</template>

<style scoped>
.container {
	height: 100%;
	display: flex;
	flex-direction: column;
	gap: var(--el-gap);
	align-items: center;
	padding: var(--el-gap);
	width: 100%;
	background-color: var(--el-bg-color);
	border-radius: var(--el-border-radius-middle);
	overflow: auto;

	.show-cont {
		display: flex;
		gap: var(--el-gap);
		width: 100%;
		height: 500px;
		overflow: hidden;

		.scene-list {
			width: 200px;
			background-color: var(--el-bg-color-page);
			display: flex;
			flex-direction: column;
			height: 100%;

			.scene-title {
				padding: var(--el-gap-half);
				border-bottom: 1px solid var(--el-border-color);
			}

			.scene-cont {
				flex: 1;
				overflow: auto;

				.scene-item {
					margin: var(--el-gap) auto;
					width: 75%;
					height: fit-content;
					border-radius: var(--el-border-radius-base);
					overflow: hidden;
					background-color: var(--el-bg-color);
					cursor: pointer;

					.scene-name {
						font-size: var(--el-font-size-base);
						text-align: center;
						padding: var(--el-gap-half) 0;
					}

					img {
						display: block;
						width: 100%;
					}

					&.active {
						border: 1px solid var(--el-color-primary);
						box-shadow: var(--el-box-shadow);
					}
				}
			}
		}
	}

	.image-section {
		width: 100%;
		display: flex;
		flex-direction: column;
		align-items: center;
		background-color: var(--el-bg-color-page);
		padding: var(--el-gap);

		.image-wrapper {
			width: 240px;
			height: 420px;
			overflow: hidden;
			display: flex;
			justify-content: center;
			align-items: center;
			background-color: var(--el-bg-color);
			border-radius: var(--el-border-radius-base);

			.vertical-img {
				width: 100%;
				height: 100%;
				object-fit: cover;
				transition: object-fit 0.3s ease;
			}

			.vertical-video {
				width: 100%;
				height: 100%;
				object-fit: cover;
			}
		}
	}

	.textarea-section {
		width: 100%;
		margin-bottom: 20px;
	}
}
</style>
