#!/usr/bin/env fish

# SVN删除脚本 - 删除所有状态为!的文件
# 使用方法: ./svnDelete.fish

function svn_delete_missing
    # 获取所有状态为!的文件
    set missing_files (svn st | grep '^!' | awk '{$1=""; print substr($0,2)}')
    
    if test (count $missing_files) -eq 0
        echo "没有发现状态为!的文件"
        return 0
    end
    
    echo "发现以下状态为!的文件:"
    for file in $missing_files
        echo "  $file"
    end
    
    echo ""
    echo "总共 "(count $missing_files)" 个文件将被从SVN中删除"
    echo ""
    
    # 确认删除
    read -l -P "确认删除这些文件吗? (y/N): " confirm
    
    switch $confirm
        case y Y yes YES
            echo "开始删除..."
            for file in $missing_files
                echo "删除: $file"
                svn rm "$file"
            end
            echo "删除完成!"
            echo ""
            echo "请运行 'svn commit' 提交更改"
        case '*'
            echo "操作已取消"
            return 1
    end
end

# 检查是否在SVN工作目录中
if not test -d .svn
    echo "错误: 当前目录不是SVN工作目录"
    exit 1
end

# 执行删除函数
svn_delete_missing