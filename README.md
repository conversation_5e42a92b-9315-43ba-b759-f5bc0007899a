# Vue 3 + TypeScript + Vite

This template should help get you started developing with Vue 3 and TypeScript in Vite. The template uses Vue 3 `<script setup>` SFCs, check out the [script setup docs](https://v3.vuejs.org/api/sfc-script-setup.html#sfc-script-setup) to learn more.

Learn more about the recommended Project Setup and IDE Support in the [Vue Docs TypeScript Guide](https://vuejs.org/guide/typescript/overview.html#project-setup).

# 命名规范
## 页面
- 目录小写字母开头，驼峰命名法
- 页面文件名以大写字母开头，采用驼峰命名法，例：Demo.vue或DemoTest.vue
## 组件
- 小写字母开头，驼峰命名法
## 页面/页面组件TS类型
- 写在`/assets/types/views/pages/`
- 所有页面的ts类型写在`@/assets/types/views/pages/`文件夹内，每个页面对应一个ts文件
- ts文件命名规则：页面名称.d.ts
## API
- API接口统一写在 `src/assets/api/` 文件夹内，每个页面对应一个API文件
- API接口文件命名规则：以api开头拼接页面名称，采用驼峰命名法，例：页面名称为Demo.vue，则API接口文件为：apiDemo.ts