// 获取当前地址并去除指定参数
export function utilUrlRemoveParam(param: string) {
  const cleanUrl = window.location.href.split("?")[0];
  const params = new URLSearchParams(window.location.search);
  params.delete(param);
  const newUrl = `${cleanUrl}?${params.toString()}`;
  return encodeURIComponent(newUrl);
}
// 获取当前url
export function utilCurrUrl() {
  const cleanUrl = window.location.href.split("?")[0];
  const params = new URLSearchParams(window.location.search);
  const newUrl = `${cleanUrl}?${params.toString()}`;
  // 对URL进行编码，防止特殊字符导致的问题
  return encodeURIComponent(newUrl);
}
// 页面加载完成
export function utilMountedReady() {
  const isReady = ref(false);
  onMounted(() => {
    isReady.value = true;
  });
  onActivated(() => {
    isReady.value = true;
  });
  onDeactivated(() => {
    isReady.value = false;
  });
  return {
    isReady,
  };
}
// 路由数据转换
export function utilRouteDynamic(
  routes: TsStore.RouteDynamic[],
  config: {
    forMenu?: boolean;
    userType?: TsStore.User["userType"];
  }
): TsVueRouter.RouteRecordRaw[] {
  const views = import.meta.glob("@/views/**/**.vue");
  const layout = import.meta.glob("@/components/layout/**.vue");
  const modules = {
    ...views,
    ...layout,
  };
  const list: TsVueRouter.RouteRecordRaw[] = [];
  routes.map((item) => {
    const { viewPath, children, ...other } = item;
    let pass = true;
    if(config.forMenu && item.meta.isDetail) pass = false;
    if(pass && config.userType && item.meta.permissions && !item.meta.permissions.includes(config.userType)) pass = false;
    if(pass) {
      list.push({
        ...other,
        component: modules["/src" + viewPath],
        children: children ? utilRouteDynamic(children, config) : [],
      });
    }
  });
  return list;
}

/**
 * 主题切换
 * @val 值
 * @icon 当前icon
 * @toggle 切换方法
 * */
export function utilThemeToggle() {
  const val = ref(false);
  let isDark = vueuse.useDark({
    disableTransition: false,
    // 存储到localStorage/sessionStorage中的Key 根据自己的需求更改
    storageKey: "themeKey",
    // 暗黑class名字
    valueDark: "dark",
    // 高亮class名字
    valueLight: "light",
  });
  val.value = isDark.value;
  const onToggle = vueuse.useToggle(isDark);
  const { x, y } = vueuse.useMouse();
  const iconLight = "solar:sun-fog-bold";
  const iconDark = "solar:moon-fog-bold";
  const icon = computed(() => (val.value ? iconLight : iconDark));
  function toggle() {
    val.value = !val.value;
    const doc = document as unknown as any;
    //在不支持的浏览器里不做动画
    if (!doc.startViewTransition) {
      onToggle();
    } else {
      // 开始一次视图过渡：
      const transition = doc.startViewTransition(() => onToggle());
      transition.ready.then(() => {
        //计算按钮到最远点的距离用作裁剪圆形的半径
        const endRadius = Math.hypot(
          Math.max(x.value, innerWidth - x.value),
          Math.max(y.value, innerHeight - y.value)
        );
        const clipPath = [
          `circle(0px at ${x.value}px ${y.value}px)`,
          `circle(${endRadius}px at ${x.value}px ${y.value}px)`,
        ];
        //开始动画
        doc.documentElement.animate(
          {
            clipPath: isDark.value ? [...clipPath].reverse() : clipPath,
          },
          {
            duration: 400,
            easing: "ease-in",
            pseudoElement: isDark.value
              ? "::view-transition-old(root)"
              : "::view-transition-new(root)",
          }
        );
      });
    }
  }
  return {
    val,
    icon,
    iconLight,
    iconDark,
    toggle,
  };
}
// 数组对象去重
export function utilUniqueObj<
  T extends {
    [p: string | number]: any;
  },
>(arr: T[], label: string = "id"): T[] {
  const res = new Map();
  return arr.filter((a) => !res.has(a[label]) && res.set(a[label], 1));
}
// 从嵌套数组中获取指定参数，并更改该参数的键名
type RenameKeys<T, K extends Record<string, keyof T>> = {
  [P in keyof K]: T[K[P]];
} & {
  children?: RenameKeys<T, K>[];
};

export function utilRename<T, K extends Record<string, keyof T>>({
  data,
  keys,
}: {
  data: T[];
  keys: K;
}): RenameKeys<T, K>[] {
  if (!data || data.length === 0) return [];

  return data.map((item) => {
    const newItem: any = {};

    // 处理键名映射（排除children）
    for (const [newKey, oldKey] of Object.entries(keys)) {
      if (newKey === "children") continue;

      // 安全访问属性
      const value = (item as any)[oldKey];
      newItem[newKey] = value !== undefined ? value : null;
    }

    // 特殊处理children - 使用类型安全的访问方式
    if ("children" in keys) {
      const childrenKey = keys.children as keyof T;

      // 使用类型断言确保安全访问
      if (item && typeof item === "object" && childrenKey in (item as object)) {
        const childrenData = (item as any)[childrenKey];

        if (Array.isArray(childrenData)) {
          newItem.children = utilRename({
            data: childrenData,
            keys,
          });
        }
      }
    }

    return newItem as RenameKeys<T, K>;
  });
}

// 根据传入的name获取options中name所在项的数组，并转换成el-segmented组件所需的数据
export function utilSegmentOptionsByName(
  targetName: string,
  menuData: TsStore.RouteDynamic[]
) {
  // 递归查找目标菜单项
  function findMenuItem(
    name: string,
    items: TsStore.RouteDynamic[]
  ): TsStore.RouteDynamic[] | null {
    for (const item of items) {
      if (item.name === name) {
        return items.filter((item) => !item.meta?.isDetail);
      }
      if (item.children && item.children.length > 0) {
        const found = findMenuItem(name, item.children);
        if (found) return found;
      }
    }
    return null;
  }
  // 查找目标菜单项
  const menuItems = findMenuItem(targetName, menuData);
  if (!menuItems || menuItems.length === 0) {
    return [];
  }
  // 将children转换为el-segmented所需的options格式
  return menuItems.map((child: TsStore.RouteDynamic) => ({
    label: child.meta?.label || child.name,
    value: child.name,
    icon: child.meta?.icon,
  }));
}

/**
 * 低优先级任务
 * @param fn 所执行任务
 */
export let lowPriorityFn = (fn: () => void) => {
  if (window.requestIdleCallback) {
    lowPriorityFn = (func) => window.requestIdleCallback(func);
  } else if (window.requestAnimationFrame) {
    lowPriorityFn = (func) => window.requestAnimationFrame(func);
  } else {
    lowPriorityFn = (func) => setTimeout(func, 0);
  }

  lowPriorityFn(fn);
};

/**
 * 复制内容
 * @param text 要复制的内容
 * @returns 复制成功返回Promise, 拒绝状态为失败，成功状态为复制成功
 */
export let copyText = (text: string): Promise<void> => {
  if (navigator.clipboard) {
    copyText = async (str: string) => {
      await navigator.clipboard.writeText(str);
      return;
    };
  } else {
    copyText = (str: string) =>
      new Promise((res, rej) => {
        const input = document.createElement("input");
        input.style = "display: none;";
        document.body.appendChild(input);
        input.setAttribute("value", str);
        input.select();

        const result = document.execCommand("copy");

        lowPriorityFn(() => {
          document.body.removeChild(input);
        });

        result ? res() : rej();
      });
  }

  return copyText(text);
};

/**
 * 判断是否为thenable
 * @param obj 
 * @returns 
*/
export function isThenable(obj: any) {
  return obj !== null && (typeof obj === "object" || typeof obj === "function") && typeof obj.then === "function";
}

/**
 * 防抖函数
 * @param fn 要防抖的函数
 * @param delay[number] 延迟时间
 * @param thisArg[any] 上下文环境
 * @returns 防抖后的函数
 */
export function createDebounceFunc<T extends (...args: any[]) => any>(
  fn: T,
  delay: number,
  thisArg?: any
) {
  let timer: ReturnType<typeof setTimeout> | null = null;
  return function (...args: Parameters<T>) {
    return new Promise<ReturnType<T>>((res, rej) => {
      if (timer) {
        clearTimeout(timer);
      }
      timer = setTimeout(() => {
        try {
          const result = fn.apply(thisArg, args);
          if (isThenable(result)) {
            result.then(res).catch(rej);
          } else {
            res(result);
          }
        } catch (error) {
          rej(error);
        }
      }, delay);
    });
  };
}

/**
 * 秒数转换函数
 * @param sec 秒数
 * @returns XX分XX秒
 */
export function secToMinSec(sec: number) {
  const min = Math.floor(sec / 60);
  const second = Math.floor(sec % 60);
  return `${min}分${second}秒`;
}
