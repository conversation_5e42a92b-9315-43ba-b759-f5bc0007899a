/* eslint-disable */
/* prettier-ignore */
// @ts-nocheck
// noinspection JSUnusedGlobalSymbols
// Generated by unplugin-auto-import
// biome-ignore lint: disable
export {}
declare global {
  const EffectScope: typeof import('vue')['EffectScope']
  const ElMessage: typeof import('element-plus/es')['ElMessage']
  const ElMessageBox: typeof import('element-plus/es')['ElMessageBox']
  const ElementPlus: typeof import('./src/assets/utils/ElementPlus')['default']
  const EventBus: typeof import('./src/assets/utils/eventBus')['EventBus']
  const IconSolarAltArrowDownBoldDuotone: typeof import('~icons/solar/alt-arrow-down-bold-duotone')['default']
  const IconSolarAltArrowUpBoldDuotone: typeof import('~icons/solar/alt-arrow-up-bold-duotone')['default']
  const IconSolarChartLinear: typeof import('~icons/solar/chart-linear')['default']
  const IconSolarDoubleAltArrowLeftLinear: typeof import('~icons/solar/double-alt-arrow-left-linear')['default']
  const IconSolarIphoneLinear: typeof import('~icons/solar/iphone-linear')['default']
  const IconSolarLockLinear: typeof import('~icons/solar/lock-linear')['default']
  const IconSolarMagniferLinear: typeof import('~icons/solar/magnifer-linear')['default']
  const IconSolarRoundedMagniferLinear: typeof import('~icons/solar/rounded-magnifer-linear')['default']
  const IconSolarUserHandUpLinear: typeof import('~icons/solar/user-hand-up-linear')['default']
  const IconSolarUserIdLinear: typeof import('~icons/solar/user-id-linear')['default']
  const IconSolarUserLinear: typeof import('~icons/solar/user-linear')['default']
  const REGEXP: typeof import('./src/assets/utils/const')['REGEXP']
  const alova: typeof import('alova/client')
  const apiApp: typeof import('./src/assets/api/apiApp')['default']
  const apiDept: typeof import('./src/assets/api/apiDept')['default']
  const apiDict: typeof import('./src/assets/api/apiDict')['default']
  const apiDigital: typeof import('./src/assets/api/pages/apiDigital')['default']
  const apiDigitalAudio: typeof import('./src/assets/api/pages/apiDigitalAudio')['default']
  const apiDigitalCard: typeof import('./src/assets/api/pages/apiDigitalCard')['default']
  const apiDigitalModel: typeof import('./src/assets/api/pages/apiDigitalModel')['default']
  const apiMyOrder: typeof import('./src/assets/api/pages/apiMyOrder')['default']
  const apiUser: typeof import('./src/assets/api/pages/apiUser')['default']
  const computed: typeof import('vue')['computed']
  const copyText: typeof import('./src/assets/utils/eventFn')['copyText']
  const createApp: typeof import('vue')['createApp']
  const createDebounceFunc: typeof import('./src/assets/utils/eventFn')['createDebounceFunc']
  const customRef: typeof import('vue')['customRef']
  const dataRoutes: typeof import('./src/assets/data/dataRoutes')['default']
  const dataRoutesDigital: typeof import('./src/assets/data/dataRoutes')['dataRoutesDigital']
  const defineAsyncComponent: typeof import('vue')['defineAsyncComponent']
  const defineComponent: typeof import('vue')['defineComponent']
  const digitalMarketModel: typeof import('./src/assets/const/digitalMarketModel')['default']
  const echarts: typeof import('echarts')
  const effectScope: typeof import('vue')['effectScope']
  const eventBus: typeof import('./src/assets/utils/eventBus')['default']
  const getCurrentInstance: typeof import('vue')['getCurrentInstance']
  const getCurrentScope: typeof import('vue')['getCurrentScope']
  const h: typeof import('vue')['h']
  const inject: typeof import('vue')['inject']
  const isProxy: typeof import('vue')['isProxy']
  const isReactive: typeof import('vue')['isReactive']
  const isReadonly: typeof import('vue')['isReadonly']
  const isRef: typeof import('vue')['isRef']
  const isThenable: typeof import('./src/assets/utils/eventFn')['isThenable']
  const lowPriorityFn: typeof import('./src/assets/utils/eventFn')['lowPriorityFn']
  const markRaw: typeof import('vue')['markRaw']
  const myOrder: typeof import('./src/assets/const/myOrder')['default']
  const nextTick: typeof import('vue')['nextTick']
  const onActivated: typeof import('vue')['onActivated']
  const onBeforeMount: typeof import('vue')['onBeforeMount']
  const onBeforeRouteLeave: typeof import('vue-router')['onBeforeRouteLeave']
  const onBeforeRouteUpdate: typeof import('vue-router')['onBeforeRouteUpdate']
  const onBeforeUnmount: typeof import('vue')['onBeforeUnmount']
  const onBeforeUpdate: typeof import('vue')['onBeforeUpdate']
  const onDeactivated: typeof import('vue')['onDeactivated']
  const onErrorCaptured: typeof import('vue')['onErrorCaptured']
  const onMounted: typeof import('vue')['onMounted']
  const onRenderTracked: typeof import('vue')['onRenderTracked']
  const onRenderTriggered: typeof import('vue')['onRenderTriggered']
  const onScopeDispose: typeof import('vue')['onScopeDispose']
  const onServerPrefetch: typeof import('vue')['onServerPrefetch']
  const onUnmounted: typeof import('vue')['onUnmounted']
  const onUpdated: typeof import('vue')['onUpdated']
  const onWatcherCleanup: typeof import('vue')['onWatcherCleanup']
  const pinia: typeof import('pinia')
  const provide: typeof import('vue')['provide']
  const reactive: typeof import('vue')['reactive']
  const readonly: typeof import('vue')['readonly']
  const recharge: typeof import('./src/assets/const/myOrder')['recharge']
  const ref: typeof import('vue')['ref']
  const resolveComponent: typeof import('vue')['resolveComponent']
  const router: typeof import('./src/router/index')['default']
  const routes: typeof import('./src/router/routes')['default']
  const shallowReactive: typeof import('vue')['shallowReactive']
  const shallowReadonly: typeof import('vue')['shallowReadonly']
  const shallowRef: typeof import('vue')['shallowRef']
  const spend: typeof import('./src/assets/const/myOrder')['spend']
  const ssoEnable: typeof import('./src/assets/config/app')['ssoEnable']
  const store: typeof import('./src/store/index')['default']
  const storeApp: typeof import('./src/store/storeApp/index')['default']
  const storePassing: typeof import('./src/store/storePassing/index')['default']
  const storeUser: typeof import('./src/store/storeUser/index')['default']
  const toRaw: typeof import('vue')['toRaw']
  const toRef: typeof import('vue')['toRef']
  const toRefs: typeof import('vue')['toRefs']
  const toValue: typeof import('vue')['toValue']
  const tokenExpired: typeof import('./src/assets/config/app')['tokenExpired']
  const triggerRef: typeof import('vue')['triggerRef']
  const unref: typeof import('vue')['unref']
  const useAttrs: typeof import('vue')['useAttrs']
  const useCssModule: typeof import('vue')['useCssModule']
  const useCssVars: typeof import('vue')['useCssVars']
  const useDetail: typeof import('./src/assets/utils/useDetail')['default']
  const useEasyStorge: typeof import('./src/assets/utils/useEasyStorge')['default']
  const useId: typeof import('vue')['useId']
  const useLink: typeof import('vue-router')['useLink']
  const useModel: typeof import('vue')['useModel']
  const useRoute: typeof import('vue-router')['useRoute']
  const useRouter: typeof import('vue-router')['useRouter']
  const useSlots: typeof import('vue')['useSlots']
  const useTemplateRef: typeof import('vue')['useTemplateRef']
  const utilCurrUrl: typeof import('./src/assets/utils/eventFn')['utilCurrUrl']
  const utilMountedReady: typeof import('./src/assets/utils/eventFn')['utilMountedReady']
  const utilRename: typeof import('./src/assets/utils/eventFn')['utilRename']
  const utilRouteDynamic: typeof import('./src/assets/utils/eventFn')['utilRouteDynamic']
  const utilSegmentOptionsByName: typeof import('./src/assets/utils/eventFn')['utilSegmentOptionsByName']
  const utilThemeToggle: typeof import('./src/assets/utils/eventFn')['utilThemeToggle']
  const utilUniqueObj: typeof import('./src/assets/utils/eventFn')['utilUniqueObj']
  const utilUrlRemoveParam: typeof import('./src/assets/utils/eventFn')['utilUrlRemoveParam']
  const vueuse: typeof import('@vueuse/core')
  const watch: typeof import('vue')['watch']
  const watchEffect: typeof import('vue')['watchEffect']
  const watchPostEffect: typeof import('vue')['watchPostEffect']
  const watchSyncEffect: typeof import('vue')['watchSyncEffect']
  const whiteList: typeof import('./src/assets/config/app')['whiteList']
}
// for type re-export
declare global {
  // @ts-ignore
  export type { Component, Slot, Slots, ComponentPublicInstance, ComputedRef, DirectiveBinding, ExtractDefaultPropTypes, ExtractPropTypes, ExtractPublicPropTypes, InjectionKey, PropType, Ref, MaybeRef, MaybeRefOrGetter, VNode, WritableComputedRef } from 'vue'
  import('vue')
}
