<script setup lang="ts">
const props = defineProps<{
	modelValue: string;
	type: TsDigital.UploadFileType;
}>();
const emits = defineEmits(["update:modelValue"]);
const model = vueuse.useVModel(props, "modelValue", emits);
// 视频配置
const videoConfig: TsDigitalVideo.Config = {
	width: "100%",
	height: "100%",
	controls: true,
};
// 组件实例
const uploadRef = useTemplateRef<TsElementPlus.UploadInstance>("uploadRef");
// 文件列表
const fileList = ref<TsElementPlus.UploadUserFile[]>([]);
// 预览地址
const previewUrl = ref<string | null>(null);
// 文件上传地址
const fileUploadUrl = ref<string>("");
// 上传文件类型
const fileType = ref<TsApis.ApiData<"general", "post_aivatarapi_gettemporaryuploadurl">["fileType"] | null>(null);
// 根据props.type确定accept类型和api fileType参数
const fileConfig = computed((): TsDigital.UploadFileConfig => {
	switch (props.type) {
		case "video":
			return {
                name: "视频",
				accept: ".mp4,.webm",
				limit: 50,
                headerType: fileType.value == 'webm' ? 'video/webm' : 'video/mp4'
			};
		case "audio":
			return {
                name: "音频",
				accept: ".wav",
				limit: 50,
                headerType: 'audio/x-wav'
			};
		case "image":
		default:
			return {
                name: "图片",
				accept: ".png,.jpeg",
				limit: 50,
                headerType: fileType.value == 'png' ? 'image/png' : 'image/jpeg'
			};
	}
});

// 获取上传地址
const { loading, send: onSubmit, onSuccess } = apiDigital.uploadFile();
onSuccess((res) => {
	fileUploadUrl.value = res.data.data.temporaryUrl;
	model.value = res.data.data.objectUrl;    
	uploadRef.value?.submit();
});
// 获取选择文件
const uploadChange: TsElementPlus.UploadProps["onChange"] = (uploadFile, uploadFiles) => {
	if (uploadFile.status === "ready") {
		// 处理文件类型判断逻辑
		const fileName = uploadFile.name.toLowerCase().split(".");
		fileType.value = fileName[fileName.length - 1] as TsApis.ApiData<"general", "post_aivatarapi_gettemporaryuploadurl">["fileType"];
        // 上传文件
        onSubmit({ fileType: fileType.value! });
	}
	// 保存当前文件引用
	if (uploadFile.raw) {
		// 生成预览URL
		previewUrl.value = URL.createObjectURL(uploadFile.raw);
	}
	// 更新文件列表
	fileList.value = [...uploadFiles];
};
// 清除预览
const clearPreview = () => {
	if (previewUrl.value) {
		URL.revokeObjectURL(previewUrl.value);
		previewUrl.value = null;
	}
	fileList.value = [];
};

// 组件卸载时清理预览URL
onUnmounted(() => {
	clearPreview();
});
</script>
<template>
	<el-upload
		ref="uploadRef"
		v-model:file-list="fileList"
		:action="fileUploadUrl"
		:auto-upload="false"
		:headers="{
			'Content-Type': `${props.type}/${fileType}`,
		}"
        method="PUT"
		:show-file-list="true"
		:accept="fileConfig.accept"
		:limit="1"
		:on-change="uploadChange"
		:on-remove="clearPreview">
		<base-button :loading="loading">选择{{ fileConfig.name }}</base-button>
		<template #tip>
			<div class="el-upload__tip">{{ fileConfig.name }} 文件大小不超过 {{ fileConfig.limit }}M</div>
		</template>
	</el-upload>

	<!-- 预览区域 -->
	<div v-if="previewUrl" class="preview-container">
		<!-- 图片预览 -->
		<div v-if="props.type === 'image'" class="preview-image">
			<img :src="previewUrl" alt="预览图片" />
		</div>

		<!-- 视频预览 -->
		<div v-else-if="props.type === 'video'" class="preview-video">
			<digital-video-play :video-src="previewUrl" :config="videoConfig"></digital-video-play>
		</div>

		<!-- 音频预览 -->
		<div v-else-if="props.type === 'audio'" class="preview-audio">
			<digital-audio-play :option="{ avatar: '', audioSrc: previewUrl }" />
		</div>
	</div>
</template>
<style scoped lang="scss">
.preview-container {
	width: 100%;
	display: flex;
	justify-content: center;
	align-items: center;
	border: 1px dashed var(--el-border-color);
	border-radius: var(--el-border-radius-base);
	padding: var(--el-gap-half);
	.preview-image {
		width: 100%;
        img {
            width: 100%;
            height: 100%;
            display: block;
            object-fit: cover;
        }
    }
	.preview-video {
		width: 100%;
	}
	.preview-audio {
		width: 100%;
	}
}
</style>
