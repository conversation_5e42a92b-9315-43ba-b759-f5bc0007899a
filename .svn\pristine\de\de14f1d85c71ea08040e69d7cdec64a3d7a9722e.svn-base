namespace TsModelMarket {
  type ModleData = TsApis.ApiResponse<'general', "post_aivatarapi_robotlist">['rows'][number]
  type SenceData = NonNullable<ModleData['sceneList']>[number]
  type ModelType = '' |'latest' | 'hottest' | 'male' | 'female' | 'greenScreen' | 'standing' | 'sitting' | 'advanced' | 'foreign'
  interface VideoType {
    sceneName: string;
    sceneCode: string;
    proportion: string;
    description: string;
    videoUrl: string;
    coverUrl: string;
    coverMattingUrl: string | null;
    sceneType:  0 | 1,
    duration: number
  }
}
