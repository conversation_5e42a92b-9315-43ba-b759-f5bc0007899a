<script setup lang="ts">
// 配置信息
const { collapse } = pinia.storeToRefs(storeApp());
const { nickName, deptName } = pinia.storeToRefs(storeUser());
// 图标
const iconCollapse = computed(() => (collapse.value ? "solar:widget-4-linear" : "solar:widget-4-bold"));
// 折叠
function onCollapse() {
	collapse.value = !collapse.value;
}

</script>
<template>
	<div class="the-header">
		<div class="header-left">
			<base-icon :icon="iconCollapse" @click="onCollapse" class="header-icon-collapse" />
			<the-breadcrumb />
		</div>
		<div class="header-right">
			<span>{{ `${nickName} / ${deptName || '无所属部门'}` }}</span>
			<the-handle />
		</div>
	</div>
</template>
<style scoped>
.the-header {
	height: 100%;
	display: flex;
	align-items: center;
	justify-content: space-between;
	padding: 0 var(--el-gap-half);
	font-size: var(--el-font-size-small);
}
.header-left {
	display: flex;
	align-items: center;
	gap: var(--el-gap-half);
}
.header-icon-collapse {
	cursor: pointer;
	color: var(--el-color-primary);
	font-size: var(--el-font-size-medium);
}
.header-right {
	display: flex;
	align-items: center;
	gap: var(--el-gap-half);
}
</style>
