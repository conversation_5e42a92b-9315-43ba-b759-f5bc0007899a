namespace TsInput {
    type Model = Exclude<TsElementPlus.InputProps["modelValue"], null | undefined>;

    interface Sets extends Partial<TsElementPlus.InputProps> {
    }

    interface Slots {
        prepend?(_: {}): any;
        prefix?(_: {}): any;
        suffix?(_: {}): any;
        append?(_: {}): any;
    }
}
namespace TsInputNumber {
    type Model = number;
    interface Sets extends Partial<TsElementPlus.InputNumberProps> {
    }
    interface Slots {
        prefix?(_: {}): any;
        suffix?(_: {}): any;
        decreaseIcon?(_: {}): any;
        increaseIcon?(_: {}): any;
    }
}