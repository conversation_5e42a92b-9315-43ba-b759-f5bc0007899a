<script setup lang="ts">
const props = defineProps<{
    modelValue?: TsDept.Id;
}>()
const setsHandle: TsButton.Sets = {
    size: "small",
    circle: true,
    plain: true,
}
const setsHandleFold: TsButton.Sets = {
    ...setsHandle,
    icon: markRaw(IconSolarAltArrowDownBoldDuotone)
}
const setsHandleUnfold: TsButton.Sets = {
    ...setsHandle,
    icon: markRaw(IconSolarAltArrowUpBoldDuotone)
}
const setsInput:TsInput.Sets = {
    placeholder: "请输入部门名称"
}
const setsTree:TsTree.Sets = {
    nodeKey: "value"
}
const treeEl = useTemplateRef<TsTree.El>("treeEl")
const emits = defineEmits(["update:modelValue"]);
const model = vueuse.useVModel(props, "modelValue", emits);
const {data: options} = apiDept.getTree();
const filterText = ref<TsInput.Model>("");
watch(() => props.modelValue, (val) => {
    if (!val && treeEl.value) treeEl.value.removeHighlight();
})
watch(filterText, (val) => {
    treeEl.value && treeEl.value.filter(val as string)
})

function onFold(bol: boolean) {
    treeEl.value?.onFold(bol);
}
</script>
<template>
    <div class="the-tree-dept">
        <base-input v-model="filterText" :sets="setsInput"/>
        <base-tree v-model="model" ref="treeEl" :options="options" :sets="setsTree"/>
        <div class="tree-dept-handle">
            <base-button :sets="setsHandleUnfold" @click="onFold(false)" title="全部折叠"></base-button>
            <base-button :sets="setsHandleFold" @click="onFold(true)" title="全部展开"></base-button>
        </div>
    </div>
</template>
<style scoped>
.the-tree-dept {
    height: 100%;
    display: flex;
    flex-direction: column;
}
.base-input {
    width: 220px;
    margin-bottom: var(--base-gap-half);
}
.base-tree {
    flex: 1;
    overflow: auto;
}
.tree-dept-handle {
    display: flex;
    align-items: center;
    justify-content: flex-end;
}
.tree-dept-handle :deep(.el-icon) {
    font-size: 1.5em;
}
</style>