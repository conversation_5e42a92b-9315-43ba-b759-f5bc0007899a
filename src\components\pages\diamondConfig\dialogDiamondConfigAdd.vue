<script setup lang="ts">
// 弹框显示状态
const visible = ref(false);
// 表单ref
const formRef = useTemplateRef<TsForm.El>("formRef");

// 表单校验规则
const rules = reactive<TsElementPlus.FormRules>({
	amount: [
		{ required: true, message: "请输入金额", trigger: "blur" },
		{ pattern: /^[0-9]+(\.[0-9]{0,2})?$/, message: "请输入正确的金额格式(最多2位小数)", trigger: "change" }
	],
	diamondCount: [
		{ required: true, message: "请输入钻石数量", trigger: "blur" },
	],
});

// 表单API
const {
    form,
	loading,
	send: submit,
	reset,
	onSuccess,
} = apiDiamondConfig.add();

onSuccess(() => {
	reset();
	visible.value = false;
});

// 提交表单
async function handleSubmit(e?: Event) {
	e?.preventDefault();
	if (!formRef.value) return;
	await formRef.value.validate();
	await submit();
}

// 取消
function handleCancel() {
	reset();
	visible.value = false;
}

// 打开弹框
function open() {
	visible.value = true;
	reset();
}

// 向父组件暴露方法
defineExpose({
	open,
});
</script>

<template>
	<base-dialog
		v-model="visible"
		title="新增钻石配置"
		width="350px"
		@confirm="handleSubmit"
		@cancel="handleCancel"
	>
		<base-form
			ref="formRef"
			:model-value="form"
			:rules="rules"
			label-width="100px"
			v-loading="loading"
			@submit="handleSubmit"
		>
            <base-form-item label="售价" prop="amount">
                <base-input
					v-model="form.amount"
					placeholder="请输入金额"
				/>
            </base-form-item>
    
            <base-form-item label="钻石数量" prop="diamondCount">
                <base-input-number
					v-model="form.diamondCount"
					placeholder="请输入钻石数量"
					style="width: 200px;"
					:precision="0"
					:step="50"
					:min="0"
					:max="99999"
				/>
            </base-form-item>
		</base-form>
	</base-dialog>
</template>
