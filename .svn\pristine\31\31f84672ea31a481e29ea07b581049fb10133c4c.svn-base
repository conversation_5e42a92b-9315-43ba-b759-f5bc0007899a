<script setup lang="ts">
// 账户信息（模拟数据）
const accountInfo = ref<TsDiamond.AccountInfo>({
  balance: 0.00
});

// 积分充值选项（模拟数据）
const rechargeOptions: TsDiamond.RechargeOption[] = [
  { id: '1', amount: 1, price: 1.00 },
  { id: '10', amount: 10, price: 10.00 },
  { id: '50', amount: 50, price: 50.00 },
  { id: '100', amount: 100, price: 100.00 },
  { id: '500', amount: 500, price: 500.00 },
  { id: '1000', amount: 1000, price: 1000.00 },
  { id: '2000', amount: 2000, price: 2000.00 },
  { id: '8000', amount: 8000, price: 8000.00 },
];

// 表单数据
const formData = reactive<TsDiamond.RechargeForm>({
  selectedOption: ''
});

// 选择充值选项
function selectOption(optionId: string) {
  formData.selectedOption = optionId;
}

// 立即充值
function handleRecharge() {
  if (!formData.selectedOption) {
    ElMessage.warning('请选择充值金额');
    return;
  }
  
  const selectedOption = rechargeOptions.find(option => option.id === formData.selectedOption);
  if (selectedOption) {
    ElMessage.success(`即将充值 ${selectedOption.amount} 积分，金额 ¥${selectedOption.price}`);
    // 这里可以调用充值接口
  }
}
</script>

<template>
  <div class="diamond-recharge">
    <!-- 账户余额 -->
    <div class="balance-section">
      <div class="balance-title">账户余额</div>
      <div class="balance-amount">
        <base-icon icon="solar:verified-check-line-duotone" class="diamond-icon" />
        <span class="amount">{{ accountInfo.balance.toFixed(2) }}</span>
      </div>
    </div>

    <!-- 积分充值选项 -->
    <div class="recharge-section">
      <div class="section-title">积分充值</div>
      <div class="recharge-options">
        <div 
          v-for="option in rechargeOptions" 
          :key="option.id"
          :class="['recharge-option', { active: formData.selectedOption === option.id }]"
          @click="selectOption(option.id)"
        >
          <div class="option-content">
            <base-icon icon="solar:verified-check-bold-duotone" class="option-diamond-icon" />
            <div class="option-amount">{{ option.amount }}</div>
            <div class="option-unit">积分</div>
          </div>
          <div class="option-price">¥{{ option.price.toFixed(2) }}</div>
        </div>
      </div>
    </div>

    <!-- 立即充值按钮 -->
    <div class="recharge-action">
      <base-button 
        @click="handleRecharge"
        :sets="{ size: 'large', type: 'primary' }"
        class="recharge-btn"
      >
        立即充值
      </base-button>
    </div>

    <!-- 充值说明 -->
    <div class="recharge-notice">
      <p>1. 积分充值后将立即到账，请确认充值金额。</p>
      <p>2. 充值过程中如遇问题，请联系客服处理。</p>
    </div>
  </div>
</template>

<style scoped lang="scss">
.diamond-recharge {
  padding: var(--el-gap);
  max-width: 1200px;
  margin: 0 auto;
}

.balance-section {
  background-color: var(--el-bg-color);
  border-radius: var(--el-border-radius-round);
  padding: var(--el-gap);
  margin-bottom: var(--el-gap);
  text-align: center;

  .balance-title {
    font-size: 16px;
    color: var(--el-text-color-regular);
    margin-bottom: var(--el-gap-half);
  }

  .balance-amount {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: var(--el-gap-half);

    .diamond-icon {
      font-size: 24px;
      color: var(--el-color-primary);
    }

    .amount {
      font-size: 32px;
      font-weight: bold;
      color: var(--el-color-primary);
    }
  }
}

.recharge-section {
  background-color: var(--el-bg-color);
  border-radius: var(--el-border-radius-round);
  padding: var(--el-gap);
  margin-bottom: var(--el-gap);

  .section-title {
    font-size: 18px;
    font-weight: bold;
    color: var(--el-text-color-primary);
    margin-bottom: var(--el-gap);
  }

  .recharge-options {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: var(--el-gap);

    .recharge-option {
      background-color: var(--el-bg-color-page);
      border: 2px solid var(--el-border-color-light);
      border-radius: var(--el-border-radius-round);
      padding: var(--el-gap);
      cursor: pointer;
      transition: all 0.3s ease;
      text-align: center;

      &:hover {
        border-color: var(--el-color-primary-light-5);
        box-shadow: var(--el-box-shadow-light);
      }

      &.active {
        border-color: var(--el-color-primary);
        background-color: var(--el-color-primary-light-9);
      }

      .option-content {
        display: flex;
        flex-direction: column;
        align-items: center;
        gap: var(--el-gap-half);
        margin-bottom: var(--el-gap-half);

        .option-diamond-icon {
          font-size: 48px;
          color: var(--el-color-primary-light-3);
          margin-bottom: var(--el-gap-half);
        }

        .option-amount {
          font-size: 24px;
          font-weight: bold;
          color: var(--el-text-color-primary);
          line-height: 1;
        }

        .option-unit {
          font-size: 14px;
          color: var(--el-text-color-regular);
        }
      }

      .option-price {
        font-size: 16px;
        font-weight: bold;
        color: var(--el-color-primary);
        background-color: var(--el-color-primary-light-8);
        padding: 4px 12px;
        border-radius: var(--el-border-radius-round);
        display: inline-block;
      }
    }
  }
}

.recharge-action {
  text-align: center;
  margin-bottom: var(--el-gap);

  .recharge-btn {
    min-width: 200px;
  }
}

.recharge-notice {
  background-color: var(--el-bg-color);
  border-radius: var(--el-border-radius-round);
  padding: var(--el-gap);
  color: var(--el-text-color-regular);
  font-size: 14px;
  line-height: 1.6;

  p {
    margin: 0;
    margin-bottom: var(--el-gap-half);

    &:last-child {
      margin-bottom: 0;
    }
  }
}

@media (max-width: 768px) {
  .recharge-options {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (max-width: 480px) {
  .recharge-options {
    grid-template-columns: 1fr;
  }
}
</style>
