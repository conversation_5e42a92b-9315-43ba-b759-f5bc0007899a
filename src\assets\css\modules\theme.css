:root {
	--el-gap: 20px;
	--el-gap-half: 10px;
	--el-scroll-width: 4px;
	--el-scroll-color: var(--el-color-primary-light-3);
	--el-scroll-color-active: var(--el-color-primary);
	--el-color-white-light: #dbdbdb;
	--el-color-background: #f5f5f5;
	--el-border-radius-middle: 10px;
	--el-linear-white: linear-gradient(to right bottom, rgba(255, 255, 255, 0.15), rgba(255, 255, 255, 0.05));
	--el-linear-primary: linear-gradient(to right bottom, var(--el-color-primary-light-3), var(--el-color-primary));
	--el-linear-success: linear-gradient(to right bottom, var(--el-color-success-light-3), var(--el-color-success));
	--el-linear-warning: linear-gradient(to right bottom, var(--el-color-warning-light-3), var(--el-color-warning));
	--el-linear-danger: linear-gradient(to right bottom, var(--el-color-danger-light-3), var(--el-color-danger));
	/* --el-box-shadow: 0px 3px 2px -39px rgba(0, 0, 0, 0.076), 0px 7px 5px -39px rgba(0, 0, 0, 0.109), 0px 12px 10px -39px rgba(0, 0, 0, 0.135), 0px 22px 18px -39px rgba(0, 0, 0, 0.161), 0px 42px 33px -39px rgba(0, 0, 0, 0.194), 0px 100px 80px -39px rgba(0, 0, 0, 0.27);
     */
	--el-box-shadow-part: 0px 3px 6px 0px rgba(0, 0, 0, 0.05);
	--el-box-shadow-avatar: 0px 5px 15px 0px rgb(0 0 0 / 15%);
}

html:not(.dark) {
	--el-menu-bg-color: #404f65;
}

/* Alternative custom animation style */
::view-transition-old(root),
::view-transition-new(root) {
	height: auto;
	width: 100vw;
	animation: none;
	mix-blend-mode: normal;
}
html.dark::view-transition-old(root) {
	z-index: 2147483646;
}
html.dark::view-transition-new(root) {
	z-index: 100;
}
html::view-transition-old(root) {
	z-index: 100;
}
html::view-transition-new(root) {
	z-index: 2147483646;
}
