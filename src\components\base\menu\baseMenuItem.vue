<script lang="ts" setup>
import {Icon} from "@iconify/vue"

const {option} = defineProps<{
    option: TsMenu.Option
}>()
const optionObj = computed(() => option.path ? option : option.children![0]);
const isSubMenu = computed(() => optionObj.value.children && optionObj.value.children.length)
const name = computed(() => optionObj.value.name!.toString());
const label = computed(() => optionObj.value.meta?.label);
const icon = computed((): string => optionObj.value.meta!.icon as string);
</script>
<template>
    <el-sub-menu :index="name" class="base-sub-menu" v-if="isSubMenu">
        <template #title>
            <div class="el-icon" v-if="icon">
                <Icon :icon="icon"/>
            </div>
            <span>{{ label }}</span>
        </template>
        <base-menu-item v-for="item in optionObj.children" :key="item.name" :option="item"/>
    </el-sub-menu>
    <el-menu-item :index="name" class="base-menu-item" v-else>
        <div class="el-icon" v-if="icon">
            <Icon :icon="icon"/>
        </div>
        <template #title>{{ label }}</template>
    </el-menu-item>
</template>
<style scoped>
.base-menu-item,
.base-sub-menu,
.base-sub-menu :deep(.el-sub-menu__title) {
    --el-menu-item-height: 44px;
    --el-menu-sub-item-height: var(--el-menu-item-height);
}

.base-sub-menu :deep(.el-sub-menu__title + .el-menu) {
    margin-top: var(--el-gap-half);
}

.base-menu-item,
.base-sub-menu :deep(.el-sub-menu__title) {
    --bg-color: var(--el-linear-white);
    background-color: transparent;
    background-image: var(--bg-color);
    background-repeat: no-repeat;
    background-size: 0 100%;
    background-position: right center;
    transition: background-size var(--el-transition-duration);
    overflow: hidden;
    border-radius: var(--el-border-radius-base);
    position: relative;
}

.base-menu-item:not(.is-active):hover {
    --bg-color: linear-gradient(to right bottom, rgba(255, 255, 255, .05), rgba(255, 255, 255, .03));
    background-size: 100% 100%;
    background-position: left center;
}
.base-sub-menu.is-active:not(.is-opened) :deep(.el-sub-menu__title),
.base-menu-item.is-active {
    --bg-color: var(--el-linear-primary);
    color: var(--el-color-white);
    background-size: 100% 100%;
    background-position: left center;
    animation: active var(--el-transition-duration) linear forwards;
}

.base-sub-menu.is-active:not(.is-opened) :deep(.el-sub-menu__title) {
    color: var(--el-color-white);
}
@keyframes active {
    from {
        background-size: 0 100%;
    }
    to {
        background-size: 100% 100%;
    }
}
</style>