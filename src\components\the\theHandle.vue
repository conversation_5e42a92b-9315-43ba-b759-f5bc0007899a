<script setup lang="ts">
const {avatar} = pinia.storeToRefs(storeUser());
const {isLock} = pinia.storeToRefs(storeApp());
const {val, icon, toggle} = utilThemeToggle();
// 图标
const iconLock = "solar:lock-keyhole-minimalistic-linear";
const iconLogout = "solar:power-linear";

// 锁屏
function onLock() {
    isLock.value = true;
    router.push({
        name: "Lock"
    });
}
// 退出登录接口
const { send: sendLogout } = apiApp.logout();
</script>
<template>
    <el-popover width="246px">
        <template #reference>
            <el-avatar :src="avatar" />
        </template>
        <div class="the-handle">
            <div class="handle-item" @click="toggle">
                <base-icon :icon="icon"/>
                <div class="handle-label">{{ val ? "明亮" : "黑暗" }}</div>
            </div>
            <div class="handle-item" @click="onLock">
                <base-icon :icon="iconLock"/>
                <div class="handle-label">锁屏</div>
            </div>
            <div class="handle-item" @click="$router.push({ name: 'My' })">
                <base-icon icon="solar:home-linear"/>
                <div class="handle-label">我的</div>
            </div>
            <div class="handle-item danger" @click="sendLogout">
                <base-icon :icon="iconLogout"/>
                <div class="handle-label">退出</div>
            </div>
        </div>
    </el-popover>
</template>
<style scoped>
.the-handle {
    display: flex;
    flex-wrap: wrap;
    gap: var(--el-gap);
    width: fit-content;
}
.handle-item {
    width: 60px;
    text-align: center;
    cursor: pointer;
    display: flex;
    flex-direction: column;
    align-items: center;
}
.handle-item .base-icon {
    font-size: 1.2em;
}
.handle-label {
    margin-top: var(--el-gap-half);
}
.handle-item:hover {
    color: var(--el-color-primary);
}
.handle-item.danger {
    color: var(--el-color-danger);
}
</style>