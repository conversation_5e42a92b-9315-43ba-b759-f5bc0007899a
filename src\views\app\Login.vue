<script setup lang="ts">
// 验证码接口
const {data: dataCaptcha, onSuccess: onSuccessCaptcha, send: sendCaptcha} = apiApp.getCaptchaImage();
onSuccessCaptcha(() => {
	if (dataCaptcha.value && dataCaptcha.value.uuid) {
		form.uuid = dataCaptcha.value.uuid;  // 获取验证码时设置uuid
	}
})
// 项目标题
const title = import.meta.env.VITE_BASE_TITLE;
// 表单引用
const formRef = ref();
// 登录表单
const form = reactive<TsLogin.Form>({
    username: "admin",
    password: "admin123",
    code: "",
    uuid: "",
});
// 登录设置
const setsLogin = computed((): TsButton.Sets=>({
    block: true,
    loading: loadingLogin.value || loadingUserInfo.value,
}))
// 注册设置
const setsRegister = shallowRef<TsButton.Sets>({
    block: true,
    type: 'default',
})
// 账号设置
const setsAccount = computed((): TsInput.Sets=>({
    placeholder: "请输入账号",
    clearable: true,
    prefixIcon: markRaw(IconSolarUserLinear),
}));
// 密码设置
const setsPassword = computed((): TsInput.Sets=>({
    type: "password",
    placeholder: "请输入密码",
    clearable: true,
    prefixIcon: markRaw(IconSolarLockLinear),
    showPassword: true,
}));
// 表单设置
const setsForm = computed((): TsForm.Sets=>({
    size: "large",
    rules: {
        username: [{ required: true, message: "请输入账号", trigger: "blur" }],
        password: [{ required: true, message: "请输入密码", trigger: "blur" }],
        code: [{ required: true, message: "请输入验证码", trigger: "blur" }]
    }
}));
// 登录接口
const { loading: loadingLogin, send: sendLogin, onError: onErrorLogin } = apiApp.login(form);
onErrorLogin(() => {
	// 刷新验证码
	sendCaptcha();
})
// 获取用户信息
const { send: sendUserInfo } = apiApp.getUserInfo();
const { send: setAccount } = apiApp.getAccount();

const loadingUserInfo = ref(false);


// 登录
async function onLogin() {
    if (!formRef.value) return;
    try {
		await formRef.value.validate();
		loadingUserInfo.value = true;
		await sendLogin();
		await Promise.all([sendUserInfo(), setAccount()]);
		// 登录成功，触发登录事件
		eventBus.emit("login");
	} finally {
		loadingUserInfo.value = false;
	}
}
</script>
<template>
	<div class="login">
		<div class="logo-box">
			<div class="logo">
				<img src="/vite.svg" alt="" />
			</div>
			<div class="logo-text">{{ title }}</div>
		</div>
		<div class="form-box">
			<div class="form-title">欢迎登录</div>
			<div class="form-cont">
                <base-form ref="formRef" v-model="form" :sets="setsForm" @keyup.enter="onLogin">
                    <base-form-item prop="username">
                        <base-input v-model="form.username" :sets="setsAccount" />
                    </base-form-item>
                    <base-form-item prop="password">
                        <base-input v-model="form.password" :sets="setsPassword" />
                    </base-form-item>
					<base-form-item prop="code">
						<base-input v-model="form.code">
							<template #append>
								<el-image :src="dataCaptcha.img" v-if="dataCaptcha.img" @click="sendCaptcha" title="点击刷新验证码" />
							</template>
						</base-input>
					</base-form-item>
					<div class="button-group">
						<base-button :sets="setsRegister" @click="$router.push({ name: 'Register' })">注册</base-button>
                    	<base-button class="login-button" :sets="setsLogin" @click="onLogin">登录</base-button>
					</div>
                </base-form>
			</div>
		</div>
	</div>
</template>
<style scoped>
.login {
	width: 100%;
	height: 100%;
	background: url("@/assets/image/bg-login.jpg") no-repeat center;
	background-size: cover;
	position: relative;
}

.logo-box {
	position: absolute;
	left: 10%;
	top: 10%;
	display: flex;
	align-items: center;
	gap: var(--el-gap);
}

.button-group {
	display: flex;
	justify-content: space-between;
}

.logo {
	width: 40px;
	height: 40px;
}

.logo-text {
	font: bold 28px "Microsoft YaHei UI";
	letter-spacing: 2px;
}

.form-box {
	width: 360px;
	height: auto;
	border-radius: var(--el-border-radius-round);
	overflow: hidden;
	box-shadow: var(--el-box-shadow);
	position: absolute;
	right: 14%;
	top: calc(50% - 210px);
	background-color: rgba(255, 255, 255, 0.35);
	backdrop-filter: blur(4px);
	padding: var(--el-gap) calc(var(--el-gap) * 2);
}

.form-title {
	padding: 10px 0 30px;
	text-align: center;
	font: bold 24px "Microsoft YaHei UI";
	letter-spacing: 3px;
}
.form-box :deep(.el-input-group__append) {
	margin-left: var(--el-gap-half);
	padding: 0;
}
.form-box :deep(.el-image) {
	width: 83px;
	height: 38px;
	cursor: pointer;
}
</style>
