import type { RouteLocationNormalizedGeneric } from "vue-router";

export default pinia.defineStore("storeApp", {
	state: (): TsStore.App => ({
		token: "", // 登录token
		tabs: [], // 标签栏
		tabsShowIcon: true, // 展示标签栏图标
		keepAlive: [], // 缓存路由
		collapse: false, // 是否折叠菜单
		theme: "light", // 主题
		isLock: false, // 是否锁屏
		routesDynamic: [], // 动态路由
		breadcrumbShowIcon: false, // 展示面包屑图标
		routeInfo: {}, // 路由参数信息Map
	}),
	getters: {},
	actions: {
		// 跳转增加tab
		tabsAdd(to: RouteLocationNormalizedGeneric) {
			const tabItem = this.tabs.find((tab) => tab.name === to.name);
			if (!tabItem) {
				this.tabs.push({
					name: to.name as string,
					label: (to?.meta?.label ?? '') as string,
					icon: (to?.meta?.icon) as string,
				});
			}
			this.routeInfo[to.name as string] = {
				params: to?.params || null,
				query: to?.query || null,
			};
		},
		// 关闭移除tab
		tabsRemoved(name: string) {
			this.tabs = this.tabs.filter((tab) => tab.name !== name);
			Reflect.deleteProperty(this.routeInfo, name);
		},
	},
	// 缓存配置
	persist: {
		// 存储的名称
		key: "store_app",
		// 存储到localStorage
		storage: localStorage,
		// 指定状态缓存，不配置则缓存所有状态
		pick: ["token", "tabs", "collapse", "theme","isLock"],
	},
});
