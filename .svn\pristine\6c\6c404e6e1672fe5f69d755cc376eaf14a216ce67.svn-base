import { createWebHistory, createRouter } from "vue-router";

// 创建路由
const router = createRouter({
	history: createWebHistory(import.meta.env.VITE_BASE_ROUTER_PREFIX || "/"),
	routes: routes,
});

// 路由守卫
router.beforeEach(async (to, _from, next) => {
	// 白名单直接进入
	if (whiteList.includes(to.name as string)) return next();
	// 获取store
	const useStoreApp = storeApp();
	const { token, isLock, routesDynamic, keepAlive } = pinia.storeToRefs(useStoreApp);
	const {userType} = pinia.storeToRefs(storeUser());
	// 锁定状态下跳转锁屏页面
	if (isLock.value) return next({ name: "Lock" });
	// 单点登录，通过code获取token
	const urlParams = new URLSearchParams(window.location.search);
	const code = urlParams.get("code");
	if (ssoEnable && code) {
		token.value = await apiApp.getTokenByCode(code);
	}
	// 未登录，退出登录
	if (!token.value) {
		eventBus.emit("logout");
	}
	// 动态路由加载
	if (routesDynamic.value.length == 0) {
		routesDynamic.value = await apiApp.getRoutesDynamic();
		console.log(11233,routesDynamic.value);
		
		const list = utilRouteDynamic(routesDynamic.value, {forMenu: false, userType: userType.value});
		list.forEach((item) => {
			router.addRoute(item);
		});
		router.addRoute({
			path: "/:catchAll(.*)",
			redirect: "/error?code=404",
		});
		if (to.redirectedFrom) {
			next({ path: to.redirectedFrom.fullPath, query: to.query, replace: true });
		} else {
			next({ ...to, replace: true });
		}
		return;
	}
	// 添加到标签栏
	if (!to.meta?.noTab) {
		useStoreApp.tabsAdd(to);
	}
	// 添加到keepAlive
	if (to.meta?.keepAlive) {
		keepAlive.value.push(to.name as string);
	}
	// 设置页面标题
	const projectName = import.meta.env.VITE_BASE_TITLE;
	document.title = to.meta.label && to.meta.label !== projectName ? `${to.meta.label} - ${projectName}` : projectName;
	// 进入页面
	next();
});
// 事件监听-锁屏
eventBus.on("lockScreen", () => {
	router.push({ name: "Lock" });
});
// 事件监听-登录
eventBus.on("login", () => {
	router.push({ name: "Home" });
});
// 事件监听-退出登录
eventBus.on("logout", () => {
	if (ssoEnable) {
		// 单点登录
		const url = utilCurrUrl();
		window.location.href = `${import.meta.env.VITE_BASE_SSO_URL}/papi/sso/oauth2.0/authorize?client_id=${import.meta.env.VITE_BASE_SSO_ID}&redirect_uri=${url}&response_type=code`;
	} else {
		// 退出登录
		router.push({ name: "Login" });
	}
});
// 导出路由
export default router;
