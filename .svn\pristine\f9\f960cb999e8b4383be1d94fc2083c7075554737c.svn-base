<script setup lang="ts">
const currentTags = [
  '全部',
  '视频模板',
  '免费模板',
]

const query = reactive({
  search: '',
  type: '',
  tag: '',
})

const switchTab = (tab: string) => {
  query.type = tab
}

const searchSet = shallowRef<TsInput.Sets>({
  placeholder: '搜索形象关键词',
  prefixIcon: markRaw(IconSolarMagniferLinear),
})

const handleNewBlank = () => {
  console.log('新建空白')
}

const handleUse = (item: unknown) => {
  console.log('使用模板', item)
}

const {
  data,
  total,
  page,
  pageSize,
  refresh,
} = {} as any;

// 模拟模板数据
const templateList = ref<any[]>([
  {
    title: "圆满中秋",
    cover: "",
    videoSrc: "http://www.w3school.com.cn/example/html5/mov_bbb.mp4",
    isVideo: false,
    isPlaying: false,
    views: 1250,
    tags: ["节日祝福"], // 关联“节日祝福”标签
    type: "video", // 属于“视频模板”
  },
  {
    title: "为女神献礼",
    cover: "",
    videoSrc: "http://www.w3school.com.cn/example/html5/mov_bbb.mp4",
    isVideo: true,
    isPlaying: false,
    views: 2522,
    tags: ["银行金融"],
    type: "video",
  },
  {
    title: "金融产品介绍",
    cover: "",
    videoSrc: "http://www.w3school.com.cn/example/html5/mov_bbb.mp4",
    isVideo: true,
    isPlaying: false,
    views: 1890,
    tags: ["教育"],
    type: "free", // 属于“免费模板”
  },
  {
    title: "新年祝福",
    cover: "",
    videoSrc: "http://www.w3school.com.cn/example/html5/mov_bbb.mp4",
    isVideo: false,
    isPlaying: false,
    views: 3450,
    tags: ["热门"],
    type: "free",
  },
  {
    title: "科普小知识",
    cover: "",
    videoSrc: "http://www.w3school.com.cn/example/html5/mov_bbb.mp4",
    isVideo: true,
    isPlaying: false,
    views: 980,
    tags: ["热门"],
    type: "free",
  },
  {
    title: "商业推广",
    cover: "",
    videoSrc: "http://www.w3school.com.cn/example/html5/mov_bbb.mp4",
    isVideo: true,
    isPlaying: false,
    views: 2100,
    tags: ["推荐", "银行金融"],
    type: "video",
  },
  {
    title: "教育培训",
    cover: "",
    videoSrc: "http://www.w3school.com.cn/example/html5/mov_bbb.mp4",
    isVideo: false,
    isPlaying: false,
    views: 1560,
    tags: ["推荐"],
    type: "video",
  },
  {
    title: "生活技巧",
    cover: "",
    videoSrc: "http://www.w3school.com.cn/example/html5/mov_bbb.mp4",
    isVideo: true,
    isPlaying: false,
    views: 3200,
    tags: ["推荐", "科普讲解"],
    type: "video",
  },
  {
    title: "科技前沿",
    cover: "",
    videoSrc: "http://www.w3school.com.cn/example/html5/mov_bbb.mp4",
    isVideo: true,
    isPlaying: false,
    views: 1780,
    tags: ["银行金融"],
    type: "video",
  },
  {
    title: "健康养生",
    cover: "",
    videoSrc: "http://www.w3school.com.cn/example/html5/mov_bbb.mp4",
    isVideo: false,
    isPlaying: false,
    views: 2890,
    tags: ["科普讲解"],
    type: "video",
  },
  {
    title: "企业宣传",
    cover: "",
    videoSrc: "http://www.w3school.com.cn/example/html5/mov_bbb.mp4",
    isVideo: true,
    isPlaying: false,
    views: 1450,
    tags: ["银行金融"],
    type: "video",
  },
  {
    title: "旅游攻略",
    cover: "",
    videoSrc: "http://www.w3school.com.cn/example/html5/mov_bbb.mp4",
    isVideo: true,
    isPlaying: false,
    views: 3670,
    tags: ["银行金融"],
    type: "video",
  },
  {
    title: "美食制作",
    cover: "",
    videoSrc: "http://www.w3school.com.cn/example/html5/mov_bbb.mp4",
    isVideo: true,
    isPlaying: false,
    views: 4210,
    tags: ["科普讲解"],
    type: "video",
  },
  // {
  //   title: "职场技能",
  //   cover: "",
  //   videoSrc: "http://www.w3school.com.cn/example/html5/mov_bbb.mp4",
  //   isVideo: false,
  //   isPlaying: false,
  //   views: 1980,
  // },
  // {
  //   title: "亲子互动",
  //   cover: "",
  //   videoSrc: "http://www.w3school.com.cn/example/html5/mov_bbb.mp4",
  //   isVideo: true,
  //   isPlaying: false,
  //   views: 2750,
  // },
  // {
  //   title: "运动健身",
  //   cover: "",
  //   videoSrc: "http://www.w3school.com.cn/example/html5/mov_bbb.mp4",
  //   isVideo: true,
  //   isPlaying: false,
  //   views: 2340,
  // },
]);

</script>

<template>
  <layout-table>
    <template #form>
      <div class="top-row">
        <!-- 左侧模板选项 -->
        <div class="template-tabs">
          <span
              class="template-button"
              :class="{ active: query.type === 'video' }"
              @click="switchTab('video')"
          >
            视频模板
          </span>
          <span
              class="free-template"
              :class="{ active: query.type === 'free' }"
              @click="switchTab('free')"
          >
            免费模板
          </span>
        </div>
        <!-- 右侧输入框 -->
        <div class="right-content">
          <base-input
            v-model="query.search"
            :sets="searchSet"
            @keyup.enter="() => refresh()"
          />
        </div>
      </div>
      <div class="tag-group">
        <el-tag
            v-for="tag in currentTags"
            :key="tag"
            type="info"
            :hit="tag === query.tag"
            @click="query.tag = tag"
        >
          {{ tag }}
        </el-tag>
      </div>
    </template>
    <template #table>
      <div class="template-list">
        <!-- 新建空白卡片 -->
        <div class="template-card new-card" @click="handleNewBlank">
          <div class="card-content">
            <div class="plus-icon">+</div>
            <p>新建空白</p>
          </div>
        </div>
        <!-- 模板卡片列表循环 -->
        <!-- <div
            v-for="(item, index) in currentPageTemplates"
            :key="index"
            class="template-card"
            @click="handleCardClick(item)"
        >
          <div class="card-media">
            <video
                :poster="item.cover"
                controls
                loop
                muted
                preload="none"
                @mouseenter="playVideo($event)"
                @mouseleave="pauseVideo($event)"
            >
              <source :src="item.videoSrc" type="video/mp4" />
            </video>
          </div>
          <div class="card-info">
            <span class="title">{{ item.title }}</span>
            <span class="usage" @click.stop="handleUse(item, $event)">使用</span>
          </div>
          <div class="stats">
            <base-icon icon="uil:fire"></base-icon>
            <span>{{ item.views }}</span>
          </div>
        </div> -->
        <digital-template-market
            v-for="item in templateList"
            :key="item.id"
            :item="item"
            @clickUse="handleUse"
        />
      </div>
    </template>
    <!-- 分页组件 -->
     <template #pagination>
      <div class="pagination-container">
        <base-pagination
          v-model:current-page="page"
          v-model:page-size="pageSize"
          :total="total"
        />
      </div>
    </template>
  </layout-table>
</template>

<style scoped>
.top-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.template-tabs {
  display: flex;
  align-items: center;
  gap: 20px;
}

.template-button,
.free-template {
  cursor: pointer;
  padding: 5px 10px;
  border-radius: 4px;
}

.template-button.active,
.free-template.active {
  background-color: var(--el-color-primary);
  color: white;
}

.right-content {
  width: 180px;
  height: 25px;
  margin-right: 30px;
}
.tag-group {
  display: flex;
  gap: 10px;
  flex-wrap: wrap;
  margin-top: 16px;
}

.el-tag {
  cursor: pointer;
}
.template-list {
  display: flex;
  flex-wrap: wrap;
  gap: 16px;
  padding: 16px;
  justify-content: flex-start;
}
.template-card {
  width: 198px;
  height: 353px;
  background: var(--el-bg-color-page);
  border-radius: 8px;
  /* box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1); */
  cursor: pointer;
  display: flex;
  flex-direction: column;
  align-items: center;
  transition: transform 0.2s ease;
}
.template-card:hover {
  transform: translateY(-4px);
}
.new-card {
  background: var(--el-bg-color-page);
  justify-content: center;
  align-items: center;
}
.card-content {
  text-align: center;
  padding: 24px 0;
}
.plus-icon {
  font-size: 32px;
  color: #999;
  margin-bottom: 8px;
}
.card-media {
  width: 100%;
  height: 350px;
  position: relative;
  overflow: hidden;
  border-top-left-radius: 8px;
  border-top-right-radius: 8px;
}

.card-media video {
  width: 100%;
  height: 100%;
  object-fit: cover;
}
.cover-img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}
.video-mask {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  background: rgba(0, 0, 0, 0.4);
  color: var(--el-text-color-primary);
  padding: 8px 12px;
  border-radius: 4px;
  cursor: pointer;
}
.card-info {
  width: 100%;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 12px;
}
.title {
  font-size: 14px;
  color: var(--el-text-color-primary);
}
.usage {
  color: white;
  background-color: #409eff;
  cursor: pointer;
  font-size: 14px;
  padding: 4px 8px;
  border-radius: 4px;
  transition: background-color 0.3s;
}
.stats {
  font-size: 12px;
  color: #f56c6c;
  padding: 0 12px 8px;
  display: flex;
  align-items: center;
  gap: 4px;
  margin-right: auto; /* 将元素推向左侧 */
}
.pagination-container {
  display: flex;
  justify-content: center;
}
</style>
