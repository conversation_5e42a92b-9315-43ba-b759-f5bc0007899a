<script setup lang="ts">
const title = import.meta.env.VITE_BASE_TITLE;
const { keepAlive, collapse } = pinia.storeToRefs(storeApp());
</script>
<template>
  <div :class="{ 'layout-page': true, collapse: collapse }">
    <div class="logo">
      <div class="logo-img">
        <img src="/vite.svg" alt="" />
      </div>
    </div>
    <div class="project-name" v-if="!collapse">{{ title }}</div>
    <div class="header">
      <the-header />
    </div>
    <div class="tabs">
      <the-tabs />
    </div>
    <div class="menu">
      <the-menu-digital />
    </div>
    <div class="main">
      <router-view v-slot="{ Component }">
        <keep-alive :include="keepAlive">
          <component :is="Component" />
        </keep-alive>
      </router-view>
    </div>
  </div>
</template>
<style scoped>
.layout-page {
  height: 100%;
  display: grid;
  grid-template: 50px 40px 1fr / 150px 1fr;
  overflow: hidden;
  transition: all var(--el-transition-duration);
}

.layout-page.collapse {
  grid-template: 50px 40px 1fr / 100px 1fr;
}

.logo {
  grid-row: 1 / 2;
  grid-column: 1 / 2;
  background-color: var(--el-menu-bg-color);
  border-top-right-radius: var(--el-border-radius-round);
}
.logo-img {
  width: 30px;
  height: 30px;
  margin: 10px auto;
}
.project-name {
  grid-row: 2 / 3;
  grid-column: 1 / 2;
  background-color: var(--el-menu-bg-color);
  color: var(--el-color-white);
  text-align: center;
  line-height: 40px;
}
.layout-page.collapse .project-name {
  grid-row: 2 / 2;
}
.menu {
  grid-row: 3 / 4;
  grid-column: 1 / 2;
  border-bottom-right-radius: var(--el-border-radius-round);
  overflow: auto;
}
.layout-page.collapse .menu {
  grid-row: 2 / 4;
}
.header {
  grid-row: 1 / 2;
  grid-column: 2 / 3;
  box-shadow: 0 1px 0 0 var(--el-border-color);
  padding: 0 var(--el-gap-half);
}

.tabs {
  grid-row: 2 / 3;
  grid-column: 2 / 3;
}

.main {
  grid-row: 3 / 4;
  grid-column: 2 / 3;
  overflow-x: hidden;
  overflow-y: auto;
  background-color: var(--el-bg-color-page);
  padding: var(--el-gap-half);
}
</style>
