/* tslint:disable */
/* eslint-disable */
/**
 * 数字人 - version 1.0.0
 *
 *
 *
 * OpenAPI version: 3.0.0
 *
 *
 * NOTE: This file is auto generated by the alova's vscode plugin.
 *
 * https://alova.js.org/devtools/vscode
 *
 * **Do not edit the file manually.**
 */
import type { Alova, AlovaMethodCreateConfig, AlovaGenerics, Method } from 'alova';
import type { $$userConfigMap, alovaInstance } from '.';
import type apiDefinitions from './apiDefinitions';

type CollapsedAlova = typeof alovaInstance;
type UserMethodConfigMap = typeof $$userConfigMap;

type Alova2MethodConfig<Responded> =
  CollapsedAlova extends Alova<
    AlovaGenerics<
      any,
      any,
      infer RequestConfig,
      infer Response,
      infer ResponseHeader,
      infer L1Cache,
      infer L2Cache,
      infer SE
    >
  >
    ? Omit<
        AlovaMethodCreateConfig<
          AlovaGenerics<Responded, any, RequestConfig, Response, ResponseHeader, L1Cache, L2Cache, SE>,
          any,
          Responded
        >,
        'params'
      >
    : never;

// Extract the return type of transform function that define in $$userConfigMap, if it not exists, use the default type.
type ExtractUserDefinedTransformed<
  DefinitionKey extends keyof typeof apiDefinitions,
  Default
> = DefinitionKey extends keyof UserMethodConfigMap
  ? UserMethodConfigMap[DefinitionKey]['transform'] extends (...args: any[]) => any
    ? Awaited<ReturnType<UserMethodConfigMap[DefinitionKey]['transform']>>
    : Default
  : Default;
type Alova2Method<
  Responded,
  DefinitionKey extends keyof typeof apiDefinitions,
  CurrentConfig extends Alova2MethodConfig<any>
> =
  CollapsedAlova extends Alova<
    AlovaGenerics<
      any,
      any,
      infer RequestConfig,
      infer Response,
      infer ResponseHeader,
      infer L1Cache,
      infer L2Cache,
      infer SE
    >
  >
    ? Method<
        AlovaGenerics<
          CurrentConfig extends undefined
            ? ExtractUserDefinedTransformed<DefinitionKey, Responded>
            : CurrentConfig['transform'] extends (...args: any[]) => any
              ? Awaited<ReturnType<CurrentConfig['transform']>>
              : ExtractUserDefinedTransformed<DefinitionKey, Responded>,
          any,
          RequestConfig,
          Response,
          ResponseHeader,
          L1Cache,
          L2Cache,
          SE
        >
      >
    : never;

export interface UserProfileUpdate {
  /**
   * 用户账号
   */
  userName?: string;
  /**
   * 用户昵称
   */
  nickName: string;
  /**
   * 用户邮箱
   */
  email?: string;
  /**
   * 手机号码
   */
  phonenumber?: string;
  /**
   * 用户性别（0男 1女 2未知）
   */
  sex?: string;
}
export interface Role {
  /**
   * 角色ID
   */
  roleId?: number;
  /**
   * 角色名称
   */
  roleName?: string;
  /**
   * 角色权限字符串
   */
  roleKey?: string;
  /**
   * 显示顺序
   */
  roleSort?: number;
  /**
   * 数据范围（1：全部数据权限 2：自定数据权限 3：本部门数据权限 4：本部门及以下数据权限）
   */
  dataScope?: string;
  /**
   * 菜单树选择项是否关联显示
   */
  menuCheckStrictly?: boolean;
  /**
   * 部门树选择项是否关联显示
   */
  deptCheckStrictly?: boolean;
  /**
   * 角色状态（0正常 1停用）
   */
  status?: string;
  /**
   * 删除标志（0代表存在 2代表删除）
   */
  delFlag?: string;
  /**
   * 创建者
   */
  createBy?: string;
  /**
   * 创建时间
   */
  createTime?: string;
  /**
   * 更新者
   */
  updateBy?: string;
  /**
   * 更新时间
   */
  updateTime?: string;
  /**
   * 备注
   */
  remark?: string;
}
export interface Post {
  /**
   * 岗位ID
   */
  postId?: number;
  /**
   * 岗位编码
   */
  postCode?: string;
  /**
   * 岗位名称
   */
  postName?: string;
  /**
   * 显示顺序
   */
  postSort?: number;
  /**
   * 状态（0正常 1停用）
   */
  status?: string;
  /**
   * 创建者
   */
  createBy?: string;
  /**
   * 创建时间
   */
  createTime?: string;
  /**
   * 更新者
   */
  updateBy?: string;
  /**
   * 更新时间
   */
  updateTime?: string;
  /**
   * 备注
   */
  remark?: string;
}
export interface Dept {
  /**
   * 部门ID
   */
  deptId?: number;
  /**
   * 父部门ID
   */
  parentId?: number;
  /**
   * 祖级列表
   */
  ancestors?: string;
  /**
   * 部门名称
   */
  deptName?: string;
  /**
   * 显示顺序
   */
  orderNum?: number;
  /**
   * 负责人
   */
  leader?: string;
  /**
   * 联系电话
   */
  phone?: string;
  /**
   * 邮箱
   */
  email?: string;
  /**
   * 部门状态（0正常 1停用）
   */
  status?: string;
  /**
   * 删除标志（0代表存在 2代表删除）
   */
  delFlag?: string;
  /**
   * 创建者
   */
  createBy?: string;
  /**
   * 创建时间
   */
  createTime?: string;
  /**
   * 更新者
   */
  updateBy?: string;
  /**
   * 更新时间
   */
  updateTime?: string;
}
export interface User {
  /**
   * 用户ID
   */
  userId?: number;
  /**
   * 部门ID
   */
  deptId?: number;
  /**
   * 用户账号
   */
  userName?: string;
  /**
   * 用户昵称
   */
  nickName?: string;
  /**
   * 用户邮箱
   */
  email?: string;
  /**
   * 手机号码
   */
  phonenumber?: string;
  /**
   * 用户性别（0男 1女 2未知）
   */
  sex?: string;
  /**
   * 头像地址
   */
  avatar?: string;
  /**
   * 密码
   */
  password?: string;
  /**
   * 帐号状态（0正常 1停用）
   */
  status?: string;
  /**
   * 删除标志（0代表存在 2代表删除）
   */
  delFlag?: string;
  /**
   * 最后登录IP
   */
  loginIp?: string;
  /**
   * 最后登录时间
   */
  loginDate?: string;
  /**
   * 创建者
   */
  createBy?: string;
  /**
   * 创建时间
   */
  createTime?: string;
  /**
   * 更新者
   */
  updateBy?: string;
  /**
   * 更新时间
   */
  updateTime?: string;
  /**
   * 备注
   */
  remark?: string;
  dept?: Dept;
}
export interface DeptTreeNode {
  /**
   * 部门ID
   */
  id: number;
  /**
   * 部门名称
   */
  label: string;
  /**
   * 子部门
   */
  children?: DeptTreeNode[];
}
declare global {
  interface Apis {
    general: {
      /**
       * ---
       *
       * [GET] 登录验证码
       *
       * **path:** /captchaImage
       *
       * ---
       *
       * **Response**
       * ```ts
       * type Response = {
       *   // 状态码
       *   code?: number
       *   // 返回消息
       *   msg?: string
       *   // 验证码图片的base64编码
       *   img?: string
       *   // 验证码唯一标识
       *   uuid?: string
       *   // 验证码是否启用
       *   captchaEnabled?: boolean
       * }
       * ```
       */
      get_captchaimage<
        Config extends Alova2MethodConfig<{
          /**
           * 状态码
           */
          code?: number;
          /**
           * 返回消息
           */
          msg?: string;
          /**
           * 验证码图片的base64编码
           */
          img?: string;
          /**
           * 验证码唯一标识
           */
          uuid?: string;
          /**
           * 验证码是否启用
           */
          captchaEnabled?: boolean;
        }>
      >(
        config?: Config
      ): Alova2Method<
        {
          /**
           * 状态码
           */
          code?: number;
          /**
           * 返回消息
           */
          msg?: string;
          /**
           * 验证码图片的base64编码
           */
          img?: string;
          /**
           * 验证码唯一标识
           */
          uuid?: string;
          /**
           * 验证码是否启用
           */
          captchaEnabled?: boolean;
        },
        'general.get_captchaimage',
        Config
      >;
      /**
       * ---
       *
       * [POST] 登录
       *
       * **path:** /login
       *
       * ---
       *
       * **RequestBody**
       * ```ts
       * type RequestBody = {
       *   // 账号
       *   username: string
       *   // 密码
       *   password: string
       *   // 验证码
       *   code: string
       *   // 验证码唯一标识
       *   uuid: string
       * }
       * ```
       *
       * ---
       *
       * **Response**
       * ```ts
       * type Response = {
       *   // 状态码
       *   code?: number
       *   // 返回消息
       *   msg?: string
       *   // 认证token
       *   token?: string
       * }
       * ```
       */
      post_login<
        Config extends Alova2MethodConfig<{
          /**
           * 状态码
           */
          code?: number;
          /**
           * 返回消息
           */
          msg?: string;
          /**
           * 认证token
           */
          token?: string;
        }> & {
          data: {
            /**
             * 账号
             */
            username: string;
            /**
             * 密码
             */
            password: string;
            /**
             * 验证码
             */
            code: string;
            /**
             * 验证码唯一标识
             */
            uuid: string;
          };
        }
      >(
        config: Config
      ): Alova2Method<
        {
          /**
           * 状态码
           */
          code?: number;
          /**
           * 返回消息
           */
          msg?: string;
          /**
           * 认证token
           */
          token?: string;
        },
        'general.post_login',
        Config
      >;
      /**
       * ---
       *
       * [POST] 退出登录
       *
       * **path:** /logout
       *
       * ---
       *
       * **Response**
       * ```ts
       * type Response = {
       *   // 状态码
       *   code?: number
       *   // 返回消息
       *   msg?: string
       * }
       * ```
       */
      post_logout<
        Config extends Alova2MethodConfig<{
          /**
           * 状态码
           */
          code?: number;
          /**
           * 返回消息
           */
          msg?: string;
        }>
      >(
        config?: Config
      ): Alova2Method<
        {
          /**
           * 状态码
           */
          code?: number;
          /**
           * 返回消息
           */
          msg?: string;
        },
        'general.post_logout',
        Config
      >;
      /**
       * ---
       *
       * [GET] 用户信息
       *
       * **path:** /getInfo
       *
       * ---
       *
       * **Response**
       * ```ts
       * type Response = {
       *   // 状态码
       *   code?: number
       *   // 返回消息
       *   msg?: string
       *   // 权限列表
       *   // [items] start
       *   // [items] end
       *   permissions?: string[]
       *   // 角色列表
       *   // [items] start
       *   // [items] end
       *   roles?: string[]
       *   user?: {
       *     // 用户ID
       *     userId?: number
       *     // 用户名
       *     userName?: string
       *     // 用户昵称
       *     nickName?: string
       *     // 手机号码
       *     phonenumber?: number
       *     // 是否为管理员
       *     admin?: boolean
       *     // 头像地址
       *     avatar?: string
       *     // 性别
       *     sex?: string
       *     dept?: {
       *       // 部门ID
       *       deptId?: number
       *       // 部门名称
       *       deptName?: string
       *     }
       *     // 用户类型 "00"管理员, "01"平台用户
       *     userType: '00' | '01'
       *   }
       * }
       * ```
       */
      get_getinfo<
        Config extends Alova2MethodConfig<{
          /**
           * 状态码
           */
          code?: number;
          /**
           * 返回消息
           */
          msg?: string;
          /**
           * 权限列表
           */
          permissions?: string[];
          /**
           * 角色列表
           */
          roles?: string[];
          user?: {
            /**
             * 用户ID
             */
            userId?: number;
            /**
             * 用户名
             */
            userName?: string;
            /**
             * 用户昵称
             */
            nickName?: string;
            /**
             * 手机号码
             */
            phonenumber?: number;
            /**
             * 是否为管理员
             */
            admin?: boolean;
            /**
             * 头像地址
             */
            avatar?: string;
            /**
             * 性别
             */
            sex?: string;
            dept?: {
              /**
               * 部门ID
               */
              deptId?: number;
              /**
               * 部门名称
               */
              deptName?: string;
            };
            /**
             * 用户类型 "00"管理员, "01"平台用户
             */
            userType: '00' | '01';
          };
        }>
      >(
        config?: Config
      ): Alova2Method<
        {
          /**
           * 状态码
           */
          code?: number;
          /**
           * 返回消息
           */
          msg?: string;
          /**
           * 权限列表
           */
          permissions?: string[];
          /**
           * 角色列表
           */
          roles?: string[];
          user?: {
            /**
             * 用户ID
             */
            userId?: number;
            /**
             * 用户名
             */
            userName?: string;
            /**
             * 用户昵称
             */
            nickName?: string;
            /**
             * 手机号码
             */
            phonenumber?: number;
            /**
             * 是否为管理员
             */
            admin?: boolean;
            /**
             * 头像地址
             */
            avatar?: string;
            /**
             * 性别
             */
            sex?: string;
            dept?: {
              /**
               * 部门ID
               */
              deptId?: number;
              /**
               * 部门名称
               */
              deptName?: string;
            };
            /**
             * 用户类型 "00"管理员, "01"平台用户
             */
            userType: '00' | '01';
          };
        },
        'general.get_getinfo',
        Config
      >;
      /**
       * ---
       *
       * [GET] 用户列表
       *
       * **path:** /system/user/list
       *
       * ---
       *
       * **Query Parameters**
       * ```ts
       * type QueryParameters = {
       *   // 页码
       *   pageNum: number
       *   // 每页条数
       *   pageSize: number
       *   // 用户名称
       *   userName?: string
       *   // 手机号
       *   phonenumber?: string
       *   // 状态
       *   status?: string
       *   // 部门ID
       *   deptId?: number
       *   // 创建时间-开始
       *   'params[beginTime]'?: string
       *   // 创建时间-结束
       *   'params[endTime]'?: string
       * }
       * ```
       *
       * ---
       *
       * **Response**
       * ```ts
       * type Response = {
       *   // 状态码
       *   code?: number
       *   // 返回消息
       *   msg?: string
       *   // 总记录数
       *   total?: number
       *   // 用户列表数据
       *   // [items] start
       *   // [items] end
       *   rows?: Array<{
       *     // 用户ID
       *     userId?: number
       *     // 创建时间
       *     createTime?: string
       *     // 备注
       *     remark?: string
       *     // 用户名
       *     userName?: string
       *     // 昵称
       *     nickName?: string
       *     // 邮箱
       *     email?: string
       *     // 手机号
       *     phonenumber?: string
       *     // 性别
       *     sex?: string
       *     // 状态
       *     status?: string
       *     // 部门信息
       *     dept?: {
       *       // 部门ID
       *       deptId?: number
       *       // 部门名称
       *       deptName?: string
       *     }
       *     // 是否为管理员
       *     admin?: boolean
       *   }>
       * }
       * ```
       */
      get_system_user_list<
        Config extends Alova2MethodConfig<{
          /**
           * 状态码
           */
          code?: number;
          /**
           * 返回消息
           */
          msg?: string;
          /**
           * 总记录数
           */
          total?: number;
          /**
           * 用户列表数据
           */
          rows?: Array<{
            /**
             * 用户ID
             */
            userId?: number;
            /**
             * 创建时间
             */
            createTime?: string;
            /**
             * 备注
             */
            remark?: string;
            /**
             * 用户名
             */
            userName?: string;
            /**
             * 昵称
             */
            nickName?: string;
            /**
             * 邮箱
             */
            email?: string;
            /**
             * 手机号
             */
            phonenumber?: string;
            /**
             * 性别
             */
            sex?: string;
            /**
             * 状态
             */
            status?: string;
            /**
             * 部门信息
             */
            dept?: {
              /**
               * 部门ID
               */
              deptId?: number;
              /**
               * 部门名称
               */
              deptName?: string;
            };
            /**
             * 是否为管理员
             */
            admin?: boolean;
          }>;
        }> & {
          params: {
            /**
             * 页码
             */
            pageNum: number;
            /**
             * 每页条数
             */
            pageSize: number;
            /**
             * 用户名称
             */
            userName?: string;
            /**
             * 手机号
             */
            phonenumber?: string;
            /**
             * 状态
             */
            status?: string;
            /**
             * 部门ID
             */
            deptId?: number;
            /**
             * 创建时间-开始
             */
            'params[beginTime]'?: string;
            /**
             * 创建时间-结束
             */
            'params[endTime]'?: string;
          };
        }
      >(
        config: Config
      ): Alova2Method<
        {
          /**
           * 状态码
           */
          code?: number;
          /**
           * 返回消息
           */
          msg?: string;
          /**
           * 总记录数
           */
          total?: number;
          /**
           * 用户列表数据
           */
          rows?: Array<{
            /**
             * 用户ID
             */
            userId?: number;
            /**
             * 创建时间
             */
            createTime?: string;
            /**
             * 备注
             */
            remark?: string;
            /**
             * 用户名
             */
            userName?: string;
            /**
             * 昵称
             */
            nickName?: string;
            /**
             * 邮箱
             */
            email?: string;
            /**
             * 手机号
             */
            phonenumber?: string;
            /**
             * 性别
             */
            sex?: string;
            /**
             * 状态
             */
            status?: string;
            /**
             * 部门信息
             */
            dept?: {
              /**
               * 部门ID
               */
              deptId?: number;
              /**
               * 部门名称
               */
              deptName?: string;
            };
            /**
             * 是否为管理员
             */
            admin?: boolean;
          }>;
        },
        'general.get_system_user_list',
        Config
      >;
      /**
       * ---
       *
       * [GET] 用户列表
       *
       * **path:** /system/user/list
       *
       * ---
       *
       * **Query Parameters**
       * ```ts
       * type QueryParameters = {
       *   // 页码
       *   pageNum: number
       *   // 每页条数
       *   pageSize: number
       *   // 用户名称
       *   userName?: string
       *   // 手机号
       *   phonenumber?: string
       *   // 状态
       *   status?: string
       *   // 部门ID
       *   deptId?: number
       *   // 创建时间-开始
       *   'params[beginTime]'?: string
       *   // 创建时间-结束
       *   'params[endTime]'?: string
       * }
       * ```
       *
       * ---
       *
       * **Response**
       * ```ts
       * type Response = {
       *   // 状态码
       *   code?: number
       *   // 返回消息
       *   msg?: string
       *   // 总记录数
       *   total?: number
       *   // 用户列表数据
       *   // [items] start
       *   // [items] end
       *   rows?: Array<{
       *     // 用户ID
       *     userId?: number
       *     // 创建时间
       *     createTime?: string
       *     // 备注
       *     remark?: string
       *     // 用户名
       *     userName?: string
       *     // 昵称
       *     nickName?: string
       *     // 邮箱
       *     email?: string
       *     // 手机号
       *     phonenumber?: string
       *     // 性别
       *     sex?: string
       *     // 状态
       *     status?: string
       *     // 部门信息
       *     dept?: {
       *       // 部门ID
       *       deptId?: number
       *       // 部门名称
       *       deptName?: string
       *     }
       *     // 是否为管理员
       *     admin?: boolean
       *   }>
       * }
       * ```
       */
      get_system_user_list<
        Config extends Alova2MethodConfig<{
          /**
           * 状态码
           */
          code?: number;
          /**
           * 返回消息
           */
          msg?: string;
          /**
           * 总记录数
           */
          total?: number;
          /**
           * 用户列表数据
           */
          rows?: Array<{
            /**
             * 用户ID
             */
            userId?: number;
            /**
             * 创建时间
             */
            createTime?: string;
            /**
             * 备注
             */
            remark?: string;
            /**
             * 用户名
             */
            userName?: string;
            /**
             * 昵称
             */
            nickName?: string;
            /**
             * 邮箱
             */
            email?: string;
            /**
             * 手机号
             */
            phonenumber?: string;
            /**
             * 性别
             */
            sex?: string;
            /**
             * 状态
             */
            status?: string;
            /**
             * 部门信息
             */
            dept?: {
              /**
               * 部门ID
               */
              deptId?: number;
              /**
               * 部门名称
               */
              deptName?: string;
            };
            /**
             * 是否为管理员
             */
            admin?: boolean;
          }>;
        }> & {
          params: {
            /**
             * 页码
             */
            pageNum: number;
            /**
             * 每页条数
             */
            pageSize: number;
            /**
             * 用户名称
             */
            userName?: string;
            /**
             * 手机号
             */
            phonenumber?: string;
            /**
             * 状态
             */
            status?: string;
            /**
             * 部门ID
             */
            deptId?: number;
            /**
             * 创建时间-开始
             */
            'params[beginTime]'?: string;
            /**
             * 创建时间-结束
             */
            'params[endTime]'?: string;
          };
        }
      >(
        config: Config
      ): Alova2Method<
        {
          /**
           * 状态码
           */
          code?: number;
          /**
           * 返回消息
           */
          msg?: string;
          /**
           * 总记录数
           */
          total?: number;
          /**
           * 用户列表数据
           */
          rows?: Array<{
            /**
             * 用户ID
             */
            userId?: number;
            /**
             * 创建时间
             */
            createTime?: string;
            /**
             * 备注
             */
            remark?: string;
            /**
             * 用户名
             */
            userName?: string;
            /**
             * 昵称
             */
            nickName?: string;
            /**
             * 邮箱
             */
            email?: string;
            /**
             * 手机号
             */
            phonenumber?: string;
            /**
             * 性别
             */
            sex?: string;
            /**
             * 状态
             */
            status?: string;
            /**
             * 部门信息
             */
            dept?: {
              /**
               * 部门ID
               */
              deptId?: number;
              /**
               * 部门名称
               */
              deptName?: string;
            };
            /**
             * 是否为管理员
             */
            admin?: boolean;
          }>;
        },
        'general.get_system_user_list',
        Config
      >;
      /**
       * ---
       *
       * [POST] 新增用户
       *
       * **path:** /system/user
       *
       * ---
       *
       * **RequestBody**
       * ```ts
       * type RequestBody = {
       *   // 昵称
       *   nickName: string
       *   // 归属部门ID
       *   deptId?: number
       *   // 账号
       *   userName: string
       *   // 密码
       *   password: string
       *   // 手机号
       *   phonenumber?: string
       *   // 邮箱
       *   email?: string
       *   // 性别
       *   sex?: string
       *   // 状态
       *   status: string
       *   // 岗位ID数组
       *   // [items] start
       *   // [items] end
       *   postIds?: number[]
       *   // 角色ID数组
       *   // [items] start
       *   // [items] end
       *   roleIds?: number[]
       *   // 备注
       *   remark?: string
       * }
       * ```
       *
       * ---
       *
       * **Response**
       * ```ts
       * type Response = {
       *   // 状态码
       *   code?: number
       *   // 返回消息
       *   msg?: string
       * }
       * ```
       */
      post_system_user<
        Config extends Alova2MethodConfig<{
          /**
           * 状态码
           */
          code?: number;
          /**
           * 返回消息
           */
          msg?: string;
        }> & {
          data: {
            /**
             * 昵称
             */
            nickName: string;
            /**
             * 归属部门ID
             */
            deptId?: number;
            /**
             * 账号
             */
            userName: string;
            /**
             * 密码
             */
            password: string;
            /**
             * 手机号
             */
            phonenumber?: string;
            /**
             * 邮箱
             */
            email?: string;
            /**
             * 性别
             */
            sex?: string;
            /**
             * 状态
             */
            status: string;
            /**
             * 岗位ID数组
             */
            postIds?: number[];
            /**
             * 角色ID数组
             */
            roleIds?: number[];
            /**
             * 备注
             */
            remark?: string;
          };
        }
      >(
        config: Config
      ): Alova2Method<
        {
          /**
           * 状态码
           */
          code?: number;
          /**
           * 返回消息
           */
          msg?: string;
        },
        'general.post_system_user',
        Config
      >;
      /**
       * ---
       *
       * [POST] 新增用户
       *
       * **path:** /system/user
       *
       * ---
       *
       * **RequestBody**
       * ```ts
       * type RequestBody = {
       *   // 昵称
       *   nickName: string
       *   // 归属部门ID
       *   deptId?: number
       *   // 账号
       *   userName: string
       *   // 密码
       *   password: string
       *   // 手机号
       *   phonenumber?: string
       *   // 邮箱
       *   email?: string
       *   // 性别
       *   sex?: string
       *   // 状态
       *   status: string
       *   // 岗位ID数组
       *   // [items] start
       *   // [items] end
       *   postIds?: number[]
       *   // 角色ID数组
       *   // [items] start
       *   // [items] end
       *   roleIds?: number[]
       *   // 备注
       *   remark?: string
       * }
       * ```
       *
       * ---
       *
       * **Response**
       * ```ts
       * type Response = {
       *   // 状态码
       *   code?: number
       *   // 返回消息
       *   msg?: string
       * }
       * ```
       */
      post_system_user<
        Config extends Alova2MethodConfig<{
          /**
           * 状态码
           */
          code?: number;
          /**
           * 返回消息
           */
          msg?: string;
        }> & {
          data: {
            /**
             * 昵称
             */
            nickName: string;
            /**
             * 归属部门ID
             */
            deptId?: number;
            /**
             * 账号
             */
            userName: string;
            /**
             * 密码
             */
            password: string;
            /**
             * 手机号
             */
            phonenumber?: string;
            /**
             * 邮箱
             */
            email?: string;
            /**
             * 性别
             */
            sex?: string;
            /**
             * 状态
             */
            status: string;
            /**
             * 岗位ID数组
             */
            postIds?: number[];
            /**
             * 角色ID数组
             */
            roleIds?: number[];
            /**
             * 备注
             */
            remark?: string;
          };
        }
      >(
        config: Config
      ): Alova2Method<
        {
          /**
           * 状态码
           */
          code?: number;
          /**
           * 返回消息
           */
          msg?: string;
        },
        'general.post_system_user',
        Config
      >;
      /**
       * ---
       *
       * [PUT] 修改用户
       *
       * **path:** /system/user
       *
       * ---
       *
       * **RequestBody**
       * ```ts
       * type RequestBody = {
       *   // 用户ID
       *   userId: number
       *   // 昵称
       *   nickName: string
       *   // 归属部门ID
       *   deptId: number
       *   // 用户名
       *   userName?: string
       *   // 手机号
       *   phonenumber?: string
       *   // 邮箱
       *   email?: string
       *   // 性别
       *   sex?: string
       *   // 状态
       *   status?: string
       *   // 岗位ID数组
       *   // [items] start
       *   // [items] end
       *   postIds?: number[]
       *   // 角色ID数组
       *   // [items] start
       *   // [items] end
       *   roleIds?: number[]
       *   // 备注
       *   remark?: string
       * }
       * ```
       *
       * ---
       *
       * **Response**
       * ```ts
       * type Response = {
       *   // 状态码
       *   code?: number
       *   // 返回消息
       *   msg?: string
       * }
       * ```
       */
      put_system_user<
        Config extends Alova2MethodConfig<{
          /**
           * 状态码
           */
          code?: number;
          /**
           * 返回消息
           */
          msg?: string;
        }> & {
          data: {
            /**
             * 用户ID
             */
            userId: number;
            /**
             * 昵称
             */
            nickName: string;
            /**
             * 归属部门ID
             */
            deptId: number;
            /**
             * 用户名
             */
            userName?: string;
            /**
             * 手机号
             */
            phonenumber?: string;
            /**
             * 邮箱
             */
            email?: string;
            /**
             * 性别
             */
            sex?: string;
            /**
             * 状态
             */
            status?: string;
            /**
             * 岗位ID数组
             */
            postIds?: number[];
            /**
             * 角色ID数组
             */
            roleIds?: number[];
            /**
             * 备注
             */
            remark?: string;
          };
        }
      >(
        config: Config
      ): Alova2Method<
        {
          /**
           * 状态码
           */
          code?: number;
          /**
           * 返回消息
           */
          msg?: string;
        },
        'general.put_system_user',
        Config
      >;
      /**
       * ---
       *
       * [PUT] 修改用户
       *
       * **path:** /system/user
       *
       * ---
       *
       * **RequestBody**
       * ```ts
       * type RequestBody = {
       *   // 用户ID
       *   userId: number
       *   // 昵称
       *   nickName: string
       *   // 归属部门ID
       *   deptId: number
       *   // 用户名
       *   userName?: string
       *   // 手机号
       *   phonenumber?: string
       *   // 邮箱
       *   email?: string
       *   // 性别
       *   sex?: string
       *   // 状态
       *   status?: string
       *   // 岗位ID数组
       *   // [items] start
       *   // [items] end
       *   postIds?: number[]
       *   // 角色ID数组
       *   // [items] start
       *   // [items] end
       *   roleIds?: number[]
       *   // 备注
       *   remark?: string
       * }
       * ```
       *
       * ---
       *
       * **Response**
       * ```ts
       * type Response = {
       *   // 状态码
       *   code?: number
       *   // 返回消息
       *   msg?: string
       * }
       * ```
       */
      put_system_user<
        Config extends Alova2MethodConfig<{
          /**
           * 状态码
           */
          code?: number;
          /**
           * 返回消息
           */
          msg?: string;
        }> & {
          data: {
            /**
             * 用户ID
             */
            userId: number;
            /**
             * 昵称
             */
            nickName: string;
            /**
             * 归属部门ID
             */
            deptId: number;
            /**
             * 用户名
             */
            userName?: string;
            /**
             * 手机号
             */
            phonenumber?: string;
            /**
             * 邮箱
             */
            email?: string;
            /**
             * 性别
             */
            sex?: string;
            /**
             * 状态
             */
            status?: string;
            /**
             * 岗位ID数组
             */
            postIds?: number[];
            /**
             * 角色ID数组
             */
            roleIds?: number[];
            /**
             * 备注
             */
            remark?: string;
          };
        }
      >(
        config: Config
      ): Alova2Method<
        {
          /**
           * 状态码
           */
          code?: number;
          /**
           * 返回消息
           */
          msg?: string;
        },
        'general.put_system_user',
        Config
      >;
      /**
       * ---
       *
       * [GET] 用户详情
       *
       * **path:** /system/user/{userId}
       *
       * ---
       *
       * **Path Parameters**
       * ```ts
       * type PathParameters = {
       *   // 用户ID
       *   userId: number
       * }
       * ```
       *
       * ---
       *
       * **Response**
       * ```ts
       * type Response = {
       *   // 状态码
       *   code?: number
       *   // 返回消息
       *   msg?: string
       *   // 用户详情数据
       *   data?: {
       *     // 备注
       *     remark?: string
       *     // 用户ID
       *     userId?: number
       *     // 部门ID
       *     deptId?: number
       *     // 账号
       *     userName?: string
       *     // 昵称
       *     nickName?: string
       *     // 邮箱
       *     email?: string
       *     // 手机号
       *     phonenumber?: string
       *     // 性别
       *     sex?: string
       *     // 头像
       *     avatar?: string
       *     // 密码
       *     password?: string
       *     // 状态
       *     status?: string
       *     // 部门信息
       *     dept?: {
       *       // 部门ID
       *       deptId?: number
       *       // 部门名称
       *       deptName?: string
       *     }
       *     // 是否为管理员
       *     admin?: boolean
       *   }
       *   // 岗位ID数组
       *   // [items] start
       *   // [items] end
       *   postIds?: number[]
       *   // 可选岗位数据
       *   // [items] start
       *   // [items] end
       *   posts?: Array<{
       *     // 岗位ID
       *     postId?: number
       *     // 岗位名称
       *     postName?: string
       *     // 岗位状态
       *     status?: string
       *   }>
       *   // 角色ID数组
       *   // [items] start
       *   // [items] end
       *   roleIds?: number[]
       *   // 可选角色数据
       *   // [items] start
       *   // [items] end
       *   roles?: Array<{
       *     // 角色ID
       *     roleId?: number
       *     // 角色名称
       *     roleName?: string
       *     // 角色状态
       *     status?: string
       *   }>
       * }
       * ```
       */
      get_system_user_userid<
        Config extends Alova2MethodConfig<{
          /**
           * 状态码
           */
          code?: number;
          /**
           * 返回消息
           */
          msg?: string;
          /**
           * 用户详情数据
           */
          data?: {
            /**
             * 备注
             */
            remark?: string;
            /**
             * 用户ID
             */
            userId?: number;
            /**
             * 部门ID
             */
            deptId?: number;
            /**
             * 账号
             */
            userName?: string;
            /**
             * 昵称
             */
            nickName?: string;
            /**
             * 邮箱
             */
            email?: string;
            /**
             * 手机号
             */
            phonenumber?: string;
            /**
             * 性别
             */
            sex?: string;
            /**
             * 头像
             */
            avatar?: string;
            /**
             * 密码
             */
            password?: string;
            /**
             * 状态
             */
            status?: string;
            /**
             * 部门信息
             */
            dept?: {
              /**
               * 部门ID
               */
              deptId?: number;
              /**
               * 部门名称
               */
              deptName?: string;
            };
            /**
             * 是否为管理员
             */
            admin?: boolean;
          };
          /**
           * 岗位ID数组
           */
          postIds?: number[];
          /**
           * 可选岗位数据
           */
          posts?: Array<{
            /**
             * 岗位ID
             */
            postId?: number;
            /**
             * 岗位名称
             */
            postName?: string;
            /**
             * 岗位状态
             */
            status?: string;
          }>;
          /**
           * 角色ID数组
           */
          roleIds?: number[];
          /**
           * 可选角色数据
           */
          roles?: Array<{
            /**
             * 角色ID
             */
            roleId?: number;
            /**
             * 角色名称
             */
            roleName?: string;
            /**
             * 角色状态
             */
            status?: string;
          }>;
        }> & {
          pathParams: {
            /**
             * 用户ID
             */
            userId: number;
          };
        }
      >(
        config: Config
      ): Alova2Method<
        {
          /**
           * 状态码
           */
          code?: number;
          /**
           * 返回消息
           */
          msg?: string;
          /**
           * 用户详情数据
           */
          data?: {
            /**
             * 备注
             */
            remark?: string;
            /**
             * 用户ID
             */
            userId?: number;
            /**
             * 部门ID
             */
            deptId?: number;
            /**
             * 账号
             */
            userName?: string;
            /**
             * 昵称
             */
            nickName?: string;
            /**
             * 邮箱
             */
            email?: string;
            /**
             * 手机号
             */
            phonenumber?: string;
            /**
             * 性别
             */
            sex?: string;
            /**
             * 头像
             */
            avatar?: string;
            /**
             * 密码
             */
            password?: string;
            /**
             * 状态
             */
            status?: string;
            /**
             * 部门信息
             */
            dept?: {
              /**
               * 部门ID
               */
              deptId?: number;
              /**
               * 部门名称
               */
              deptName?: string;
            };
            /**
             * 是否为管理员
             */
            admin?: boolean;
          };
          /**
           * 岗位ID数组
           */
          postIds?: number[];
          /**
           * 可选岗位数据
           */
          posts?: Array<{
            /**
             * 岗位ID
             */
            postId?: number;
            /**
             * 岗位名称
             */
            postName?: string;
            /**
             * 岗位状态
             */
            status?: string;
          }>;
          /**
           * 角色ID数组
           */
          roleIds?: number[];
          /**
           * 可选角色数据
           */
          roles?: Array<{
            /**
             * 角色ID
             */
            roleId?: number;
            /**
             * 角色名称
             */
            roleName?: string;
            /**
             * 角色状态
             */
            status?: string;
          }>;
        },
        'general.get_system_user_userid',
        Config
      >;
      /**
       * ---
       *
       * [GET] 用户详情
       *
       * **path:** /system/user/{userId}
       *
       * ---
       *
       * **Path Parameters**
       * ```ts
       * type PathParameters = {
       *   // 用户ID
       *   userId: number
       * }
       * ```
       *
       * ---
       *
       * **Response**
       * ```ts
       * type Response = {
       *   // 状态码
       *   code?: number
       *   // 返回消息
       *   msg?: string
       *   // 用户详情数据
       *   data?: {
       *     // 备注
       *     remark?: string
       *     // 用户ID
       *     userId?: number
       *     // 部门ID
       *     deptId?: number
       *     // 账号
       *     userName?: string
       *     // 昵称
       *     nickName?: string
       *     // 邮箱
       *     email?: string
       *     // 手机号
       *     phonenumber?: string
       *     // 性别
       *     sex?: string
       *     // 头像
       *     avatar?: string
       *     // 密码
       *     password?: string
       *     // 状态
       *     status?: string
       *     // 部门信息
       *     dept?: {
       *       // 部门ID
       *       deptId?: number
       *       // 部门名称
       *       deptName?: string
       *     }
       *     // 是否为管理员
       *     admin?: boolean
       *   }
       *   // 岗位ID数组
       *   // [items] start
       *   // [items] end
       *   postIds?: number[]
       *   // 可选岗位数据
       *   // [items] start
       *   // [items] end
       *   posts?: Array<{
       *     // 岗位ID
       *     postId?: number
       *     // 岗位名称
       *     postName?: string
       *     // 岗位状态
       *     status?: string
       *   }>
       *   // 角色ID数组
       *   // [items] start
       *   // [items] end
       *   roleIds?: number[]
       *   // 可选角色数据
       *   // [items] start
       *   // [items] end
       *   roles?: Array<{
       *     // 角色ID
       *     roleId?: number
       *     // 角色名称
       *     roleName?: string
       *     // 角色状态
       *     status?: string
       *   }>
       * }
       * ```
       */
      get_system_user_userid<
        Config extends Alova2MethodConfig<{
          /**
           * 状态码
           */
          code?: number;
          /**
           * 返回消息
           */
          msg?: string;
          /**
           * 用户详情数据
           */
          data?: {
            /**
             * 备注
             */
            remark?: string;
            /**
             * 用户ID
             */
            userId?: number;
            /**
             * 部门ID
             */
            deptId?: number;
            /**
             * 账号
             */
            userName?: string;
            /**
             * 昵称
             */
            nickName?: string;
            /**
             * 邮箱
             */
            email?: string;
            /**
             * 手机号
             */
            phonenumber?: string;
            /**
             * 性别
             */
            sex?: string;
            /**
             * 头像
             */
            avatar?: string;
            /**
             * 密码
             */
            password?: string;
            /**
             * 状态
             */
            status?: string;
            /**
             * 部门信息
             */
            dept?: {
              /**
               * 部门ID
               */
              deptId?: number;
              /**
               * 部门名称
               */
              deptName?: string;
            };
            /**
             * 是否为管理员
             */
            admin?: boolean;
          };
          /**
           * 岗位ID数组
           */
          postIds?: number[];
          /**
           * 可选岗位数据
           */
          posts?: Array<{
            /**
             * 岗位ID
             */
            postId?: number;
            /**
             * 岗位名称
             */
            postName?: string;
            /**
             * 岗位状态
             */
            status?: string;
          }>;
          /**
           * 角色ID数组
           */
          roleIds?: number[];
          /**
           * 可选角色数据
           */
          roles?: Array<{
            /**
             * 角色ID
             */
            roleId?: number;
            /**
             * 角色名称
             */
            roleName?: string;
            /**
             * 角色状态
             */
            status?: string;
          }>;
        }> & {
          pathParams: {
            /**
             * 用户ID
             */
            userId: number;
          };
        }
      >(
        config: Config
      ): Alova2Method<
        {
          /**
           * 状态码
           */
          code?: number;
          /**
           * 返回消息
           */
          msg?: string;
          /**
           * 用户详情数据
           */
          data?: {
            /**
             * 备注
             */
            remark?: string;
            /**
             * 用户ID
             */
            userId?: number;
            /**
             * 部门ID
             */
            deptId?: number;
            /**
             * 账号
             */
            userName?: string;
            /**
             * 昵称
             */
            nickName?: string;
            /**
             * 邮箱
             */
            email?: string;
            /**
             * 手机号
             */
            phonenumber?: string;
            /**
             * 性别
             */
            sex?: string;
            /**
             * 头像
             */
            avatar?: string;
            /**
             * 密码
             */
            password?: string;
            /**
             * 状态
             */
            status?: string;
            /**
             * 部门信息
             */
            dept?: {
              /**
               * 部门ID
               */
              deptId?: number;
              /**
               * 部门名称
               */
              deptName?: string;
            };
            /**
             * 是否为管理员
             */
            admin?: boolean;
          };
          /**
           * 岗位ID数组
           */
          postIds?: number[];
          /**
           * 可选岗位数据
           */
          posts?: Array<{
            /**
             * 岗位ID
             */
            postId?: number;
            /**
             * 岗位名称
             */
            postName?: string;
            /**
             * 岗位状态
             */
            status?: string;
          }>;
          /**
           * 角色ID数组
           */
          roleIds?: number[];
          /**
           * 可选角色数据
           */
          roles?: Array<{
            /**
             * 角色ID
             */
            roleId?: number;
            /**
             * 角色名称
             */
            roleName?: string;
            /**
             * 角色状态
             */
            status?: string;
          }>;
        },
        'general.get_system_user_userid',
        Config
      >;
      /**
       * ---
       *
       * [DELETE] 删除用户
       *
       * **path:** /system/user/{userId}
       *
       * ---
       *
       * **Path Parameters**
       * ```ts
       * type PathParameters = {
       *   // 用户ID
       *   userId: number
       * }
       * ```
       *
       * ---
       *
       * **Response**
       * ```ts
       * type Response = {
       *   // 状态码
       *   code?: number
       *   // 返回消息
       *   msg?: string
       * }
       * ```
       */
      delete_system_user_userid<
        Config extends Alova2MethodConfig<{
          /**
           * 状态码
           */
          code?: number;
          /**
           * 返回消息
           */
          msg?: string;
        }> & {
          pathParams: {
            /**
             * 用户ID
             */
            userId: number;
          };
        }
      >(
        config: Config
      ): Alova2Method<
        {
          /**
           * 状态码
           */
          code?: number;
          /**
           * 返回消息
           */
          msg?: string;
        },
        'general.delete_system_user_userid',
        Config
      >;
      /**
       * ---
       *
       * [DELETE] 删除用户
       *
       * **path:** /system/user/{userId}
       *
       * ---
       *
       * **Path Parameters**
       * ```ts
       * type PathParameters = {
       *   // 用户ID
       *   userId: number
       * }
       * ```
       *
       * ---
       *
       * **Response**
       * ```ts
       * type Response = {
       *   // 状态码
       *   code?: number
       *   // 返回消息
       *   msg?: string
       * }
       * ```
       */
      delete_system_user_userid<
        Config extends Alova2MethodConfig<{
          /**
           * 状态码
           */
          code?: number;
          /**
           * 返回消息
           */
          msg?: string;
        }> & {
          pathParams: {
            /**
             * 用户ID
             */
            userId: number;
          };
        }
      >(
        config: Config
      ): Alova2Method<
        {
          /**
           * 状态码
           */
          code?: number;
          /**
           * 返回消息
           */
          msg?: string;
        },
        'general.delete_system_user_userid',
        Config
      >;
      /**
       * ---
       *
       * [GET] 获取用户初始化信息
       *
       * **path:** /system/user/
       *
       * ---
       *
       * **Response**
       * ```ts
       * type Response = {
       *   msg?: string
       *   code?: number
       *   // [items] start
       *   // [items] end
       *   roles?: Array<{
       *     // 角色ID
       *     roleId?: number
       *     // 角色名称
       *     roleName?: string
       *     // 角色权限字符串
       *     roleKey?: string
       *     // 显示顺序
       *     roleSort?: number
       *     // 数据范围（1：全部数据权限 2：自定数据权限 3：本部门数据权限 4：本部门及以下数据权限）
       *     dataScope?: string
       *     // 菜单树选择项是否关联显示
       *     menuCheckStrictly?: boolean
       *     // 部门树选择项是否关联显示
       *     deptCheckStrictly?: boolean
       *     // 角色状态（0正常 1停用）
       *     status?: string
       *     // 删除标志（0代表存在 2代表删除）
       *     delFlag?: string
       *     // 创建者
       *     createBy?: string
       *     // 创建时间
       *     createTime?: string
       *     // 更新者
       *     updateBy?: string
       *     // 更新时间
       *     updateTime?: string
       *     // 备注
       *     remark?: string
       *   }>
       *   // [items] start
       *   // [items] end
       *   posts?: Array<{
       *     // 岗位ID
       *     postId?: number
       *     // 岗位编码
       *     postCode?: string
       *     // 岗位名称
       *     postName?: string
       *     // 显示顺序
       *     postSort?: number
       *     // 状态（0正常 1停用）
       *     status?: string
       *     // 创建者
       *     createBy?: string
       *     // 创建时间
       *     createTime?: string
       *     // 更新者
       *     updateBy?: string
       *     // 更新时间
       *     updateTime?: string
       *     // 备注
       *     remark?: string
       *   }>
       * }
       * ```
       */
      get_system_user<
        Config extends Alova2MethodConfig<{
          msg?: string;
          code?: number;
          roles?: Role[];
          posts?: Post[];
        }>
      >(
        config?: Config
      ): Alova2Method<
        {
          msg?: string;
          code?: number;
          roles?: Role[];
          posts?: Post[];
        },
        'general.get_system_user',
        Config
      >;
      /**
       * ---
       *
       * [PUT] 用户密码重置
       *
       * **path:** /system/user/resetPwd
       *
       * ---
       *
       * **RequestBody**
       * ```ts
       * type RequestBody = {
       *   // 用户ID
       *   userId: number
       *   // 新密码
       *   password: string
       * }
       * ```
       *
       * ---
       *
       * **Response**
       * ```ts
       * type Response = {
       *   msg?: string
       *   code?: number
       * }
       * ```
       */
      put_system_user_resetpwd<
        Config extends Alova2MethodConfig<{
          msg?: string;
          code?: number;
        }> & {
          data: {
            /**
             * 用户ID
             */
            userId: number;
            /**
             * 新密码
             */
            password: string;
          };
        }
      >(
        config: Config
      ): Alova2Method<
        {
          msg?: string;
          code?: number;
        },
        'general.put_system_user_resetpwd',
        Config
      >;
      /**
       * ---
       *
       * [PUT] 用户状态修改
       *
       * **path:** /system/user/changeStatus
       *
       * ---
       *
       * **RequestBody**
       * ```ts
       * type RequestBody = {
       *   // 用户ID
       *   userId: number
       *   // 用户状态（0正常 1停用）
       *   status: string
       * }
       * ```
       *
       * ---
       *
       * **Response**
       * ```ts
       * type Response = {
       *   msg?: string
       *   code?: number
       * }
       * ```
       */
      put_system_user_changestatus<
        Config extends Alova2MethodConfig<{
          msg?: string;
          code?: number;
        }> & {
          data: {
            /**
             * 用户ID
             */
            userId: number;
            /**
             * 用户状态（0正常 1停用）
             */
            status: string;
          };
        }
      >(
        config: Config
      ): Alova2Method<
        {
          msg?: string;
          code?: number;
        },
        'general.put_system_user_changestatus',
        Config
      >;
      /**
       * ---
       *
       * [GET] 查询用户个人信息
       *
       * **path:** /system/user/profile
       *
       * ---
       *
       * **Response**
       * ```ts
       * type Response = {
       *   msg?: string
       *   code?: number
       *   data?: {
       *     // 用户ID
       *     userId?: number
       *     // 部门ID
       *     deptId?: number
       *     // 用户账号
       *     userName?: string
       *     // 用户昵称
       *     nickName?: string
       *     // 用户邮箱
       *     email?: string
       *     // 手机号码
       *     phonenumber?: string
       *     // 用户性别（0男 1女 2未知）
       *     sex?: string
       *     // 头像地址
       *     avatar?: string
       *     // 密码
       *     password?: string
       *     // 帐号状态（0正常 1停用）
       *     status?: string
       *     // 删除标志（0代表存在 2代表删除）
       *     delFlag?: string
       *     // 最后登录IP
       *     loginIp?: string
       *     // 最后登录时间
       *     loginDate?: string
       *     // 创建者
       *     createBy?: string
       *     // 创建时间
       *     createTime?: string
       *     // 更新者
       *     updateBy?: string
       *     // 更新时间
       *     updateTime?: string
       *     // 备注
       *     remark?: string
       *     dept?: {
       *       // 部门ID
       *       deptId?: number
       *       // 父部门ID
       *       parentId?: number
       *       // 祖级列表
       *       ancestors?: string
       *       // 部门名称
       *       deptName?: string
       *       // 显示顺序
       *       orderNum?: number
       *       // 负责人
       *       leader?: string
       *       // 联系电话
       *       phone?: string
       *       // 邮箱
       *       email?: string
       *       // 部门状态（0正常 1停用）
       *       status?: string
       *       // 删除标志（0代表存在 2代表删除）
       *       delFlag?: string
       *       // 创建者
       *       createBy?: string
       *       // 创建时间
       *       createTime?: string
       *       // 更新者
       *       updateBy?: string
       *       // 更新时间
       *       updateTime?: string
       *     }
       *   }
       *   // 角色组
       *   roleGroup?: string
       *   // 岗位组
       *   postGroup?: string
       * }
       * ```
       */
      get_system_user_profile<
        Config extends Alova2MethodConfig<{
          msg?: string;
          code?: number;
          data?: User;
          /**
           * 角色组
           */
          roleGroup?: string;
          /**
           * 岗位组
           */
          postGroup?: string;
        }>
      >(
        config?: Config
      ): Alova2Method<
        {
          msg?: string;
          code?: number;
          data?: User;
          /**
           * 角色组
           */
          roleGroup?: string;
          /**
           * 岗位组
           */
          postGroup?: string;
        },
        'general.get_system_user_profile',
        Config
      >;
      /**
       * ---
       *
       * [PUT] 修改用户个人信息
       *
       * **path:** /system/user/profile
       *
       * ---
       *
       * **RequestBody**
       * ```ts
       * type RequestBody = {
       *   // 用户账号
       *   userName?: string
       *   // 用户昵称
       *   nickName: string
       *   // 用户邮箱
       *   email?: string
       *   // 手机号码
       *   phonenumber?: string
       *   // 用户性别（0男 1女 2未知）
       *   sex?: string
       * }
       * ```
       *
       * ---
       *
       * **Response**
       * ```ts
       * type Response = {
       *   msg?: string
       *   code?: number
       * }
       * ```
       */
      put_system_user_profile<
        Config extends Alova2MethodConfig<{
          msg?: string;
          code?: number;
        }> & {
          data: UserProfileUpdate;
        }
      >(
        config: Config
      ): Alova2Method<
        {
          msg?: string;
          code?: number;
        },
        'general.put_system_user_profile',
        Config
      >;
      /**
       * ---
       *
       * [GET] 查询授权角色
       *
       * **path:** /system/user/authRole/{userId}
       *
       * ---
       *
       * **Path Parameters**
       * ```ts
       * type PathParameters = {
       *   // 用户ID
       *   userId: number
       * }
       * ```
       *
       * ---
       *
       * **Response**
       * ```ts
       * type Response = {
       *   msg?: string
       *   code?: number
       *   user?: {
       *     // 用户ID
       *     userId?: number
       *     // 部门ID
       *     deptId?: number
       *     // 用户账号
       *     userName?: string
       *     // 用户昵称
       *     nickName?: string
       *     // 用户邮箱
       *     email?: string
       *     // 手机号码
       *     phonenumber?: string
       *     // 用户性别（0男 1女 2未知）
       *     sex?: string
       *     // 头像地址
       *     avatar?: string
       *     // 密码
       *     password?: string
       *     // 帐号状态（0正常 1停用）
       *     status?: string
       *     // 删除标志（0代表存在 2代表删除）
       *     delFlag?: string
       *     // 最后登录IP
       *     loginIp?: string
       *     // 最后登录时间
       *     loginDate?: string
       *     // 创建者
       *     createBy?: string
       *     // 创建时间
       *     createTime?: string
       *     // 更新者
       *     updateBy?: string
       *     // 更新时间
       *     updateTime?: string
       *     // 备注
       *     remark?: string
       *     dept?: {
       *       // 部门ID
       *       deptId?: number
       *       // 父部门ID
       *       parentId?: number
       *       // 祖级列表
       *       ancestors?: string
       *       // 部门名称
       *       deptName?: string
       *       // 显示顺序
       *       orderNum?: number
       *       // 负责人
       *       leader?: string
       *       // 联系电话
       *       phone?: string
       *       // 邮箱
       *       email?: string
       *       // 部门状态（0正常 1停用）
       *       status?: string
       *       // 删除标志（0代表存在 2代表删除）
       *       delFlag?: string
       *       // 创建者
       *       createBy?: string
       *       // 创建时间
       *       createTime?: string
       *       // 更新者
       *       updateBy?: string
       *       // 更新时间
       *       updateTime?: string
       *     }
       *   }
       *   // [items] start
       *   // [items] end
       *   roles?: Array<{
       *     // 角色ID
       *     roleId?: number
       *     // 角色名称
       *     roleName?: string
       *     // 角色权限字符串
       *     roleKey?: string
       *     // 显示顺序
       *     roleSort?: number
       *     // 数据范围（1：全部数据权限 2：自定数据权限 3：本部门数据权限 4：本部门及以下数据权限）
       *     dataScope?: string
       *     // 菜单树选择项是否关联显示
       *     menuCheckStrictly?: boolean
       *     // 部门树选择项是否关联显示
       *     deptCheckStrictly?: boolean
       *     // 角色状态（0正常 1停用）
       *     status?: string
       *     // 删除标志（0代表存在 2代表删除）
       *     delFlag?: string
       *     // 创建者
       *     createBy?: string
       *     // 创建时间
       *     createTime?: string
       *     // 更新者
       *     updateBy?: string
       *     // 更新时间
       *     updateTime?: string
       *     // 备注
       *     remark?: string
       *   }>
       * }
       * ```
       */
      get_system_user_authrole_userid<
        Config extends Alova2MethodConfig<{
          msg?: string;
          code?: number;
          user?: User;
          roles?: Role[];
        }> & {
          pathParams: {
            /**
             * 用户ID
             */
            userId: number;
          };
        }
      >(
        config: Config
      ): Alova2Method<
        {
          msg?: string;
          code?: number;
          user?: User;
          roles?: Role[];
        },
        'general.get_system_user_authrole_userid',
        Config
      >;
      /**
       * ---
       *
       * [POST] 导入用户数据
       *
       * **path:** /system/user/importData
       *
       * ---
       *
       * **Query Parameters**
       * ```ts
       * type QueryParameters = {
       *   // 是否更新已存在的用户数据
       *   updateSupport?: number
       * }
       * ```
       *
       * ---
       *
       * **RequestBody**
       * ```ts
       * type RequestBody = {
       *   // 导入的Excel文件
       *   file: Blob
       * }
       * ```
       *
       * ---
       *
       * **Response**
       * ```ts
       * type Response = {
       *   msg?: string
       *   code?: number
       * }
       * ```
       */
      post_system_user_importdata<
        Config extends Alova2MethodConfig<{
          msg?: string;
          code?: number;
        }> & {
          params: {
            /**
             * 是否更新已存在的用户数据
             */
            updateSupport?: number;
          };
          data: {
            /**
             * 导入的Excel文件
             */
            file: Blob;
          };
        }
      >(
        config: Config
      ): Alova2Method<
        {
          msg?: string;
          code?: number;
        },
        'general.post_system_user_importdata',
        Config
      >;
      /**
       * ---
       *
       * [GET] 组织机构树
       *
       * **path:** /system/user/deptTree
       *
       * ---
       *
       * **Response**
       * ```ts
       * type Response = {
       *   // 状态码
       *   code?: number
       *   // 返回消息
       *   msg?: string
       *   // 组织机构树数据
       *   // [items] start
       *   // [items] end
       *   data?: Array<{
       *     // 部门ID
       *     id: number
       *     // 部门名称
       *     label: string
       *     // 子部门
       *     // [items] start
       *     // [cycle] $.data.[]
       *     // [items] end
       *     children?: DeptTreeNode[]
       *   }>
       * }
       * ```
       */
      get_system_user_depttree<
        Config extends Alova2MethodConfig<{
          /**
           * 状态码
           */
          code?: number;
          /**
           * 返回消息
           */
          msg?: string;
          /**
           * 组织机构树数据
           */
          data?: DeptTreeNode[];
        }>
      >(
        config?: Config
      ): Alova2Method<
        {
          /**
           * 状态码
           */
          code?: number;
          /**
           * 返回消息
           */
          msg?: string;
          /**
           * 组织机构树数据
           */
          data?: DeptTreeNode[];
        },
        'general.get_system_user_depttree',
        Config
      >;
      /**
       * ---
       *
       * [GET] 获取字典
       *
       * **path:** /system/dict/data/type/{dictType}
       *
       * ---
       *
       * **Path Parameters**
       * ```ts
       * type PathParameters = {
       *   // 字典类型
       *   dictType: string
       * }
       * ```
       *
       * ---
       *
       * **Response**
       * ```ts
       * type Response = {
       *   // 状态码
       *   code?: number
       *   // 返回消息
       *   msg?: string
       *   // 字典数据列表
       *   // [items] start
       *   // [items] end
       *   data?: Array<{
       *     // 字典排序
       *     dictSort?: number
       *     // 字典标签
       *     dictLabel?: string
       *     // 字典键值
       *     dictValue?: string
       *     // 字典类型
       *     dictType?: string
       *     // 样式属性
       *     cssClass?: string
       *     // 表格回显样式
       *     listClass?: string
       *     // 状态
       *     status?: string
       *   }>
       * }
       * ```
       */
      get_system_dict_data_type_dicttype<
        Config extends Alova2MethodConfig<{
          /**
           * 状态码
           */
          code?: number;
          /**
           * 返回消息
           */
          msg?: string;
          /**
           * 字典数据列表
           */
          data?: Array<{
            /**
             * 字典排序
             */
            dictSort?: number;
            /**
             * 字典标签
             */
            dictLabel?: string;
            /**
             * 字典键值
             */
            dictValue?: string;
            /**
             * 字典类型
             */
            dictType?: string;
            /**
             * 样式属性
             */
            cssClass?: string;
            /**
             * 表格回显样式
             */
            listClass?: string;
            /**
             * 状态
             */
            status?: string;
          }>;
        }> & {
          pathParams: {
            /**
             * 字典类型
             */
            dictType: string;
          };
        }
      >(
        config: Config
      ): Alova2Method<
        {
          /**
           * 状态码
           */
          code?: number;
          /**
           * 返回消息
           */
          msg?: string;
          /**
           * 字典数据列表
           */
          data?: Array<{
            /**
             * 字典排序
             */
            dictSort?: number;
            /**
             * 字典标签
             */
            dictLabel?: string;
            /**
             * 字典键值
             */
            dictValue?: string;
            /**
             * 字典类型
             */
            dictType?: string;
            /**
             * 样式属性
             */
            cssClass?: string;
            /**
             * 表格回显样式
             */
            listClass?: string;
            /**
             * 状态
             */
            status?: string;
          }>;
        },
        'general.get_system_dict_data_type_dicttype',
        Config
      >;
      /**
       * ---
       *
       * [POST] 免费模特列表查询（分页）
       *
       * **path:** /aiVatarApi/robotList
       *
       * ---
       *
       * **Query Parameters**
       * ```ts
       * type QueryParameters = {
       *   page?: number
       *   size?: number
       * }
       * ```
       *
       * ---
       *
       * **Response**
       * ```ts
       * type Response = {
       *   total: number
       *   // [items] start
       *   // [items] end
       *   rows: Array<{
       *     id: number
       *     robotName: string
       *     robotDesc: string
       *     coverUrl: string
       *     coverMattingUrl: null
       *     configJson: null
       *     gender: number
       *     age: number
       *     starSigns: number
       *     face: number | null
       *     // [items] start
       *     // [items] end
       *     sceneList: Array<{
       *       id: number
       *       sceneName: string
       *       sceneCode: string
       *       baseCode: null
       *       exampleUrl: string | null
       *       coverUrl: string
       *       coverMattingUrl: null
       *       samplePictureUrl: string
       *       proportion: string
       *       resolution: string
       *       robotId: number
       *       sceneType: number
       *       posture: string
       *       applicationType: string
       *       noTrainFlag: number
       *       duration: number
       *     }>
       *     version: number
       *     scene: null
       *     type: null
       *     expireTime: null
       *     popularity: number
       *     speakerId: string
       *     ttsSpeaker: null
       *     // [title] 标签列表
       *     // 标签列表
       *     // [params1] start
       *     // [items] start
       *     // [items] end
       *     // [params1] end
       *     labelBaseDTOList: Array<{
       *       // [title] 标签名
       *       // 标签名
       *       labelName: string
       *     }> | null
       *   }>
       *   code: number
       *   msg: string
       * }
       * ```
       */
      post_aivatarapi_robotlist<
        Config extends Alova2MethodConfig<{
          total: number;
          rows: Array<{
            id: number;
            robotName: string;
            robotDesc: string;
            coverUrl: string;
            coverMattingUrl: null;
            configJson: null;
            gender: number;
            age: number;
            starSigns: number;
            face: number | null;
            sceneList: Array<{
              id: number;
              sceneName: string;
              sceneCode: string;
              baseCode: null;
              exampleUrl: string | null;
              coverUrl: string;
              coverMattingUrl: null;
              samplePictureUrl: string;
              proportion: string;
              resolution: string;
              robotId: number;
              sceneType: number;
              posture: string;
              applicationType: string;
              noTrainFlag: number;
              duration: number;
            }>;
            version: number;
            scene: null;
            type: null;
            expireTime: null;
            popularity: number;
            speakerId: string;
            ttsSpeaker: null;
            /**
             * 标签列表
             * ---
             * 标签列表
             */
            labelBaseDTOList: Array<{
              /**
               * 标签名
               * ---
               * 标签名
               */
              labelName: string;
            }> | null;
          }>;
          code: number;
          msg: string;
        }> & {
          params: {
            page?: number;
            size?: number;
          };
        }
      >(
        config: Config
      ): Alova2Method<
        {
          total: number;
          rows: Array<{
            id: number;
            robotName: string;
            robotDesc: string;
            coverUrl: string;
            coverMattingUrl: null;
            configJson: null;
            gender: number;
            age: number;
            starSigns: number;
            face: number | null;
            sceneList: Array<{
              id: number;
              sceneName: string;
              sceneCode: string;
              baseCode: null;
              exampleUrl: string | null;
              coverUrl: string;
              coverMattingUrl: null;
              samplePictureUrl: string;
              proportion: string;
              resolution: string;
              robotId: number;
              sceneType: number;
              posture: string;
              applicationType: string;
              noTrainFlag: number;
              duration: number;
            }>;
            version: number;
            scene: null;
            type: null;
            expireTime: null;
            popularity: number;
            speakerId: string;
            ttsSpeaker: null;
            /**
             * 标签列表
             * ---
             * 标签列表
             */
            labelBaseDTOList: Array<{
              /**
               * 标签名
               * ---
               * 标签名
               */
              labelName: string;
            }> | null;
          }>;
          code: number;
          msg: string;
        },
        'general.post_aivatarapi_robotlist',
        Config
      >;
      /**
       * ---
       *
       * [POST] 获取免费模特列表(全部)
       *
       * **path:** /aiVatarApi/freeRobotListGetAll
       *
       * ---
       *
       * **Response**
       * ```ts
       * type Response = {
       *   // 总记录数（非必填字段）
       *   total: number
       *   // 模特列表（对应字段要求中的data列表，非必填字段）
       *   // [items] start
       *   // [items] end
       *   rows: Array<{
       *     // 模特ID（非必填字段）
       *     id: number
       *     // 模特名称（非必填字段）
       *     robotName: string
       *     // 模特描述（非必填字段）
       *     robotDesc: string
       *     // 模特封面地址（非必填字段）
       *     coverUrl: string
       *     // 绿幕封面自动抠图地址（非必填字段，此处为空）
       *     coverMattingUrl: null
       *     // 配置信息JSON（非必填字段，此处为空）
       *     configJson: null
       *     // 性别，2表示女（非必填字段）
       *     gender: number
       *     // 年龄（非必填字段）
       *     age: number
       *     // 星座，8表示天蝎座（非必填字段）
       *     starSigns: number
       *     // 面部相关信息（非必填字段，此处为空）
       *     face: number | null
       *     // 数字人场景列表（非必填字段）
       *     // [items] start
       *     // [items] end
       *     sceneList: Array<{
       *       // 场景id（重要，视频合成必填ID，非必填字段）
       *       id: number
       *       // 场景名称（非必填字段）
       *       sceneName: string
       *       // 场景编码（非必填字段，无明确备注）
       *       sceneCode: string
       *       // 基础编码（非必填字段，此处为空）
       *       baseCode: null
       *       // 示例视频（非必填字段）
       *       exampleUrl: string | null
       *       // 场景封面地址（非必填字段）
       *       coverUrl: string
       *       // 场景绿幕封面自动抠图地址（非必填字段，此处为空）
       *       coverMattingUrl: null
       *       // 示例图片（非必填字段）
       *       samplePictureUrl: string
       *       // 比例（非必填字段，无明确备注）
       *       proportion: string
       *       // 分辨率（非必填字段，无明确备注）
       *       resolution: string
       *       // 关联的模特ID（非必填字段，无明确备注）
       *       robotId: number
       *       // 场景类型，0表示绿幕（非必填字段）
       *       sceneType: number
       *       // 姿势（非必填字段，无明确备注）
       *       posture: string
       *       // 应用类型（非必填字段，无明确备注）
       *       applicationType: string
       *       // 无训练标记（非必填字段，无明确备注）
       *       noTrainFlag: number
       *       // 时长（非必填字段，无明确备注）
       *       duration: number
       *     }>
       *     // 类型，0表示2D（非必填字段）
       *     version: number
       *     // 场景信息（非必填字段，此处为空）
       *     scene: null
       *     // 模特类型（非必填字段，此处为空）
       *     type: null
       *     // 过期时间（非必填字段，此处为空）
       *     expireTime: null
       *     // 人气（非必填字段）
       *     popularity: number
       *     // 默认tts发言人ID，对应声音列表接口中的ID字段（非必填字段，可能为空值）
       *     speakerId: string
       *     // tts发言人信息（非必填字段，此处为空）
       *     ttsSpeaker: null
       *     // [title] 标签列表
       *     // 标签列表
       *     // [params1] start
       *     // [items] start
       *     // [items] end
       *     // [params1] end
       *     labelBaseDTOList: Array<{
       *       // [title] 标签名称
       *       // 标签名称
       *       labelName: string
       *     }> | null
       *   }>
       *   code: number
       *   msg: string
       * }
       * ```
       */
      post_aivatarapi_freerobotlistgetall<
        Config extends Alova2MethodConfig<{
          /**
           * 总记录数（非必填字段）
           */
          total: number;
          /**
           * 模特列表（对应字段要求中的data列表，非必填字段）
           */
          rows: Array<{
            /**
             * 模特ID（非必填字段）
             */
            id: number;
            /**
             * 模特名称（非必填字段）
             */
            robotName: string;
            /**
             * 模特描述（非必填字段）
             */
            robotDesc: string;
            /**
             * 模特封面地址（非必填字段）
             */
            coverUrl: string;
            /**
             * 绿幕封面自动抠图地址（非必填字段，此处为空）
             */
            coverMattingUrl: null;
            /**
             * 配置信息JSON（非必填字段，此处为空）
             */
            configJson: null;
            /**
             * 性别，2表示女（非必填字段）
             */
            gender: number;
            /**
             * 年龄（非必填字段）
             */
            age: number;
            /**
             * 星座，8表示天蝎座（非必填字段）
             */
            starSigns: number;
            /**
             * 面部相关信息（非必填字段，此处为空）
             */
            face: number | null;
            /**
             * 数字人场景列表（非必填字段）
             */
            sceneList: Array<{
              /**
               * 场景id（重要，视频合成必填ID，非必填字段）
               */
              id: number;
              /**
               * 场景名称（非必填字段）
               */
              sceneName: string;
              /**
               * 场景编码（非必填字段，无明确备注）
               */
              sceneCode: string;
              /**
               * 基础编码（非必填字段，此处为空）
               */
              baseCode: null;
              /**
               * 示例视频（非必填字段）
               */
              exampleUrl: string | null;
              /**
               * 场景封面地址（非必填字段）
               */
              coverUrl: string;
              /**
               * 场景绿幕封面自动抠图地址（非必填字段，此处为空）
               */
              coverMattingUrl: null;
              /**
               * 示例图片（非必填字段）
               */
              samplePictureUrl: string;
              /**
               * 比例（非必填字段，无明确备注）
               */
              proportion: string;
              /**
               * 分辨率（非必填字段，无明确备注）
               */
              resolution: string;
              /**
               * 关联的模特ID（非必填字段，无明确备注）
               */
              robotId: number;
              /**
               * 场景类型，0表示绿幕（非必填字段）
               */
              sceneType: number;
              /**
               * 姿势（非必填字段，无明确备注）
               */
              posture: string;
              /**
               * 应用类型（非必填字段，无明确备注）
               */
              applicationType: string;
              /**
               * 无训练标记（非必填字段，无明确备注）
               */
              noTrainFlag: number;
              /**
               * 时长（非必填字段，无明确备注）
               */
              duration: number;
            }>;
            /**
             * 类型，0表示2D（非必填字段）
             */
            version: number;
            /**
             * 场景信息（非必填字段，此处为空）
             */
            scene: null;
            /**
             * 模特类型（非必填字段，此处为空）
             */
            type: null;
            /**
             * 过期时间（非必填字段，此处为空）
             */
            expireTime: null;
            /**
             * 人气（非必填字段）
             */
            popularity: number;
            /**
             * 默认tts发言人ID，对应声音列表接口中的ID字段（非必填字段，可能为空值）
             */
            speakerId: string;
            /**
             * tts发言人信息（非必填字段，此处为空）
             */
            ttsSpeaker: null;
            /**
             * 标签列表
             * ---
             * 标签列表
             */
            labelBaseDTOList: Array<{
              /**
               * 标签名称
               * ---
               * 标签名称
               */
              labelName: string;
            }> | null;
          }>;
          code: number;
          msg: string;
        }>
      >(
        config?: Config
      ): Alova2Method<
        {
          /**
           * 总记录数（非必填字段）
           */
          total: number;
          /**
           * 模特列表（对应字段要求中的data列表，非必填字段）
           */
          rows: Array<{
            /**
             * 模特ID（非必填字段）
             */
            id: number;
            /**
             * 模特名称（非必填字段）
             */
            robotName: string;
            /**
             * 模特描述（非必填字段）
             */
            robotDesc: string;
            /**
             * 模特封面地址（非必填字段）
             */
            coverUrl: string;
            /**
             * 绿幕封面自动抠图地址（非必填字段，此处为空）
             */
            coverMattingUrl: null;
            /**
             * 配置信息JSON（非必填字段，此处为空）
             */
            configJson: null;
            /**
             * 性别，2表示女（非必填字段）
             */
            gender: number;
            /**
             * 年龄（非必填字段）
             */
            age: number;
            /**
             * 星座，8表示天蝎座（非必填字段）
             */
            starSigns: number;
            /**
             * 面部相关信息（非必填字段，此处为空）
             */
            face: number | null;
            /**
             * 数字人场景列表（非必填字段）
             */
            sceneList: Array<{
              /**
               * 场景id（重要，视频合成必填ID，非必填字段）
               */
              id: number;
              /**
               * 场景名称（非必填字段）
               */
              sceneName: string;
              /**
               * 场景编码（非必填字段，无明确备注）
               */
              sceneCode: string;
              /**
               * 基础编码（非必填字段，此处为空）
               */
              baseCode: null;
              /**
               * 示例视频（非必填字段）
               */
              exampleUrl: string | null;
              /**
               * 场景封面地址（非必填字段）
               */
              coverUrl: string;
              /**
               * 场景绿幕封面自动抠图地址（非必填字段，此处为空）
               */
              coverMattingUrl: null;
              /**
               * 示例图片（非必填字段）
               */
              samplePictureUrl: string;
              /**
               * 比例（非必填字段，无明确备注）
               */
              proportion: string;
              /**
               * 分辨率（非必填字段，无明确备注）
               */
              resolution: string;
              /**
               * 关联的模特ID（非必填字段，无明确备注）
               */
              robotId: number;
              /**
               * 场景类型，0表示绿幕（非必填字段）
               */
              sceneType: number;
              /**
               * 姿势（非必填字段，无明确备注）
               */
              posture: string;
              /**
               * 应用类型（非必填字段，无明确备注）
               */
              applicationType: string;
              /**
               * 无训练标记（非必填字段，无明确备注）
               */
              noTrainFlag: number;
              /**
               * 时长（非必填字段，无明确备注）
               */
              duration: number;
            }>;
            /**
             * 类型，0表示2D（非必填字段）
             */
            version: number;
            /**
             * 场景信息（非必填字段，此处为空）
             */
            scene: null;
            /**
             * 模特类型（非必填字段，此处为空）
             */
            type: null;
            /**
             * 过期时间（非必填字段，此处为空）
             */
            expireTime: null;
            /**
             * 人气（非必填字段）
             */
            popularity: number;
            /**
             * 默认tts发言人ID，对应声音列表接口中的ID字段（非必填字段，可能为空值）
             */
            speakerId: string;
            /**
             * tts发言人信息（非必填字段，此处为空）
             */
            ttsSpeaker: null;
            /**
             * 标签列表
             * ---
             * 标签列表
             */
            labelBaseDTOList: Array<{
              /**
               * 标签名称
               * ---
               * 标签名称
               */
              labelName: string;
            }> | null;
          }>;
          code: number;
          msg: string;
        },
        'general.post_aivatarapi_freerobotlistgetall',
        Config
      >;
      /**
       * ---
       *
       * [POST] 获取定制模特列表
       *
       * **path:** /aiVatarApi/dzRobotList
       *
       * ---
       *
       * **RequestBody**
       * ```ts
       * type RequestBody = {
       *   page?: number
       *   size?: number
       * }
       * ```
       *
       * ---
       *
       * **Response**
       * ```ts
       * type Response = {
       *   total: number
       *   // [items] start
       *   // [items] end
       *   rows: string[]
       *   code: number
       *   msg: string
       * }
       * ```
       */
      post_aivatarapi_dzrobotlist<
        Config extends Alova2MethodConfig<{
          total: number;
          rows: string[];
          code: number;
          msg: string;
        }> & {
          data: {
            page?: number;
            size?: number;
          };
        }
      >(
        config: Config
      ): Alova2Method<
        {
          total: number;
          rows: string[];
          code: number;
          msg: string;
        },
        'general.post_aivatarapi_dzrobotlist',
        Config
      >;
      /**
       * ---
       *
       * [POST] 极速克隆视频合成接口
       *
       * **path:** /aiVatarApi/simpleCreate
       *
       * ---
       *
       * **RequestBody**
       * ```ts
       * type RequestBody = {
       *   // 视频名称
       *   videoName?: string
       *   // 模特场景ID
       *   sceneId: string
       *   // 音频文件地址(如果有值，则优先用此音频驱动数字人合成)
       *   audioUrl?: string
       *   // 文本(如果audioUrl为空，则使用此文本驱动数字人合成)
       *   text?: string
       *   // 发言人ID(audioUrl为空，text不为空的时候使用)
       *   speakerId?: string
       *   // 指定背景(训练时指定为绿幕视频在合成时可以指定背景)
       *   backgroundUrl?: string
       *   // 音量，取值区间：[0-1](audioUrl为空，text不为空的时候使用)
       *   volume?: string
       *   // 语速，取值区间：[0-1](audioUrl为空，text不为空的时候使用)
       *   speechRate?: string
       *   // 语调，取值区间：[0-1](audioUrl为空，text不为空的时候使用)
       *   pitch?: string
       *   // 循环合成规则：1-反向（默认），0-正向
       *   pn?: number
       * }
       * ```
       *
       * ---
       *
       * **Response**
       * ```ts
       * type Response = object
       * ```
       */
      post_aivatarapi_simplecreate<
        Config extends Alova2MethodConfig<object> & {
          data: {
            /**
             * 视频名称
             */
            videoName?: string;
            /**
             * 模特场景ID
             */
            sceneId: string;
            /**
             * 音频文件地址(如果有值，则优先用此音频驱动数字人合成)
             */
            audioUrl?: string;
            /**
             * 文本(如果audioUrl为空，则使用此文本驱动数字人合成)
             */
            text?: string;
            /**
             * 发言人ID(audioUrl为空，text不为空的时候使用)
             */
            speakerId?: string;
            /**
             * 指定背景(训练时指定为绿幕视频在合成时可以指定背景)
             */
            backgroundUrl?: string;
            /**
             * 音量，取值区间：[0-1](audioUrl为空，text不为空的时候使用)
             */
            volume?: string;
            /**
             * 语速，取值区间：[0-1](audioUrl为空，text不为空的时候使用)
             */
            speechRate?: string;
            /**
             * 语调，取值区间：[0-1](audioUrl为空，text不为空的时候使用)
             */
            pitch?: string;
            /**
             * 循环合成规则：1-反向（默认），0-正向
             */
            pn?: number;
          };
        }
      >(
        config: Config
      ): Alova2Method<object, 'general.post_aivatarapi_simplecreate', Config>;
      /**
       * ---
       *
       * [POST] E级声音克隆接口
       *
       * **path:** /aiVatarApi/clone
       *
       * ---
       *
       * **RequestBody**
       * ```ts
       * type RequestBody = {
       *   // 声音名称
       *   name: string
       *   // 音频素材地址（目前只支持wav格式）
       *   audioUrl: string
       *   // 性别 1: 男，2：女
       *   sex?: number
       * }
       * ```
       *
       * ---
       *
       * **Response**
       * ```ts
       * type Response = object
       * ```
       */
      post_aivatarapi_clone<
        Config extends Alova2MethodConfig<object> & {
          data: {
            /**
             * 声音名称
             */
            name: string;
            /**
             * 音频素材地址（目前只支持wav格式）
             */
            audioUrl: string;
            /**
             * 性别 1: 男，2：女
             */
            sex?: number;
          };
        }
      >(
        config: Config
      ): Alova2Method<object, 'general.post_aivatarapi_clone', Config>;
      /**
       * ---
       *
       * [POST] 声音克隆列表查询
       *
       * **path:** /aiVatarApi/clonePageList
       *
       * ---
       *
       * **Query Parameters**
       * ```ts
       * type QueryParameters = {
       *   // 页码
       *   page: number
       *   // 页数
       *   size: number
       * }
       * ```
       *
       * ---
       *
       * **Response**
       * ```ts
       * type Response = {
       *   // 返回的状态码，类型为string，必须项，0表示成功
       *   code: string
       *   // 成功/错误的描述信息，类型为string，必须项
       *   message: string
       *   // 请求是否成功的标识，补充字段，true表示成功
       *   success: boolean
       *   // 返回的jsonobject，类型为string，必须项
       *   data: {
       *     // 页尺寸，类型为integer，非必须项
       *     pageSize: number
       *     // 页号，类型为integer，非必须项
       *     pageNo: number
       *     // 总记录数，类型为integer，非必须项
       *     totalRecord: number
       *     // 记录列表，类型为list，非必须项
       *     // [items] start
       *     // [items] end
       *     records: Array<{
       *       // 声音ID，类型为string，必须项
       *       speakerId?: string
       *       // 声音名称，类型为string，非必须项
       *       ttsName?: string
       *       // 声音特征，类型为string，非必须项
       *       ttsFeatures?: string
       *       // 声音素材地址，类型为string，非必须项
       *       audioUrl?: string
       *       // 示例音频地址，类型为string，非必须项
       *       ttsAudition?: string
       *       // 支持的语种，类型为array，非必须项，此处支持中文（zh）和英文（en）
       *       // [items] start
       *       // [items] end
       *       languages?: string[]
       *       // 性别，类型为integer，非必须项，1表示男，2表示女，此处为空
       *       sex?: null
       *       // 训练状态，类型为integer，必须项，2表示训练成功（状态说明：0-准备中 1-训练中 2-训练成功 3-训练失败 4-已过期）
       *       status?: number
       *       // 失败原因，类型为string，非必须项，此处为空
       *       reason?: null
       *       // 过期时间，类型为string，非必须项
       *       expireTime?: string
       *     }>
       *   }
       * }
       * ```
       */
      post_aivatarapi_clonepagelist<
        Config extends Alova2MethodConfig<{
          /**
           * 返回的状态码，类型为string，必须项，0表示成功
           */
          code: string;
          /**
           * 成功/错误的描述信息，类型为string，必须项
           */
          message: string;
          /**
           * 请求是否成功的标识，补充字段，true表示成功
           */
          success: boolean;
          /**
           * 返回的jsonobject，类型为string，必须项
           */
          data: {
            /**
             * 页尺寸，类型为integer，非必须项
             */
            pageSize: number;
            /**
             * 页号，类型为integer，非必须项
             */
            pageNo: number;
            /**
             * 总记录数，类型为integer，非必须项
             */
            totalRecord: number;
            /**
             * 记录列表，类型为list，非必须项
             */
            records: Array<{
              /**
               * 声音ID，类型为string，必须项
               */
              speakerId?: string;
              /**
               * 声音名称，类型为string，非必须项
               */
              ttsName?: string;
              /**
               * 声音特征，类型为string，非必须项
               */
              ttsFeatures?: string;
              /**
               * 声音素材地址，类型为string，非必须项
               */
              audioUrl?: string;
              /**
               * 示例音频地址，类型为string，非必须项
               */
              ttsAudition?: string;
              /**
               * 支持的语种，类型为array，非必须项，此处支持中文（zh）和英文（en）
               */
              languages?: string[];
              /**
               * 性别，类型为integer，非必须项，1表示男，2表示女，此处为空
               */
              sex?: null;
              /**
               * 训练状态，类型为integer，必须项，2表示训练成功（状态说明：0-准备中 1-训练中 2-训练成功 3-训练失败 4-已过期）
               */
              status?: number;
              /**
               * 失败原因，类型为string，非必须项，此处为空
               */
              reason?: null;
              /**
               * 过期时间，类型为string，非必须项
               */
              expireTime?: string;
            }>;
          };
        }> & {
          params: {
            /**
             * 页码
             */
            page: number;
            /**
             * 页数
             */
            size: number;
          };
        }
      >(
        config: Config
      ): Alova2Method<
        {
          /**
           * 返回的状态码，类型为string，必须项，0表示成功
           */
          code: string;
          /**
           * 成功/错误的描述信息，类型为string，必须项
           */
          message: string;
          /**
           * 请求是否成功的标识，补充字段，true表示成功
           */
          success: boolean;
          /**
           * 返回的jsonobject，类型为string，必须项
           */
          data: {
            /**
             * 页尺寸，类型为integer，非必须项
             */
            pageSize: number;
            /**
             * 页号，类型为integer，非必须项
             */
            pageNo: number;
            /**
             * 总记录数，类型为integer，非必须项
             */
            totalRecord: number;
            /**
             * 记录列表，类型为list，非必须项
             */
            records: Array<{
              /**
               * 声音ID，类型为string，必须项
               */
              speakerId?: string;
              /**
               * 声音名称，类型为string，非必须项
               */
              ttsName?: string;
              /**
               * 声音特征，类型为string，非必须项
               */
              ttsFeatures?: string;
              /**
               * 声音素材地址，类型为string，非必须项
               */
              audioUrl?: string;
              /**
               * 示例音频地址，类型为string，非必须项
               */
              ttsAudition?: string;
              /**
               * 支持的语种，类型为array，非必须项，此处支持中文（zh）和英文（en）
               */
              languages?: string[];
              /**
               * 性别，类型为integer，非必须项，1表示男，2表示女，此处为空
               */
              sex?: null;
              /**
               * 训练状态，类型为integer，必须项，2表示训练成功（状态说明：0-准备中 1-训练中 2-训练成功 3-训练失败 4-已过期）
               */
              status?: number;
              /**
               * 失败原因，类型为string，非必须项，此处为空
               */
              reason?: null;
              /**
               * 过期时间，类型为string，非必须项
               */
              expireTime?: string;
            }>;
          };
        },
        'general.post_aivatarapi_clonepagelist',
        Config
      >;
      /**
       * ---
       *
       * [POST] 声音克隆详情查询
       *
       * **path:** /aiVatarApi/cloneDetail
       *
       * ---
       *
       * **Query Parameters**
       * ```ts
       * type QueryParameters = {
       *   // 声音ID(路径参数)
       *   id?: string
       * }
       * ```
       *
       * ---
       *
       * **Response**
       * ```ts
       * type Response = {
       *   // 返回的状态码，类型为string，必须项，0表示成功
       *   code: string
       *   // 成功/错误的描述信息，类型为string，必须项
       *   message: string
       *   // 请求是否成功的标识，补充字段，true表示成功
       *   success: boolean
       *   // 返回的jsonobject，类型为string，必须项
       *   data: {
       *     // 声音ID（在合成接口中使用此ID），类型为string，必须项
       *     speakerId: string
       *     // 声音名称，类型为string，非必须项
       *     ttsName: string
       *     // 声音特征，类型为string，非必须项
       *     ttsFeatures: string
       *     // 音频素材地址，类型为string，非必须项
       *     audioUrl: string
       *     // 示例音频地址，类型为string，非必须项
       *     ttsAudition: string
       *     // 支持的语种，类型为array，非必须项，此处支持中文（zh）和英文（en）
       *     // [items] start
       *     // [items] end
       *     languages: string[]
       *     // 性别，类型为Integer，非必须项，1表示男，2表示女，此处为空
       *     sex: null
       *     // 训练状态，类型为Integer，必须项，2表示训练成功（状态说明：0-准备中 1-训练中 2-训练成功 3-训练失败 4-已过期）
       *     status: number
       *     // 失败原因，类型为string，非必须项，此处为空
       *     reason: null
       *     // 过期时间，类型为string，非必须项
       *     expireTime: string
       *   }
       * }
       * ```
       */
      post_aivatarapi_clonedetail<
        Config extends Alova2MethodConfig<{
          /**
           * 返回的状态码，类型为string，必须项，0表示成功
           */
          code: string;
          /**
           * 成功/错误的描述信息，类型为string，必须项
           */
          message: string;
          /**
           * 请求是否成功的标识，补充字段，true表示成功
           */
          success: boolean;
          /**
           * 返回的jsonobject，类型为string，必须项
           */
          data: {
            /**
             * 声音ID（在合成接口中使用此ID），类型为string，必须项
             */
            speakerId: string;
            /**
             * 声音名称，类型为string，非必须项
             */
            ttsName: string;
            /**
             * 声音特征，类型为string，非必须项
             */
            ttsFeatures: string;
            /**
             * 音频素材地址，类型为string，非必须项
             */
            audioUrl: string;
            /**
             * 示例音频地址，类型为string，非必须项
             */
            ttsAudition: string;
            /**
             * 支持的语种，类型为array，非必须项，此处支持中文（zh）和英文（en）
             */
            languages: string[];
            /**
             * 性别，类型为Integer，非必须项，1表示男，2表示女，此处为空
             */
            sex: null;
            /**
             * 训练状态，类型为Integer，必须项，2表示训练成功（状态说明：0-准备中 1-训练中 2-训练成功 3-训练失败 4-已过期）
             */
            status: number;
            /**
             * 失败原因，类型为string，非必须项，此处为空
             */
            reason: null;
            /**
             * 过期时间，类型为string，非必须项
             */
            expireTime: string;
          };
        }> & {
          params: {
            /**
             * 声音ID(路径参数)
             */
            id?: string;
          };
        }
      >(
        config: Config
      ): Alova2Method<
        {
          /**
           * 返回的状态码，类型为string，必须项，0表示成功
           */
          code: string;
          /**
           * 成功/错误的描述信息，类型为string，必须项
           */
          message: string;
          /**
           * 请求是否成功的标识，补充字段，true表示成功
           */
          success: boolean;
          /**
           * 返回的jsonobject，类型为string，必须项
           */
          data: {
            /**
             * 声音ID（在合成接口中使用此ID），类型为string，必须项
             */
            speakerId: string;
            /**
             * 声音名称，类型为string，非必须项
             */
            ttsName: string;
            /**
             * 声音特征，类型为string，非必须项
             */
            ttsFeatures: string;
            /**
             * 音频素材地址，类型为string，非必须项
             */
            audioUrl: string;
            /**
             * 示例音频地址，类型为string，非必须项
             */
            ttsAudition: string;
            /**
             * 支持的语种，类型为array，非必须项，此处支持中文（zh）和英文（en）
             */
            languages: string[];
            /**
             * 性别，类型为Integer，非必须项，1表示男，2表示女，此处为空
             */
            sex: null;
            /**
             * 训练状态，类型为Integer，必须项，2表示训练成功（状态说明：0-准备中 1-训练中 2-训练成功 3-训练失败 4-已过期）
             */
            status: number;
            /**
             * 失败原因，类型为string，非必须项，此处为空
             */
            reason: null;
            /**
             * 过期时间，类型为string，非必须项
             */
            expireTime: string;
          };
        },
        'general.post_aivatarapi_clonedetail',
        Config
      >;
      /**
       * ---
       *
       * [POST] 极速声音克隆接口
       *
       * **path:** /aiVatarApi/fastClone
       *
       * ---
       *
       * **RequestBody**
       * ```ts
       * type RequestBody = {
       *   // 声音名称
       *   name: string
       *   // 音频素材地址（目前只支持wav格式）
       *   audioUrl: string
       *   // 性别 1: 男，2：女
       *   sex?: number
       * }
       * ```
       *
       * ---
       *
       * **Response**
       * ```ts
       * type Response = object
       * ```
       */
      post_aivatarapi_fastclone<
        Config extends Alova2MethodConfig<object> & {
          data: {
            /**
             * 声音名称
             */
            name: string;
            /**
             * 音频素材地址（目前只支持wav格式）
             */
            audioUrl: string;
            /**
             * 性别 1: 男，2：女
             */
            sex?: number;
          };
        }
      >(
        config: Config
      ): Alova2Method<object, 'general.post_aivatarapi_fastclone', Config>;
      /**
       * ---
       *
       * [POST] 声音克隆音频合成接口
       *
       * **path:** /aiVatarApi/fastTts
       *
       * ---
       *
       * **RequestBody**
       * ```ts
       * type RequestBody = {
       *   // 发言人ID不能为空
       *   speakerId: string
       *   // 文本内容不能为空
       *   content: string
       * }
       * ```
       *
       * ---
       *
       * **Response**
       * ```ts
       * type Response = object
       * ```
       */
      post_aivatarapi_fasttts<
        Config extends Alova2MethodConfig<object> & {
          data: {
            /**
             * 发言人ID不能为空
             */
            speakerId: string;
            /**
             * 文本内容不能为空
             */
            content: string;
          };
        }
      >(
        config: Config
      ): Alova2Method<object, 'general.post_aivatarapi_fasttts', Config>;
      /**
       * ---
       *
       * [POST] 声音合成列表查询
       *
       * **path:** /aiVatarApi/ttsPageList
       *
       * ---
       *
       * **Query Parameters**
       * ```ts
       * type QueryParameters = {
       *   page?: number
       *   size?: number
       * }
       * ```
       *
       * ---
       *
       * **Response**
       * ```ts
       * type Response = {
       *   total: number
       *   // [items] start
       *   // [items] end
       *   rows: string[]
       *   code: number
       *   msg: string
       * }
       * ```
       */
      post_aivatarapi_ttspagelist<
        Config extends Alova2MethodConfig<{
          total: number;
          rows: string[];
          code: number;
          msg: string;
        }> & {
          params: {
            page?: number;
            size?: number;
          };
        }
      >(
        config: Config
      ): Alova2Method<
        {
          total: number;
          rows: string[];
          code: number;
          msg: string;
        },
        'general.post_aivatarapi_ttspagelist',
        Config
      >;
      /**
       * ---
       *
       * [POST] 声音合成详情查询
       *
       * **path:** /aiVatarApi/ttsDetail
       *
       * ---
       *
       * **Query Parameters**
       * ```ts
       * type QueryParameters = {
       *   // TTS提交时返回的ID(路径参数)
       *   id?: number
       * }
       * ```
       *
       * ---
       *
       * **Response**
       * ```ts
       * type Response = {
       *   msg: string
       *   code: number
       *   data: object
       * }
       * ```
       */
      post_aivatarapi_ttsdetail<
        Config extends Alova2MethodConfig<{
          msg: string;
          code: number;
          data: object;
        }> & {
          params: {
            /**
             * TTS提交时返回的ID(路径参数)
             */
            id?: number;
          };
        }
      >(
        config: Config
      ): Alova2Method<
        {
          msg: string;
          code: number;
          data: object;
        },
        'general.post_aivatarapi_ttsdetail',
        Config
      >;
      /**
       * ---
       *
       * [POST] 获取企业所有发音人列表
       *
       * **path:** /aiVatarApi/speakerList
       *
       * ---
       *
       * **Response**
       * ```ts
       * type Response = {
       *   // 符合查询条件的发言人总数量
       *   total: number
       *   // 发言人列表数据，为jsonArray格式
       *   // [items] start
       *   // [items] end
       *   rows: Array<{
       *     // 发言人ID，对应字段"id"，类型为string，为必须项
       *     id: number
       *     // 发言人名
       *     ttsName: string
       *     // 发言人介绍
       *     ttsIntroduction: string
       *     // 发言人使用场景
       *     ttsScenes: string
       *     // 发言人标识
       *     ttsSpeaker: string
       *     // 发言人特色
       *     ttsFeatures: string
       *     // 示例音频地址
       *     ttsAudition: string
       *     // 封面地址
       *     ttsCover: string
       *     // 发言人来源，非必须返回字段，此处为空
       *     ttsSource: null
       *     // 支持的语言类型列表，对应字段"languages"，类型为jsonArray，为必须项，此处仅支持中文（cn）
       *     // [items] start
       *     // [items] end
       *     languages: string[]
       *     // 是否免费使用，非必须返回字段
       *     free: boolean
       *     // 性别，对应字段"sex"，类型为integer，为必须项，2表示女
       *     sex: number
       *     ttsExtendJson: string
       *     // 多音字标识，对应字段"phonemeFlag"，类型为integer，为必须项，1表示支持
       *     phonemeFlag: number
       *   }>
       *   // 返回的状态码，对应字段"code"，类型为string，为必须项，此处200表示查询成功（原要求中成功状态码为0，此处为实际返回的状态码）
       *   code: number
       *   // 成功/错误的描述信息，类型为string，为必须项，此处为查询成功的描述
       *   msg: string
       * }
       * ```
       */
      post_aivatarapi_speakerlist<
        Config extends Alova2MethodConfig<{
          /**
           * 符合查询条件的发言人总数量
           */
          total: number;
          /**
           * 发言人列表数据，为jsonArray格式
           */
          rows: Array<{
            /**
             * 发言人ID，对应字段"id"，类型为string，为必须项
             */
            id: number;
            /**
             * 发言人名
             */
            ttsName: string;
            /**
             * 发言人介绍
             */
            ttsIntroduction: string;
            /**
             * 发言人使用场景
             */
            ttsScenes: string;
            /**
             * 发言人标识
             */
            ttsSpeaker: string;
            /**
             * 发言人特色
             */
            ttsFeatures: string;
            /**
             * 示例音频地址
             */
            ttsAudition: string;
            /**
             * 封面地址
             */
            ttsCover: string;
            /**
             * 发言人来源，非必须返回字段，此处为空
             */
            ttsSource: null;
            /**
             * 支持的语言类型列表，对应字段"languages"，类型为jsonArray，为必须项，此处仅支持中文（cn）
             */
            languages: string[];
            /**
             * 是否免费使用，非必须返回字段
             */
            free: boolean;
            /**
             * 性别，对应字段"sex"，类型为integer，为必须项，2表示女
             */
            sex: number;
            ttsExtendJson: string;
            /**
             * 多音字标识，对应字段"phonemeFlag"，类型为integer，为必须项，1表示支持
             */
            phonemeFlag: number;
          }>;
          /**
           * 返回的状态码，对应字段"code"，类型为string，为必须项，此处200表示查询成功（原要求中成功状态码为0，此处为实际返回的状态码）
           */
          code: number;
          /**
           * 成功/错误的描述信息，类型为string，为必须项，此处为查询成功的描述
           */
          msg: string;
        }>
      >(
        config?: Config
      ): Alova2Method<
        {
          /**
           * 符合查询条件的发言人总数量
           */
          total: number;
          /**
           * 发言人列表数据，为jsonArray格式
           */
          rows: Array<{
            /**
             * 发言人ID，对应字段"id"，类型为string，为必须项
             */
            id: number;
            /**
             * 发言人名
             */
            ttsName: string;
            /**
             * 发言人介绍
             */
            ttsIntroduction: string;
            /**
             * 发言人使用场景
             */
            ttsScenes: string;
            /**
             * 发言人标识
             */
            ttsSpeaker: string;
            /**
             * 发言人特色
             */
            ttsFeatures: string;
            /**
             * 示例音频地址
             */
            ttsAudition: string;
            /**
             * 封面地址
             */
            ttsCover: string;
            /**
             * 发言人来源，非必须返回字段，此处为空
             */
            ttsSource: null;
            /**
             * 支持的语言类型列表，对应字段"languages"，类型为jsonArray，为必须项，此处仅支持中文（cn）
             */
            languages: string[];
            /**
             * 是否免费使用，非必须返回字段
             */
            free: boolean;
            /**
             * 性别，对应字段"sex"，类型为integer，为必须项，2表示女
             */
            sex: number;
            ttsExtendJson: string;
            /**
             * 多音字标识，对应字段"phonemeFlag"，类型为integer，为必须项，1表示支持
             */
            phonemeFlag: number;
          }>;
          /**
           * 返回的状态码，对应字段"code"，类型为string，为必须项，此处200表示查询成功（原要求中成功状态码为0，此处为实际返回的状态码）
           */
          code: number;
          /**
           * 成功/错误的描述信息，类型为string，为必须项，此处为查询成功的描述
           */
          msg: string;
        },
        'general.post_aivatarapi_speakerlist',
        Config
      >;
      /**
       * ---
       *
       * [POST] 数字名片合成接口
       *
       * **path:** /aiVatarApi/creatDigitalCard
       *
       * ---
       *
       * **RequestBody**
       * ```ts
       * type RequestBody = {
       *   // 3.18 接口查询出来的数字名片id
       *   templateId: number
       *   // 3.18 接口查询出来的数字名片configJson(<div>#前缀的字符串，要修改的字符串)
       *   configJson: string
       *   // 声音url （wav、MP3）
       *   audioUrl: string
       *   // 头像图片url (png、jpg)
       *   headImg: string
       *   // 用3.24接口返回的风格对应的optionValue
       *   sceneId: string
       * }
       * ```
       *
       * ---
       *
       * **Response**
       * ```ts
       * type Response = {
       *   code: string
       *   message: string
       *   success: boolean
       *   data: {
       *     videoId: number
       *   }
       * }
       * ```
       */
      post_aivatarapi_creatdigitalcard<
        Config extends Alova2MethodConfig<{
          code: string;
          message: string;
          success: boolean;
          data: {
            videoId: number;
          };
        }> & {
          data: {
            /**
             * 3.18 接口查询出来的数字名片id
             */
            templateId: number;
            /**
             * 3.18 接口查询出来的数字名片configJson(<div>#前缀的字符串，要修改的字符串)
             */
            configJson: string;
            /**
             * 声音url （wav、MP3）
             */
            audioUrl: string;
            /**
             * 头像图片url (png、jpg)
             */
            headImg: string;
            /**
             * 用3.24接口返回的风格对应的optionValue
             */
            sceneId: string;
          };
        }
      >(
        config: Config
      ): Alova2Method<
        {
          code: string;
          message: string;
          success: boolean;
          data: {
            videoId: number;
          };
        },
        'general.post_aivatarapi_creatdigitalcard',
        Config
      >;
      /**
       * ---
       *
       * [POST] 查询可用数字名片模板
       *
       * **path:** /aiVatarApi/getDigitalCard
       *
       * ---
       *
       * **Query Parameters**
       * ```ts
       * type QueryParameters = {
       *   page: number
       *   size?: number
       * }
       * ```
       *
       * ---
       *
       * **Response**
       * ```ts
       * type Response = {
       *   total: number
       *   // [items] start
       *   // [items] end
       *   rows: Array<{
       *     id: number
       *     themeName: string
       *     themeCode: null
       *     themeDesc: null
       *     themeType: null
       *     functionType: null
       *     proportion: string
       *     shareType: number
       *     coverUrl: string
       *     videoCoverUrl: string
       *     configJson: {
       *       videoBitRate: string
       *       // [items] start
       *       // [items] end
       *       sounds: string[]
       *       corpId: number
       *       width: number
       *       fps: number
       *       // [items] start
       *       // [items] end
       *       scenes: Array<{
       *         duration: number
       *         // [items] start
       *         // [items] end
       *         nodes: Array<{
       *           // [items] start
       *           // [items] end
       *           effects: string[]
       *           file: string
       *           name: string
       *           lock: boolean
       *           style: {
       *             rotation: number
       *             x: number
       *             width: number
       *             y: number
       *             height: number
       *             color: string
       *             textAlign: string
       *             letterSpacing: number
       *             fontStyle: string
       *             fontFamily: string
       *             fontSize: number
       *             lineHeight: number
       *             style: {
       *               rotation: number
       *               x: number
       *               width: number
       *               y: number
       *               height: number
       *             }
       *             fontWeight: string
       *             textDecoration?: string
       *           }
       *           startTime: number
       *           id: string
       *           endTime: number
       *           type: string
       *           url: string
       *           content: string
       *           ext: object
       *           rotation: number
       *           zIndex: number
       *         }>
       *         limbAvailable: boolean
       *         data: {
       *           ttsAudio: string
       *           wait: number
       *           proportion: string
       *           localAudio: string
       *           sceneUrl: string
       *           audioType: number
       *           global: boolean
       *           version: number
       *           content: string
       *           speed: number
       *           localUrl: string
       *           duration: string
       *           volume: number
       *           emptyAudio: boolean
       *           subTitle: boolean
       *           engine: number
       *           sceneCode: string
       *           sceneType: number
       *           asrEnable: boolean
       *           speaker: string
       *           pitch: number
       *           subtitleList?: string
       *           srt?: string
       *           sceneId?: string
       *           headImg?: string
       *           sex?: number
       *           name?: string
       *           ttsUrl?: string
       *           isSwitchValue?: boolean
       *           speaker2?: string
       *           ttsCategory?: string
       *           vip?: boolean
       *           sampleRate?: number
       *         }
       *         background: {
       *           objectFit: string
       *           color: string
       *           type: string
       *           url: string
       *           zIndex?: number
       *         }
       *         // [items] start
       *         // [items] end
       *         action: string[]
       *         hasDyImage: boolean
       *         // [items] start
       *         // [items] end
       *         express: string[]
       *         id: string
       *         matting: number
       *         isAnnotationFile: boolean
       *         // [items] start
       *         // [items] end
       *         flipSections: string[]
       *         indexSeqUrl: string
       *         // [items] start
       *         // [items] end
       *         seqList: string[]
       *         firstStartIndex: number
       *       }>
       *       hasDyImage: boolean
       *       templateChargeType: number
       *       templateId: string
       *       hasTransition: boolean
       *       userId: number
       *       height: number
       *       digitalTheme?: boolean
       *       platform?: string
       *       digitalResume: boolean
       *       waterMark?: boolean
       *     }
       *     groupId: null
       *     sign: null
       *     platform: string
       *     status: null
       *     robotFreeFlag: null
       *     duration: null
       *     sceneNum: null
       *     applications: null
       *     orderNo: null
       *     fakePopularity: null
       *     fakeSales: null
       *     delFlag: null
       *     createTime: null
       *     updateTime: null
       *   }>
       *   code: number
       *   msg: string
       * }
       * ```
       */
      post_aivatarapi_getdigitalcard<
        Config extends Alova2MethodConfig<{
          total: number;
          rows: Array<{
            id: number;
            themeName: string;
            themeCode: null;
            themeDesc: null;
            themeType: null;
            functionType: null;
            proportion: string;
            shareType: number;
            coverUrl: string;
            videoCoverUrl: string;
            configJson: {
              videoBitRate: string;
              sounds: string[];
              corpId: number;
              width: number;
              fps: number;
              scenes: Array<{
                duration: number;
                nodes: Array<{
                  effects: string[];
                  file: string;
                  name: string;
                  lock: boolean;
                  style: {
                    rotation: number;
                    x: number;
                    width: number;
                    y: number;
                    height: number;
                    color: string;
                    textAlign: string;
                    letterSpacing: number;
                    fontStyle: string;
                    fontFamily: string;
                    fontSize: number;
                    lineHeight: number;
                    style: {
                      rotation: number;
                      x: number;
                      width: number;
                      y: number;
                      height: number;
                    };
                    fontWeight: string;
                    textDecoration?: string;
                  };
                  startTime: number;
                  id: string;
                  endTime: number;
                  type: string;
                  url: string;
                  content: string;
                  ext: object;
                  rotation: number;
                  zIndex: number;
                }>;
                limbAvailable: boolean;
                data: {
                  ttsAudio: string;
                  wait: number;
                  proportion: string;
                  localAudio: string;
                  sceneUrl: string;
                  audioType: number;
                  global: boolean;
                  version: number;
                  content: string;
                  speed: number;
                  localUrl: string;
                  duration: string;
                  volume: number;
                  emptyAudio: boolean;
                  subTitle: boolean;
                  engine: number;
                  sceneCode: string;
                  sceneType: number;
                  asrEnable: boolean;
                  speaker: string;
                  pitch: number;
                  subtitleList?: string;
                  srt?: string;
                  sceneId?: string;
                  headImg?: string;
                  sex?: number;
                  name?: string;
                  ttsUrl?: string;
                  isSwitchValue?: boolean;
                  speaker2?: string;
                  ttsCategory?: string;
                  vip?: boolean;
                  sampleRate?: number;
                };
                background: {
                  objectFit: string;
                  color: string;
                  type: string;
                  url: string;
                  zIndex?: number;
                };
                action: string[];
                hasDyImage: boolean;
                express: string[];
                id: string;
                matting: number;
                isAnnotationFile: boolean;
                flipSections: string[];
                indexSeqUrl: string;
                seqList: string[];
                firstStartIndex: number;
              }>;
              hasDyImage: boolean;
              templateChargeType: number;
              templateId: string;
              hasTransition: boolean;
              userId: number;
              height: number;
              digitalTheme?: boolean;
              platform?: string;
              digitalResume: boolean;
              waterMark?: boolean;
            };
            groupId: null;
            sign: null;
            platform: string;
            status: null;
            robotFreeFlag: null;
            duration: null;
            sceneNum: null;
            applications: null;
            orderNo: null;
            fakePopularity: null;
            fakeSales: null;
            delFlag: null;
            createTime: null;
            updateTime: null;
          }>;
          code: number;
          msg: string;
        }> & {
          params: {
            page: number;
            size?: number;
          };
        }
      >(
        config: Config
      ): Alova2Method<
        {
          total: number;
          rows: Array<{
            id: number;
            themeName: string;
            themeCode: null;
            themeDesc: null;
            themeType: null;
            functionType: null;
            proportion: string;
            shareType: number;
            coverUrl: string;
            videoCoverUrl: string;
            configJson: {
              videoBitRate: string;
              sounds: string[];
              corpId: number;
              width: number;
              fps: number;
              scenes: Array<{
                duration: number;
                nodes: Array<{
                  effects: string[];
                  file: string;
                  name: string;
                  lock: boolean;
                  style: {
                    rotation: number;
                    x: number;
                    width: number;
                    y: number;
                    height: number;
                    color: string;
                    textAlign: string;
                    letterSpacing: number;
                    fontStyle: string;
                    fontFamily: string;
                    fontSize: number;
                    lineHeight: number;
                    style: {
                      rotation: number;
                      x: number;
                      width: number;
                      y: number;
                      height: number;
                    };
                    fontWeight: string;
                    textDecoration?: string;
                  };
                  startTime: number;
                  id: string;
                  endTime: number;
                  type: string;
                  url: string;
                  content: string;
                  ext: object;
                  rotation: number;
                  zIndex: number;
                }>;
                limbAvailable: boolean;
                data: {
                  ttsAudio: string;
                  wait: number;
                  proportion: string;
                  localAudio: string;
                  sceneUrl: string;
                  audioType: number;
                  global: boolean;
                  version: number;
                  content: string;
                  speed: number;
                  localUrl: string;
                  duration: string;
                  volume: number;
                  emptyAudio: boolean;
                  subTitle: boolean;
                  engine: number;
                  sceneCode: string;
                  sceneType: number;
                  asrEnable: boolean;
                  speaker: string;
                  pitch: number;
                  subtitleList?: string;
                  srt?: string;
                  sceneId?: string;
                  headImg?: string;
                  sex?: number;
                  name?: string;
                  ttsUrl?: string;
                  isSwitchValue?: boolean;
                  speaker2?: string;
                  ttsCategory?: string;
                  vip?: boolean;
                  sampleRate?: number;
                };
                background: {
                  objectFit: string;
                  color: string;
                  type: string;
                  url: string;
                  zIndex?: number;
                };
                action: string[];
                hasDyImage: boolean;
                express: string[];
                id: string;
                matting: number;
                isAnnotationFile: boolean;
                flipSections: string[];
                indexSeqUrl: string;
                seqList: string[];
                firstStartIndex: number;
              }>;
              hasDyImage: boolean;
              templateChargeType: number;
              templateId: string;
              hasTransition: boolean;
              userId: number;
              height: number;
              digitalTheme?: boolean;
              platform?: string;
              digitalResume: boolean;
              waterMark?: boolean;
            };
            groupId: null;
            sign: null;
            platform: string;
            status: null;
            robotFreeFlag: null;
            duration: null;
            sceneNum: null;
            applications: null;
            orderNo: null;
            fakePopularity: null;
            fakeSales: null;
            delFlag: null;
            createTime: null;
            updateTime: null;
          }>;
          code: number;
          msg: string;
        },
        'general.post_aivatarapi_getdigitalcard',
        Config
      >;
      /**
       * ---
       *
       * [POST] 创建3D视频合成-文本合成任务
       *
       * **path:** /aiVatarApi/creat3DVideoByText
       *
       * ---
       *
       * **RequestBody**
       * ```ts
       * type RequestBody = {
       *   // 音频地址，需公网可访问
       *   audioUrl: string
       *   // 场景ID，从模特列表接口获取
       *   sceneId: string
       *   // 横向分辨率（默认’1080’）
       *   width?: string
       *   // 纵向分辨率（默认’1920’）
       *   height?: string
       *   // 合成作品名称
       *   videoName?: string
       * }
       * ```
       *
       * ---
       *
       * **Response**
       * ```ts
       * type Response = {
       *   // 状态码
       *   code: string
       *   // 成功/错误的描述信息
       *   message: string
       *   success: boolean
       *   data: {
       *     // 视频作品ID
       *     videoId: number
       *   }
       * }
       * ```
       */
      post_aivatarapi_creat3dvideobytext<
        Config extends Alova2MethodConfig<{
          /**
           * 状态码
           */
          code: string;
          /**
           * 成功/错误的描述信息
           */
          message: string;
          success: boolean;
          data: {
            /**
             * 视频作品ID
             */
            videoId: number;
          };
        }> & {
          data: {
            /**
             * 音频地址，需公网可访问
             */
            audioUrl: string;
            /**
             * 场景ID，从模特列表接口获取
             */
            sceneId: string;
            /**
             * 横向分辨率（默认’1080’）
             */
            width?: string;
            /**
             * 纵向分辨率（默认’1920’）
             */
            height?: string;
            /**
             * 合成作品名称
             */
            videoName?: string;
          };
        }
      >(
        config: Config
      ): Alova2Method<
        {
          /**
           * 状态码
           */
          code: string;
          /**
           * 成功/错误的描述信息
           */
          message: string;
          success: boolean;
          data: {
            /**
             * 视频作品ID
             */
            videoId: number;
          };
        },
        'general.post_aivatarapi_creat3dvideobytext',
        Config
      >;
      /**
       * ---
       *
       * [POST] 创建3D视频合成-语言合成任务
       *
       * **path:** /aiVatarApi/creat3DVideoByAudio
       *
       * ---
       *
       * **RequestBody**
       * ```ts
       * type RequestBody = {
       *   // 合成的文本
       *   text: string
       *   // 发音人id
       *   speakerId: string
       *   // 场景ID，从模特列表接口获取
       *   sceneId: string
       *   // 横向分辨率（默认’1080’）
       *   width?: string
       *   // 纵向分辨率（默认’1920’）
       *   height?: string
       *   // 是否生成字幕，0-不生成字幕和字幕文件 1-生成字幕和字幕文件 2-仅生成字幕文件
       *   srtFlag?: string
       *   // 语速，取值区间：[0-1.0]
       *   speechRate?: string
       *   // 合成作品名
       *   videoName?: string
       *   // 音量，取值区间：[0-1.0]
       *   volume?: string
       * }
       * ```
       *
       * ---
       *
       * **Response**
       * ```ts
       * type Response = {
       *   // 状态码
       *   code: string
       *   // 成功/错误的描述信息
       *   message: string
       *   success: boolean
       *   data: {
       *     // 视频作品ID
       *     videoId: number
       *   }
       * }
       * ```
       */
      post_aivatarapi_creat3dvideobyaudio<
        Config extends Alova2MethodConfig<{
          /**
           * 状态码
           */
          code: string;
          /**
           * 成功/错误的描述信息
           */
          message: string;
          success: boolean;
          data: {
            /**
             * 视频作品ID
             */
            videoId: number;
          };
        }> & {
          data: {
            /**
             * 合成的文本
             */
            text: string;
            /**
             * 发音人id
             */
            speakerId: string;
            /**
             * 场景ID，从模特列表接口获取
             */
            sceneId: string;
            /**
             * 横向分辨率（默认’1080’）
             */
            width?: string;
            /**
             * 纵向分辨率（默认’1920’）
             */
            height?: string;
            /**
             * 是否生成字幕，0-不生成字幕和字幕文件 1-生成字幕和字幕文件 2-仅生成字幕文件
             */
            srtFlag?: string;
            /**
             * 语速，取值区间：[0-1.0]
             */
            speechRate?: string;
            /**
             * 合成作品名
             */
            videoName?: string;
            /**
             * 音量，取值区间：[0-1.0]
             */
            volume?: string;
          };
        }
      >(
        config: Config
      ): Alova2Method<
        {
          /**
           * 状态码
           */
          code: string;
          /**
           * 成功/错误的描述信息
           */
          message: string;
          success: boolean;
          data: {
            /**
             * 视频作品ID
             */
            videoId: number;
          };
        },
        'general.post_aivatarapi_creat3dvideobyaudio',
        Config
      >;
      /**
       * ---
       *
       * [POST] 查询合成视频作品列表
       *
       * **path:** /aiVatarApi/getVideoList
       *
       * ---
       *
       * **Query Parameters**
       * ```ts
       * type QueryParameters = {
       *   // 页码
       *   page?: number
       *   // 页数
       *   size?: number
       * }
       * ```
       *
       * ---
       *
       * **Response**
       * ```ts
       * type Response = {
       *   // 总记录数，类型为integer，非必须项
       *   total: number
       *   // 记录列表，类型为list，非必须项
       *   // [items] start
       *   // [items] end
       *   rows: Array<{
       *     // 提交的任务ID，类型为integer，非必须项
       *     id?: number
       *     // 视频名称，类型为string，非必须项，此处为空
       *     videoName?: null
       *     // 视频格式，类型为string，非必须项
       *     videoFormat?: string
       *     // 水平分辨率，类型为integer，非必须项
       *     horizontal?: number
       *     // 垂直分辨率，类型为integer，非必须项
       *     vertical?: number
       *     // 视频宽高比例，补充字段
       *     proportion?: string
       *     // 时长，类型为integer，非必须项
       *     duration?: string
       *     // 文件尺寸，类型为integer，非必须项
       *     videoSize?: string
       *     // 封面图地址，类型为string，非必须项，此处为空
       *     coverUrl?: null
       *     // 合成视频的URL，类型为string，非必须项
       *     videoUrl?: string
       *     // 字幕文件URL，'srtFlag'为1或2时有值，类型为string，非必须项
       *     srtUrl?: string
       *     // 合成状态，类型为integer，非必须项，3表示合成成功（状态说明：-1.编辑中 1.排队中 2.合成中 3.合成成功 4.合成失败 6.任务取消 7.任务失败）
       *     synthesisStatus?: number
       *     // 用户ID，类型为integer，非必须项
       *     userId?: number
       *     // 创建时间，类型为string，非必须项
       *     createTime?: string
       *     // 更新时间，类型为string，非必须项
       *     updateTime?: string
       *   }>
       *   // 返回的状态码，类型为string，必须项，为0表示成功（此处实际返回为200）
       *   code: number
       *   // 成功/错误的描述信息，类型为string，必须项
       *   msg: string
       * }
       * ```
       */
      post_aivatarapi_getvideolist<
        Config extends Alova2MethodConfig<{
          /**
           * 总记录数，类型为integer，非必须项
           */
          total: number;
          /**
           * 记录列表，类型为list，非必须项
           */
          rows: Array<{
            /**
             * 提交的任务ID，类型为integer，非必须项
             */
            id?: number;
            /**
             * 视频名称，类型为string，非必须项，此处为空
             */
            videoName?: null;
            /**
             * 视频格式，类型为string，非必须项
             */
            videoFormat?: string;
            /**
             * 水平分辨率，类型为integer，非必须项
             */
            horizontal?: number;
            /**
             * 垂直分辨率，类型为integer，非必须项
             */
            vertical?: number;
            /**
             * 视频宽高比例，补充字段
             */
            proportion?: string;
            /**
             * 时长，类型为integer，非必须项
             */
            duration?: string;
            /**
             * 文件尺寸，类型为integer，非必须项
             */
            videoSize?: string;
            /**
             * 封面图地址，类型为string，非必须项，此处为空
             */
            coverUrl?: null;
            /**
             * 合成视频的URL，类型为string，非必须项
             */
            videoUrl?: string;
            /**
             * 字幕文件URL，'srtFlag'为1或2时有值，类型为string，非必须项
             */
            srtUrl?: string;
            /**
             * 合成状态，类型为integer，非必须项，3表示合成成功（状态说明：-1.编辑中 1.排队中 2.合成中 3.合成成功 4.合成失败 6.任务取消 7.任务失败）
             */
            synthesisStatus?: number;
            /**
             * 用户ID，类型为integer，非必须项
             */
            userId?: number;
            /**
             * 创建时间，类型为string，非必须项
             */
            createTime?: string;
            /**
             * 更新时间，类型为string，非必须项
             */
            updateTime?: string;
          }>;
          /**
           * 返回的状态码，类型为string，必须项，为0表示成功（此处实际返回为200）
           */
          code: number;
          /**
           * 成功/错误的描述信息，类型为string，必须项
           */
          msg: string;
        }> & {
          params: {
            /**
             * 页码
             */
            page?: number;
            /**
             * 页数
             */
            size?: number;
          };
        }
      >(
        config: Config
      ): Alova2Method<
        {
          /**
           * 总记录数，类型为integer，非必须项
           */
          total: number;
          /**
           * 记录列表，类型为list，非必须项
           */
          rows: Array<{
            /**
             * 提交的任务ID，类型为integer，非必须项
             */
            id?: number;
            /**
             * 视频名称，类型为string，非必须项，此处为空
             */
            videoName?: null;
            /**
             * 视频格式，类型为string，非必须项
             */
            videoFormat?: string;
            /**
             * 水平分辨率，类型为integer，非必须项
             */
            horizontal?: number;
            /**
             * 垂直分辨率，类型为integer，非必须项
             */
            vertical?: number;
            /**
             * 视频宽高比例，补充字段
             */
            proportion?: string;
            /**
             * 时长，类型为integer，非必须项
             */
            duration?: string;
            /**
             * 文件尺寸，类型为integer，非必须项
             */
            videoSize?: string;
            /**
             * 封面图地址，类型为string，非必须项，此处为空
             */
            coverUrl?: null;
            /**
             * 合成视频的URL，类型为string，非必须项
             */
            videoUrl?: string;
            /**
             * 字幕文件URL，'srtFlag'为1或2时有值，类型为string，非必须项
             */
            srtUrl?: string;
            /**
             * 合成状态，类型为integer，非必须项，3表示合成成功（状态说明：-1.编辑中 1.排队中 2.合成中 3.合成成功 4.合成失败 6.任务取消 7.任务失败）
             */
            synthesisStatus?: number;
            /**
             * 用户ID，类型为integer，非必须项
             */
            userId?: number;
            /**
             * 创建时间，类型为string，非必须项
             */
            createTime?: string;
            /**
             * 更新时间，类型为string，非必须项
             */
            updateTime?: string;
          }>;
          /**
           * 返回的状态码，类型为string，必须项，为0表示成功（此处实际返回为200）
           */
          code: number;
          /**
           * 成功/错误的描述信息，类型为string，必须项
           */
          msg: string;
        },
        'general.post_aivatarapi_getvideolist',
        Config
      >;
      /**
       * ---
       *
       * [POST] 创建视频合成-音频合成任务
       *
       * **path:** /aiVatarApi/createVideoByAudio
       *
       * ---
       *
       * **RequestBody**
       * ```ts
       * type RequestBody = {
       *   // 音频地址
       *   audioUrl: string
       *   // 场景ID
       *   sceneId: string
       *   // 自定义背景
       *   backgroundUrl?: null
       *   // 视频码率
       *   bitRate: string
       *   // 横向分辨率
       *   width: string
       *   // 纵向分辨率
       *   height: string
       *   // 帧率
       *   fps: string
       *   // 是否启用mask
       *   mask: string
       *   // 色彩度
       *   pixFmt: string
       *   // 控制模特大小及位置的对象
       *   style: {
       *     // 控制模特水平方向位置
       *     x: string
       *     // 控制模特垂直方向位置
       *     y: string
       *     // 控制模特宽度
       *     width: string
       *     // 控制模特高度
       *     height: string
       *   }
       *   // 字幕样式设置
       *   srtStyle: {
       *     // 字幕字体大小
       *     fontSize: string
       *     // 字幕颜色
       *     color: string
       *     // 字幕高度
       *     height: number
       *     // 字幕宽度
       *     width: number
       *     // 字幕水平方向位置
       *     x: number
       *     // 字幕垂直方向位置
       *     y: number
       *   }
       *   srtFlag: string
       *   // 字幕文件地址（补充字段），用于指定字幕内容的来源地址
       *   srtUrl: string
       * }
       * ```
       *
       * ---
       *
       * **Response**
       * ```ts
       * type Response = {
       *   // 状态码
       *   code: string
       *   // 描述信息
       *   message: string
       *   success: boolean
       *   data: {
       *     // 视频作品ID
       *     videoId: number
       *   }
       * }
       * ```
       */
      post_aivatarapi_createvideobyaudio<
        Config extends Alova2MethodConfig<{
          /**
           * 状态码
           */
          code: string;
          /**
           * 描述信息
           */
          message: string;
          success: boolean;
          data: {
            /**
             * 视频作品ID
             */
            videoId: number;
          };
        }> & {
          data: {
            /**
             * 音频地址
             */
            audioUrl: string;
            /**
             * 场景ID
             */
            sceneId: string;
            /**
             * 自定义背景
             */
            backgroundUrl?: null;
            /**
             * 视频码率
             */
            bitRate: string;
            /**
             * 横向分辨率
             */
            width: string;
            /**
             * 纵向分辨率
             */
            height: string;
            /**
             * 帧率
             */
            fps: string;
            /**
             * 是否启用mask
             */
            mask: string;
            /**
             * 色彩度
             */
            pixFmt: string;
            /**
             * 控制模特大小及位置的对象
             */
            style: {
              /**
               * 控制模特水平方向位置
               */
              x: string;
              /**
               * 控制模特垂直方向位置
               */
              y: string;
              /**
               * 控制模特宽度
               */
              width: string;
              /**
               * 控制模特高度
               */
              height: string;
            };
            /**
             * 字幕样式设置
             */
            srtStyle: {
              /**
               * 字幕字体大小
               */
              fontSize: string;
              /**
               * 字幕颜色
               */
              color: string;
              /**
               * 字幕高度
               */
              height: number;
              /**
               * 字幕宽度
               */
              width: number;
              /**
               * 字幕水平方向位置
               */
              x: number;
              /**
               * 字幕垂直方向位置
               */
              y: number;
            };
            srtFlag: string;
            /**
             * 字幕文件地址（补充字段），用于指定字幕内容的来源地址
             */
            srtUrl: string;
          };
        }
      >(
        config: Config
      ): Alova2Method<
        {
          /**
           * 状态码
           */
          code: string;
          /**
           * 描述信息
           */
          message: string;
          success: boolean;
          data: {
            /**
             * 视频作品ID
             */
            videoId: number;
          };
        },
        'general.post_aivatarapi_createvideobyaudio',
        Config
      >;
      /**
       * ---
       *
       * [POST] 创建视频合成-文本合成任务
       *
       * **path:** /aiVatarApi/createVideoByText
       *
       * ---
       *
       * **RequestBody**
       * ```ts
       * type RequestBody = {
       *   // 合成的文本
       *   text: string
       *   // 发音人id
       *   speakerId: string
       *   // 场景ID
       *   sceneId: string
       *   // 自定义背景
       *   backgroundUrl: null
       *   // 视频码率
       *   bitRate: string
       *   // 横向分辨率
       *   width: string
       *   // 纵向分辨率
       *   height: string
       *   // fps
       *   fps: string
       *   // 是否启用mask
       *   mask: string
       *   // 是否生成字幕
       *   srtFlag: string
       *   // 是否抠图
       *   matting: string
       *   // 语速
       *   speechRate: string
       *   // 色彩度
       *   pixFmt: string
       *   // 控制模特大小及位置
       *   style: {
       *     // 控制模特水平方向位置(整数值)
       *     x: string
       *     // 控制模特垂直方向位置(整数值)
       *     y: string
       *     // 控制模特宽度(整数值)
       *     width: string
       *     // 控制模特高度(整数值)
       *     height: string
       *   }
       *   // 字幕样式
       *   srtStyle: {
       *     // 字幕水平方向位置
       *     x: number
       *     // 字幕垂直方向位置
       *     y: number
       *     // 字幕字体大小
       *     fontSize: number
       *     // 字幕字体颜色
       *     color: string
       *   }
       * }
       * ```
       *
       * ---
       *
       * **Response**
       * ```ts
       * type Response = {
       *   // 状态码
       *   code: string
       *   // 描述信息
       *   message: string
       *   success: boolean
       *   data: {
       *     // 视频作品ID
       *     videoId: number
       *   }
       * }
       * ```
       */
      post_aivatarapi_createvideobytext<
        Config extends Alova2MethodConfig<{
          /**
           * 状态码
           */
          code: string;
          /**
           * 描述信息
           */
          message: string;
          success: boolean;
          data: {
            /**
             * 视频作品ID
             */
            videoId: number;
          };
        }> & {
          data: {
            /**
             * 合成的文本
             */
            text: string;
            /**
             * 发音人id
             */
            speakerId: string;
            /**
             * 场景ID
             */
            sceneId: string;
            /**
             * 自定义背景
             */
            backgroundUrl: null;
            /**
             * 视频码率
             */
            bitRate: string;
            /**
             * 横向分辨率
             */
            width: string;
            /**
             * 纵向分辨率
             */
            height: string;
            /**
             * fps
             */
            fps: string;
            /**
             * 是否启用mask
             */
            mask: string;
            /**
             * 是否生成字幕
             */
            srtFlag: string;
            /**
             * 是否抠图
             */
            matting: string;
            /**
             * 语速
             */
            speechRate: string;
            /**
             * 色彩度
             */
            pixFmt: string;
            /**
             * 控制模特大小及位置
             */
            style: {
              /**
               * 控制模特水平方向位置(整数值)
               */
              x: string;
              /**
               * 控制模特垂直方向位置(整数值)
               */
              y: string;
              /**
               * 控制模特宽度(整数值)
               */
              width: string;
              /**
               * 控制模特高度(整数值)
               */
              height: string;
            };
            /**
             * 字幕样式
             */
            srtStyle: {
              /**
               * 字幕水平方向位置
               */
              x: number;
              /**
               * 字幕垂直方向位置
               */
              y: number;
              /**
               * 字幕字体大小
               */
              fontSize: number;
              /**
               * 字幕字体颜色
               */
              color: string;
            };
          };
        }
      >(
        config: Config
      ): Alova2Method<
        {
          /**
           * 状态码
           */
          code: string;
          /**
           * 描述信息
           */
          message: string;
          success: boolean;
          data: {
            /**
             * 视频作品ID
             */
            videoId: number;
          };
        },
        'general.post_aivatarapi_createvideobytext',
        Config
      >;
      /**
       * ---
       *
       * [POST] 查询用户使用的数字名片场景风格
       *
       * **path:** /aiVatarApi/digitalCardStyle
       *
       * ---
       *
       * **Response**
       * ```ts
       * type Response = {
       *   msg: string
       *   code: number
       *   // [items] start
       *   // [items] end
       *   data: Array<{
       *     // [items] start
       *     // [items] end
       *     childOption?: Array<{
       *       dictId: string
       *       parentId: string
       *       optionId: string
       *       optionName: string
       *       optionValue: string
       *       extend: string
       *       childOption: null
       *     }>
       *     dictName?: string
       *     remark?: null
       *     id?: null
       *   }>
       * }
       * ```
       */
      post_aivatarapi_digitalcardstyle<
        Config extends Alova2MethodConfig<{
          msg: string;
          code: number;
          data: Array<{
            childOption?: Array<{
              dictId: string;
              parentId: string;
              optionId: string;
              optionName: string;
              optionValue: string;
              extend: string;
              childOption: null;
            }>;
            dictName?: string;
            remark?: null;
            id?: null;
          }>;
        }>
      >(
        config?: Config
      ): Alova2Method<
        {
          msg: string;
          code: number;
          data: Array<{
            childOption?: Array<{
              dictId: string;
              parentId: string;
              optionId: string;
              optionName: string;
              optionValue: string;
              extend: string;
              childOption: null;
            }>;
            dictName?: string;
            remark?: null;
            id?: null;
          }>;
        },
        'general.post_aivatarapi_digitalcardstyle',
        Config
      >;
      /**
       * ---
       *
       * [POST] 数字人训练接口
       *
       * **path:** /aiVatarApi/videoTaskInfo
       *
       * ---
       *
       * **RequestBody**
       * ```ts
       * type RequestBody = {
       *   // 训练任务ID，id为空表示提交新训练，id不为空表示更新训练
       *   id?: string
       *   // 数字人名称
       *   name: string
       *   // 训练视频地址
       *   videoUrl: string
       *   // 定制套餐级别： 1.极速形象克隆 3.极速形象克隆-Pro版 358.形象克隆-S级-超清 359.形象克隆-E级 360.形象克隆-S级
       *   level?: string
       *   // 是否是绿幕: 0.否 1.是
       *   greenScreen?: string
       *   // 视频素材是否压缩处理
       *   compress?: string
       * }
       * ```
       *
       * ---
       *
       * **Response**
       * ```ts
       * type Response = object
       * ```
       */
      post_aivatarapi_videotaskinfo<
        Config extends Alova2MethodConfig<object> & {
          data: {
            /**
             * 训练任务ID，id为空表示提交新训练，id不为空表示更新训练
             */
            id?: string;
            /**
             * 数字人名称
             */
            name: string;
            /**
             * 训练视频地址
             */
            videoUrl: string;
            /**
             * 定制套餐级别： 1.极速形象克隆 3.极速形象克隆-Pro版 358.形象克隆-S级-超清 359.形象克隆-E级 360.形象克隆-S级
             */
            level?: string;
            /**
             * 是否是绿幕: 0.否 1.是
             */
            greenScreen?: string;
            /**
             * 视频素材是否压缩处理
             */
            compress?: string;
          };
        }
      >(
        config: Config
      ): Alova2Method<object, 'general.post_aivatarapi_videotaskinfo', Config>;
      /**
       * ---
       *
       * [POST] 查询用户信息（总代理）
       *
       * **path:** /aiVatarApi/getUserInfo
       *
       * ---
       *
       * **Response**
       * ```ts
       * type Response = {
       *   msg: string
       *   // 返回的状态码，类型为string，必须项（原要求中成功状态码为0，此处为实际返回的状态码200）
       *   code: number
       *   // 返回的jsonobject，类型为string，必须项，包含用户信息和账户信息
       *   data: {
       *     // 用户信息，类型为json，必须项
       *     user: {
       *       // 用户ID，类型为string，必须项
       *       id: number
       *       // 冗余用户ID字段，非必须返回字段，此处为空
       *       userId: null
       *       username: string
       *       // 邮箱地址，类型为string，必须项，此处为空
       *       email: null
       *       // 昵称，类型为string，必须项，此处为空
       *       nickname: null
       *       // 企业ID，类型为string，必须项
       *       corpId: number
       *       // 企业名称，类型为string，必须项，此处为空
       *       corpName: null
       *       mobile: string
       *       // 一级组织ID，类型为string，必须项，此处为空
       *       primaryOrgId: null
       *       // 一级组织名称，类型为string，必须项，此处为空
       *       primaryOrgName: null
       *       // 二级组织ID，类型为string，必须项，此处为空
       *       secondaryOrgId: null
       *       // 二级组织名称，类型为string，必须项，此处为空
       *       secondaryOrgName: null
       *     }
       *     // 账户信息，类型为json，非必须项
       *     account: {
       *       // 音频合成剩余时长（单位：秒），对应字段"ttsDuration"，类型为integer，非必须项，此处为0
       *       ttsDuration: number
       *       // S级-超清版形象克隆剩余次数，对应字段"s1LevelAmount"，类型为integer，非必须项，此处为0
       *       s1LevelAmount: number
       *       // Ai绘画（单位：次），类型为integer，非必须项，此处为0
       *       aiPanting: number
       *       // Ai卡通（单位：次），类型为integer，非必须项，此处为0
       *       aiCartoon: number
       *       // 剩余时长（秒），类型为integer，非必须项，此处为0
       *       duration: number
       *       // E级形象克隆剩余次数，类型为integer，非必须项，此处为0
       *       e0LevelAmount: number
       *       // 合同总金额，类型为integer，非必须项，此处为0
       *       totalAmount: number
       *       // 剩余训练次数，类型为integer，非必须项，此处为0
       *       trainTime: number
       *       balance: string
       *       // S级形象克隆剩余次数，类型为integer，非必须项，此处为0
       *       s0LevelAmount: number
       *       // 模特私有时长汇总（秒），类型为integer，非必须项，此处为0
       *       privateDuration: number
       *       // 消耗时长（秒），类型为integer，非必须项，此处为0
       *       durationCost: number
       *       // 通用时长汇总（秒），类型为integer，非必须项，此处为0
       *       universalDuration: number
       *       giftBalance: string
       *       // 总消费额度，类型为float，非必须项，此处为空
       *       totalCost: null
       *     }
       *   }
       * }
       * ```
       */
      post_aivatarapi_getuserinfo<
        Config extends Alova2MethodConfig<{
          msg: string;
          /**
           * 返回的状态码，类型为string，必须项（原要求中成功状态码为0，此处为实际返回的状态码200）
           */
          code: number;
          /**
           * 返回的jsonobject，类型为string，必须项，包含用户信息和账户信息
           */
          data: {
            /**
             * 用户信息，类型为json，必须项
             */
            user: {
              /**
               * 用户ID，类型为string，必须项
               */
              id: number;
              /**
               * 冗余用户ID字段，非必须返回字段，此处为空
               */
              userId: null;
              username: string;
              /**
               * 邮箱地址，类型为string，必须项，此处为空
               */
              email: null;
              /**
               * 昵称，类型为string，必须项，此处为空
               */
              nickname: null;
              /**
               * 企业ID，类型为string，必须项
               */
              corpId: number;
              /**
               * 企业名称，类型为string，必须项，此处为空
               */
              corpName: null;
              mobile: string;
              /**
               * 一级组织ID，类型为string，必须项，此处为空
               */
              primaryOrgId: null;
              /**
               * 一级组织名称，类型为string，必须项，此处为空
               */
              primaryOrgName: null;
              /**
               * 二级组织ID，类型为string，必须项，此处为空
               */
              secondaryOrgId: null;
              /**
               * 二级组织名称，类型为string，必须项，此处为空
               */
              secondaryOrgName: null;
            };
            /**
             * 账户信息，类型为json，非必须项
             */
            account: {
              /**
               * 音频合成剩余时长（单位：秒），对应字段"ttsDuration"，类型为integer，非必须项，此处为0
               */
              ttsDuration: number;
              /**
               * S级-超清版形象克隆剩余次数，对应字段"s1LevelAmount"，类型为integer，非必须项，此处为0
               */
              s1LevelAmount: number;
              /**
               * Ai绘画（单位：次），类型为integer，非必须项，此处为0
               */
              aiPanting: number;
              /**
               * Ai卡通（单位：次），类型为integer，非必须项，此处为0
               */
              aiCartoon: number;
              /**
               * 剩余时长（秒），类型为integer，非必须项，此处为0
               */
              duration: number;
              /**
               * E级形象克隆剩余次数，类型为integer，非必须项，此处为0
               */
              e0LevelAmount: number;
              /**
               * 合同总金额，类型为integer，非必须项，此处为0
               */
              totalAmount: number;
              /**
               * 剩余训练次数，类型为integer，非必须项，此处为0
               */
              trainTime: number;
              balance: string;
              /**
               * S级形象克隆剩余次数，类型为integer，非必须项，此处为0
               */
              s0LevelAmount: number;
              /**
               * 模特私有时长汇总（秒），类型为integer，非必须项，此处为0
               */
              privateDuration: number;
              /**
               * 消耗时长（秒），类型为integer，非必须项，此处为0
               */
              durationCost: number;
              /**
               * 通用时长汇总（秒），类型为integer，非必须项，此处为0
               */
              universalDuration: number;
              giftBalance: string;
              /**
               * 总消费额度，类型为float，非必须项，此处为空
               */
              totalCost: null;
            };
          };
        }>
      >(
        config?: Config
      ): Alova2Method<
        {
          msg: string;
          /**
           * 返回的状态码，类型为string，必须项（原要求中成功状态码为0，此处为实际返回的状态码200）
           */
          code: number;
          /**
           * 返回的jsonobject，类型为string，必须项，包含用户信息和账户信息
           */
          data: {
            /**
             * 用户信息，类型为json，必须项
             */
            user: {
              /**
               * 用户ID，类型为string，必须项
               */
              id: number;
              /**
               * 冗余用户ID字段，非必须返回字段，此处为空
               */
              userId: null;
              username: string;
              /**
               * 邮箱地址，类型为string，必须项，此处为空
               */
              email: null;
              /**
               * 昵称，类型为string，必须项，此处为空
               */
              nickname: null;
              /**
               * 企业ID，类型为string，必须项
               */
              corpId: number;
              /**
               * 企业名称，类型为string，必须项，此处为空
               */
              corpName: null;
              mobile: string;
              /**
               * 一级组织ID，类型为string，必须项，此处为空
               */
              primaryOrgId: null;
              /**
               * 一级组织名称，类型为string，必须项，此处为空
               */
              primaryOrgName: null;
              /**
               * 二级组织ID，类型为string，必须项，此处为空
               */
              secondaryOrgId: null;
              /**
               * 二级组织名称，类型为string，必须项，此处为空
               */
              secondaryOrgName: null;
            };
            /**
             * 账户信息，类型为json，非必须项
             */
            account: {
              /**
               * 音频合成剩余时长（单位：秒），对应字段"ttsDuration"，类型为integer，非必须项，此处为0
               */
              ttsDuration: number;
              /**
               * S级-超清版形象克隆剩余次数，对应字段"s1LevelAmount"，类型为integer，非必须项，此处为0
               */
              s1LevelAmount: number;
              /**
               * Ai绘画（单位：次），类型为integer，非必须项，此处为0
               */
              aiPanting: number;
              /**
               * Ai卡通（单位：次），类型为integer，非必须项，此处为0
               */
              aiCartoon: number;
              /**
               * 剩余时长（秒），类型为integer，非必须项，此处为0
               */
              duration: number;
              /**
               * E级形象克隆剩余次数，类型为integer，非必须项，此处为0
               */
              e0LevelAmount: number;
              /**
               * 合同总金额，类型为integer，非必须项，此处为0
               */
              totalAmount: number;
              /**
               * 剩余训练次数，类型为integer，非必须项，此处为0
               */
              trainTime: number;
              balance: string;
              /**
               * S级形象克隆剩余次数，类型为integer，非必须项，此处为0
               */
              s0LevelAmount: number;
              /**
               * 模特私有时长汇总（秒），类型为integer，非必须项，此处为0
               */
              privateDuration: number;
              /**
               * 消耗时长（秒），类型为integer，非必须项，此处为0
               */
              durationCost: number;
              /**
               * 通用时长汇总（秒），类型为integer，非必须项，此处为0
               */
              universalDuration: number;
              giftBalance: string;
              /**
               * 总消费额度，类型为float，非必须项，此处为空
               */
              totalCost: null;
            };
          };
        },
        'general.post_aivatarapi_getuserinfo',
        Config
      >;
      /**
       * ---
       *
       * [POST] 查询用户信息（平台用戶）
       *
       * **path:** /aiVatarApi/getMemberInfo
       *
       * ---
       *
       * **Query Parameters**
       * ```ts
       * type QueryParameters = {
       *   Authorization?: string
       * }
       * ```
       *
       * ---
       *
       * **Response**
       * ```ts
       * type Response = {
       *   msg: string
       *   code: number
       *   data: {
       *     user: {
       *       admin: boolean
       *       createBy: string
       *       createTime: string
       *       delFlag: string
       *       dept: {
       *         ancestors: string
       *         // [items] start
       *         // [items] end
       *         children: string[]
       *         deptId: number
       *         deptName: string
       *         leader: string
       *         orderNum: number
       *         params: {
       *           '@type': string
       *         }
       *         parentId: number
       *         status: string
       *       }
       *       deptId: number
       *       loginDate: string
       *       loginIp: string
       *       nickName: string
       *       params: {
       *         '@type': string
       *       }
       *       phonenumber: string
       *       pwdUpdateDate: string
       *       // [items] start
       *       // [items] end
       *       roles: Array<{
       *         admin?: boolean
       *         dataScope?: string
       *         deptCheckStrictly?: boolean
       *         flag?: boolean
       *         menuCheckStrictly?: boolean
       *         params?: {
       *           '@type': string
       *         }
       *         roleId?: number
       *         roleKey?: string
       *         roleName?: string
       *         roleSort?: number
       *         status?: string
       *       }>
       *       sex: string
       *       status: string
       *       userId: number
       *       userName: string
       *       userType: string
       *     }
       *     account: {
       *       createBy: null
       *       createTime: string
       *       updateBy: null
       *       updateTime: string
       *       remark: null
       *       id: number
       *       userId: number
       *       ttsDuration: number
       *       s1LevelAmount: number
       *       aiPanting: number
       *       aiCartoon: number
       *       duration: number
       *       e0LevelAmount: number
       *       totalAmount: number
       *       trainTime: number
       *       balance: number
       *       s0LevelAmount: number
       *       privateDuration: number
       *       durationCost: number
       *       universalDuration: number
       *       giftBalance: number
       *       totalCost: number
       *     }
       *   }
       * }
       * ```
       */
      post_aivatarapi_getmemberinfo<
        Config extends Alova2MethodConfig<{
          msg: string;
          code: number;
          data: {
            user: {
              admin: boolean;
              createBy: string;
              createTime: string;
              delFlag: string;
              dept: {
                ancestors: string;
                children: string[];
                deptId: number;
                deptName: string;
                leader: string;
                orderNum: number;
                params: {
                  '@type': string;
                };
                parentId: number;
                status: string;
              };
              deptId: number;
              loginDate: string;
              loginIp: string;
              nickName: string;
              params: {
                '@type': string;
              };
              phonenumber: string;
              pwdUpdateDate: string;
              roles: Array<{
                admin?: boolean;
                dataScope?: string;
                deptCheckStrictly?: boolean;
                flag?: boolean;
                menuCheckStrictly?: boolean;
                params?: {
                  '@type': string;
                };
                roleId?: number;
                roleKey?: string;
                roleName?: string;
                roleSort?: number;
                status?: string;
              }>;
              sex: string;
              status: string;
              userId: number;
              userName: string;
              userType: string;
            };
            account: {
              createBy: null;
              createTime: string;
              updateBy: null;
              updateTime: string;
              remark: null;
              id: number;
              userId: number;
              ttsDuration: number;
              s1LevelAmount: number;
              aiPanting: number;
              aiCartoon: number;
              duration: number;
              e0LevelAmount: number;
              totalAmount: number;
              trainTime: number;
              balance: number;
              s0LevelAmount: number;
              privateDuration: number;
              durationCost: number;
              universalDuration: number;
              giftBalance: number;
              totalCost: number;
            };
          };
        }> & {
          params: {
            Authorization?: string;
          };
        }
      >(
        config: Config
      ): Alova2Method<
        {
          msg: string;
          code: number;
          data: {
            user: {
              admin: boolean;
              createBy: string;
              createTime: string;
              delFlag: string;
              dept: {
                ancestors: string;
                children: string[];
                deptId: number;
                deptName: string;
                leader: string;
                orderNum: number;
                params: {
                  '@type': string;
                };
                parentId: number;
                status: string;
              };
              deptId: number;
              loginDate: string;
              loginIp: string;
              nickName: string;
              params: {
                '@type': string;
              };
              phonenumber: string;
              pwdUpdateDate: string;
              roles: Array<{
                admin?: boolean;
                dataScope?: string;
                deptCheckStrictly?: boolean;
                flag?: boolean;
                menuCheckStrictly?: boolean;
                params?: {
                  '@type': string;
                };
                roleId?: number;
                roleKey?: string;
                roleName?: string;
                roleSort?: number;
                status?: string;
              }>;
              sex: string;
              status: string;
              userId: number;
              userName: string;
              userType: string;
            };
            account: {
              createBy: null;
              createTime: string;
              updateBy: null;
              updateTime: string;
              remark: null;
              id: number;
              userId: number;
              ttsDuration: number;
              s1LevelAmount: number;
              aiPanting: number;
              aiCartoon: number;
              duration: number;
              e0LevelAmount: number;
              totalAmount: number;
              trainTime: number;
              balance: number;
              s0LevelAmount: number;
              privateDuration: number;
              durationCost: number;
              universalDuration: number;
              giftBalance: number;
              totalCost: number;
            };
          };
        },
        'general.post_aivatarapi_getmemberinfo',
        Config
      >;
      /**
       * ---
       *
       * [POST] 查询企业下所有用户列表
       *
       * **path:** /aiVatarApi/getUserList
       *
       * ---
       *
       * **Query Parameters**
       * ```ts
       * type QueryParameters = {
       *   // 页码
       *   page?: number
       *   // 页数
       *   size?: number
       * }
       * ```
       *
       * ---
       *
       * **Response**
       * ```ts
       * type Response = {
       *   // 总记录数，类型为integer，非必须项
       *   total: number
       *   // 记录列表，类型为list，非必须项
       *   // [items] start
       *   // [items] end
       *   rows: Array<{
       *     // 用户ID，类型为string，必须项
       *     id?: number
       *     // 冗余用户ID字段，无对应必填字段，此处为空
       *     userId?: null
       *     // 用户名，类型为string，必须项
       *     username?: string
       *     // 邮箱地址，类型为string，必须项，此处为空
       *     email?: null
       *     // 昵称，类型为string，必须项，此处为空
       *     nickname?: null
       *     // 企业ID，类型为string，必须项
       *     corpId?: number
       *     // 企业名称，类型为string，必须项
       *     corpName?: string
       *     // 手机号，类型为string，必须项
       *     mobile?: string
       *     // 一级组织ID，类型为string，必须项，此处为空
       *     primaryOrgId?: null
       *     // 一级组织名称，类型为string，必须项，此处为空
       *     primaryOrgName?: null
       *     // 二级组织ID，类型为string，必须项，此处为空
       *     secondaryOrgId?: null
       *     // 二级组织名称，类型为string，必须项，此处为空
       *     secondaryOrgName?: null
       *   }>
       *   // 返回的状态码，类型为string，必须项（注：标准成功状态码为0，此处实际返回为200）
       *   code: number
       *   // 成功/错误的描述信息，类型为string，必须项
       *   msg: string
       * }
       * ```
       */
      post_aivatarapi_getuserlist<
        Config extends Alova2MethodConfig<{
          /**
           * 总记录数，类型为integer，非必须项
           */
          total: number;
          /**
           * 记录列表，类型为list，非必须项
           */
          rows: Array<{
            /**
             * 用户ID，类型为string，必须项
             */
            id?: number;
            /**
             * 冗余用户ID字段，无对应必填字段，此处为空
             */
            userId?: null;
            /**
             * 用户名，类型为string，必须项
             */
            username?: string;
            /**
             * 邮箱地址，类型为string，必须项，此处为空
             */
            email?: null;
            /**
             * 昵称，类型为string，必须项，此处为空
             */
            nickname?: null;
            /**
             * 企业ID，类型为string，必须项
             */
            corpId?: number;
            /**
             * 企业名称，类型为string，必须项
             */
            corpName?: string;
            /**
             * 手机号，类型为string，必须项
             */
            mobile?: string;
            /**
             * 一级组织ID，类型为string，必须项，此处为空
             */
            primaryOrgId?: null;
            /**
             * 一级组织名称，类型为string，必须项，此处为空
             */
            primaryOrgName?: null;
            /**
             * 二级组织ID，类型为string，必须项，此处为空
             */
            secondaryOrgId?: null;
            /**
             * 二级组织名称，类型为string，必须项，此处为空
             */
            secondaryOrgName?: null;
          }>;
          /**
           * 返回的状态码，类型为string，必须项（注：标准成功状态码为0，此处实际返回为200）
           */
          code: number;
          /**
           * 成功/错误的描述信息，类型为string，必须项
           */
          msg: string;
        }> & {
          params: {
            /**
             * 页码
             */
            page?: number;
            /**
             * 页数
             */
            size?: number;
          };
        }
      >(
        config: Config
      ): Alova2Method<
        {
          /**
           * 总记录数，类型为integer，非必须项
           */
          total: number;
          /**
           * 记录列表，类型为list，非必须项
           */
          rows: Array<{
            /**
             * 用户ID，类型为string，必须项
             */
            id?: number;
            /**
             * 冗余用户ID字段，无对应必填字段，此处为空
             */
            userId?: null;
            /**
             * 用户名，类型为string，必须项
             */
            username?: string;
            /**
             * 邮箱地址，类型为string，必须项，此处为空
             */
            email?: null;
            /**
             * 昵称，类型为string，必须项，此处为空
             */
            nickname?: null;
            /**
             * 企业ID，类型为string，必须项
             */
            corpId?: number;
            /**
             * 企业名称，类型为string，必须项
             */
            corpName?: string;
            /**
             * 手机号，类型为string，必须项
             */
            mobile?: string;
            /**
             * 一级组织ID，类型为string，必须项，此处为空
             */
            primaryOrgId?: null;
            /**
             * 一级组织名称，类型为string，必须项，此处为空
             */
            primaryOrgName?: null;
            /**
             * 二级组织ID，类型为string，必须项，此处为空
             */
            secondaryOrgId?: null;
            /**
             * 二级组织名称，类型为string，必须项，此处为空
             */
            secondaryOrgName?: null;
          }>;
          /**
           * 返回的状态码，类型为string，必须项（注：标准成功状态码为0，此处实际返回为200）
           */
          code: number;
          /**
           * 成功/错误的描述信息，类型为string，必须项
           */
          msg: string;
        },
        'general.post_aivatarapi_getuserlist',
        Config
      >;
      /**
       * ---
       *
       * [POST] 素材查询接口
       *
       * **path:** /aiVatarApi/getsucai
       *
       * ---
       *
       * **Query Parameters**
       * ```ts
       * type QueryParameters = {
       *   // 素材类型 ，0：背景（图片或者视频），1：图片素材，4：视频素材
       *   type?: number
       *   // 页码
       *   page?: number
       *   size?: number
       * }
       * ```
       *
       * ---
       *
       * **Response**
       * ```ts
       * type Response = object
       * ```
       */
      post_aivatarapi_getsucai<
        Config extends Alova2MethodConfig<object> & {
          params: {
            /**
             * 素材类型 ，0：背景（图片或者视频），1：图片素材，4：视频素材
             */
            type?: number;
            /**
             * 页码
             */
            page?: number;
            size?: number;
          };
        }
      >(
        config: Config
      ): Alova2Method<object, 'general.post_aivatarapi_getsucai', Config>;
      /**
       * ---
       *
       * [POST] 查询用户可用字体
       *
       * **path:** /aiVatarApi/getFontCategoryList
       *
       * ---
       *
       * **Response**
       * ```ts
       * type Response = {
       *   // 描述信息
       *   msg: string
       *   // 状态码
       *   code: number
       *   // [items] start
       *   // [items] end
       *   data: Array<{
       *     // 中文名
       *     text: string
       *     // 字体名称
       *     value: string
       *   }>
       * }
       * ```
       */
      post_aivatarapi_getfontcategorylist<
        Config extends Alova2MethodConfig<{
          /**
           * 描述信息
           */
          msg: string;
          /**
           * 状态码
           */
          code: number;
          data: Array<{
            /**
             * 中文名
             */
            text: string;
            /**
             * 字体名称
             */
            value: string;
          }>;
        }>
      >(
        config?: Config
      ): Alova2Method<
        {
          /**
           * 描述信息
           */
          msg: string;
          /**
           * 状态码
           */
          code: number;
          data: Array<{
            /**
             * 中文名
             */
            text: string;
            /**
             * 字体名称
             */
            value: string;
          }>;
        },
        'general.post_aivatarapi_getfontcategorylist',
        Config
      >;
      /**
       * ---
       *
       * [POST] 查询用户可用字体描边
       *
       * **path:** /aiVatarApi/getTextStyleList
       *
       * ---
       *
       * **Response**
       * ```ts
       * type Response = {
       *   msg: string
       *   code: number
       *   // [items] start
       *   // [items] end
       *   data: Array<{
       *     styleIcon: string
       *     style_text: string
       *     style_outline: string
       *     style_thickness: string
       *   }>
       * }
       * ```
       */
      post_aivatarapi_gettextstylelist<
        Config extends Alova2MethodConfig<{
          msg: string;
          code: number;
          data: Array<{
            styleIcon: string;
            style_text: string;
            style_outline: string;
            style_thickness: string;
          }>;
        }>
      >(
        config?: Config
      ): Alova2Method<
        {
          msg: string;
          code: number;
          data: Array<{
            styleIcon: string;
            style_text: string;
            style_outline: string;
            style_thickness: string;
          }>;
        },
        'general.post_aivatarapi_gettextstylelist',
        Config
      >;
      /**
       * ---
       *
       * [POST] 训练任务列表查询
       *
       * **path:** /aiVatarApi/getTrainingList
       *
       * ---
       *
       * **Query Parameters**
       * ```ts
       * type QueryParameters = {
       *   page?: number
       *   size?: number
       * }
       * ```
       *
       * ---
       *
       * **Response**
       * ```ts
       * type Response = {
       *   total: number
       *   // [items] start
       *   // [items] end
       *   rows: string[]
       *   code: number
       *   msg: string
       * }
       * ```
       */
      post_aivatarapi_gettraininglist<
        Config extends Alova2MethodConfig<{
          total: number;
          rows: string[];
          code: number;
          msg: string;
        }> & {
          params: {
            page?: number;
            size?: number;
          };
        }
      >(
        config: Config
      ): Alova2Method<
        {
          total: number;
          rows: string[];
          code: number;
          msg: string;
        },
        'general.post_aivatarapi_gettraininglist',
        Config
      >;
      /**
       * ---
       *
       * [POST] 训练任务信息查询
       *
       * **path:** /aiVatarApi/getTrainingById
       *
       * ---
       *
       * **RequestBody**
       * ```ts
       * type RequestBody = {
       *   id?: number
       * }
       * ```
       *
       * ---
       *
       * **Response**
       * ```ts
       * type Response = {
       *   msg: string
       *   code: number
       *   data: object
       * }
       * ```
       */
      post_aivatarapi_gettrainingbyid<
        Config extends Alova2MethodConfig<{
          msg: string;
          code: number;
          data: object;
        }> & {
          data: {
            id?: number;
          };
        }
      >(
        config: Config
      ): Alova2Method<
        {
          msg: string;
          code: number;
          data: object;
        },
        'general.post_aivatarapi_gettrainingbyid',
        Config
      >;
      /**
       * ---
       *
       * [POST] 合成任务信息查询
       *
       * **path:** /aiVatarApi/getMessionInfo
       *
       * ---
       *
       * **Query Parameters**
       * ```ts
       * type QueryParameters = {
       *   id?: number
       * }
       * ```
       *
       * ---
       *
       * **Response**
       * ```ts
       * type Response = {
       *   msg: string
       *   code: number
       *   data: object
       * }
       * ```
       */
      post_aivatarapi_getmessioninfo<
        Config extends Alova2MethodConfig<{
          msg: string;
          code: number;
          data: object;
        }> & {
          params: {
            id?: number;
          };
        }
      >(
        config: Config
      ): Alova2Method<
        {
          msg: string;
          code: number;
          data: object;
        },
        'general.post_aivatarapi_getmessioninfo',
        Config
      >;
      /**
       * ---
       *
       * [POST] tts试听接口
       *
       * **path:** /aiVatarApi/ttsSynthesis
       *
       * ---
       *
       * **RequestBody**
       * ```ts
       * type RequestBody = {
       *   // 发言人ID不能为空
       *   speakerId: string
       *   // 音量，取值区间：[0-1]
       *   volume?: string
       *   // 语速，取值区间：[0-1]
       *   speechRate?: string
       *   // 语调，取值区间：[0-1]
       *   pitch?: string
       *   // 文本内容不能为空，文本支持以下标签： 插入停顿(单位:秒)：<delay value="0.5"/> 自定义读音：<grammar type="custom" value="十万">100000</grammar> 多音字标注：<grammar type="pinyin" value="hang2">行</grammar>
       *   content: string
       *   // 否需要字幕文件 0-不需要 1-需要
       *   srtFlag?: string
       *   // 采样率：来自3.4接口的ttsExtendJson
       *   sampleRate?: string
       *   // 是否异步处理，true-异步返回（默认是同步返回，推荐使用异步接口）
       *   async: boolean
       * }
       * ```
       *
       * ---
       *
       * **Response**
       * ```ts
       * type Response = object
       * ```
       */
      post_aivatarapi_ttssynthesis<
        Config extends Alova2MethodConfig<object> & {
          data: {
            /**
             * 发言人ID不能为空
             */
            speakerId: string;
            /**
             * 音量，取值区间：[0-1]
             */
            volume?: string;
            /**
             * 语速，取值区间：[0-1]
             */
            speechRate?: string;
            /**
             * 语调，取值区间：[0-1]
             */
            pitch?: string;
            /**
             * 文本内容不能为空，文本支持以下标签： 插入停顿(单位:秒)：<delay value="0.5"/> 自定义读音：<grammar type="custom" value="十万">100000</grammar> 多音字标注：<grammar type="pinyin" value="hang2">行</grammar>
             */
            content: string;
            /**
             * 否需要字幕文件 0-不需要 1-需要
             */
            srtFlag?: string;
            /**
             * 采样率：来自3.4接口的ttsExtendJson
             */
            sampleRate?: string;
            /**
             * 是否异步处理，true-异步返回（默认是同步返回，推荐使用异步接口）
             */
            async: boolean;
          };
        }
      >(
        config: Config
      ): Alova2Method<object, 'general.post_aivatarapi_ttssynthesis', Config>;
      /**
       * ---
       *
       * [POST] 文件上传授权获取
       *
       * **path:** /aiVatarApi/getTemporaryUploadUrl
       *
       * ---
       *
       * **RequestBody**
       * ```ts
       * type RequestBody = {
       *   // 文件类型，如：mp4,mov,png,jpeg,jpg,gif,wav,mp3
       *   fileType: string
       * }
       * ```
       *
       * ---
       *
       * **Response**
       * ```ts
       * type Response = {
       *   // 成功/错误的描述信息
       *   msg: string
       *   // 状态码
       *   code: number
       *   data: {
       *     // 临时上传地址（请求到此地址后，调用方使用PUT方法将文件提交到此路径）
       *     temporaryUrl: string
       *     // 真实地址（待调用方上传成功后，这个即是真实地址）
       *     objectUrl: string
       *   }
       * }
       * ```
       */
      post_aivatarapi_gettemporaryuploadurl<
        Config extends Alova2MethodConfig<{
          /**
           * 成功/错误的描述信息
           */
          msg: string;
          /**
           * 状态码
           */
          code: number;
          data: {
            /**
             * 临时上传地址（请求到此地址后，调用方使用PUT方法将文件提交到此路径）
             */
            temporaryUrl: string;
            /**
             * 真实地址（待调用方上传成功后，这个即是真实地址）
             */
            objectUrl: string;
          };
        }> & {
          data: {
            /**
             * 文件类型，如：mp4,mov,png,jpeg,jpg,gif,wav,mp3
             */
            fileType: string;
          };
        }
      >(
        config: Config
      ): Alova2Method<
        {
          /**
           * 成功/错误的描述信息
           */
          msg: string;
          /**
           * 状态码
           */
          code: number;
          data: {
            /**
             * 临时上传地址（请求到此地址后，调用方使用PUT方法将文件提交到此路径）
             */
            temporaryUrl: string;
            /**
             * 真实地址（待调用方上传成功后，这个即是真实地址）
             */
            objectUrl: string;
          };
        },
        'general.post_aivatarapi_gettemporaryuploadurl',
        Config
      >;
      /**
       * ---
       *
       * [POST] 登录、注册时手机短信验证码
       *
       * **path:** /aiVatarApi/sendMsg
       *
       * ---
       *
       * **Query Parameters**
       * ```ts
       * type QueryParameters = {
       *   // 手机号码
       *   phoneNumber: string
       * }
       * ```
       *
       * ---
       *
       * **Response**
       * ```ts
       * type Response = {
       *   // 状态码
       *   msg: string
       *   // 成功/错误的描述信息
       *   code: number
       * }
       * ```
       */
      post_aivatarapi_sendmsg<
        Config extends Alova2MethodConfig<{
          /**
           * 状态码
           */
          msg: string;
          /**
           * 成功/错误的描述信息
           */
          code: number;
        }> & {
          params: {
            /**
             * 手机号码
             */
            phoneNumber: string;
          };
        }
      >(
        config: Config
      ): Alova2Method<
        {
          /**
           * 状态码
           */
          msg: string;
          /**
           * 成功/错误的描述信息
           */
          code: number;
        },
        'general.post_aivatarapi_sendmsg',
        Config
      >;
      /**
       * ---
       *
       * [POST] 用户注册
       *
       * **path:** /register
       *
       * ---
       *
       * **RequestBody**
       * ```ts
       * type RequestBody = {
       *   // 用户昵称
       *   nickname: string
       *   // 手机号码
       *   phonenumber: string
       *   // 验证码
       *   code: string
       *   // 密码
       *   password: string
       *   // 用户名
       *   username: string
       * }
       * ```
       *
       * ---
       *
       * **Response**
       * ```ts
       * type Response = {
       *   // 成功/错误的描述信息
       *   msg: string
       *   // 状态码
       *   code: number
       * }
       * ```
       */
      post_register<
        Config extends Alova2MethodConfig<{
          /**
           * 成功/错误的描述信息
           */
          msg: string;
          /**
           * 状态码
           */
          code: number;
        }> & {
          data: {
            /**
             * 用户昵称
             */
            nickname: string;
            /**
             * 手机号码
             */
            phonenumber: string;
            /**
             * 验证码
             */
            code: string;
            /**
             * 密码
             */
            password: string;
            /**
             * 用户名
             */
            username: string;
          };
        }
      >(
        config: Config
      ): Alova2Method<
        {
          /**
           * 成功/错误的描述信息
           */
          msg: string;
          /**
           * 状态码
           */
          code: number;
        },
        'general.post_register',
        Config
      >;
      /**
       * ---
       *
       * [GET] 查询账户余额
       *
       * **path:** /tabAccount/tabAccount/list
       *
       * ---
       *
       * **Response**
       * ```ts
       * type Response = {
       *   total: number
       *   // [items] start
       *   // [items] end
       *   rows: Array<{
       *     createBy?: null
       *     createTime?: string
       *     updateBy?: null
       *     updateTime?: string
       *     remark?: null
       *     id?: number
       *     userId?: number
       *     deptId?: null
       *     ttsDuration?: number
       *     s1LevelAmount?: number
       *     aiPanting?: number
       *     aiCartoon?: number
       *     duration?: number
       *     e0LevelAmount?: number
       *     totalAmount?: number
       *     trainTime?: number
       *     balance?: number
       *     s0LevelAmount?: number
       *     privateDuration?: number
       *     durationCost?: number
       *     universalDuration?: number
       *     giftBalance?: number
       *     totalCost?: number
       *   }>
       *   code: number
       *   msg: string
       * }
       * ```
       */
      get_tabaccount_tabaccount_list<
        Config extends Alova2MethodConfig<{
          total: number;
          rows: Array<{
            createBy?: null;
            createTime?: string;
            updateBy?: null;
            updateTime?: string;
            remark?: null;
            id?: number;
            userId?: number;
            deptId?: null;
            ttsDuration?: number;
            s1LevelAmount?: number;
            aiPanting?: number;
            aiCartoon?: number;
            duration?: number;
            e0LevelAmount?: number;
            totalAmount?: number;
            trainTime?: number;
            balance?: number;
            s0LevelAmount?: number;
            privateDuration?: number;
            durationCost?: number;
            universalDuration?: number;
            giftBalance?: number;
            totalCost?: number;
          }>;
          code: number;
          msg: string;
        }>
      >(
        config?: Config
      ): Alova2Method<
        {
          total: number;
          rows: Array<{
            createBy?: null;
            createTime?: string;
            updateBy?: null;
            updateTime?: string;
            remark?: null;
            id?: number;
            userId?: number;
            deptId?: null;
            ttsDuration?: number;
            s1LevelAmount?: number;
            aiPanting?: number;
            aiCartoon?: number;
            duration?: number;
            e0LevelAmount?: number;
            totalAmount?: number;
            trainTime?: number;
            balance?: number;
            s0LevelAmount?: number;
            privateDuration?: number;
            durationCost?: number;
            universalDuration?: number;
            giftBalance?: number;
            totalCost?: number;
          }>;
          code: number;
          msg: string;
        },
        'general.get_tabaccount_tabaccount_list',
        Config
      >;
      /**
       * ---
       *
       * [POST] 充值接口
       *
       * **path:** /api/wechatPay/createOrder
       *
       * ---
       *
       * **Query Parameters**
       * ```ts
       * type QueryParameters = {
       *   id?: string
       * }
       * ```
       *
       * ---
       *
       * **Response**
       * ```ts
       * type Response = object
       * ```
       */
      post_api_wechatpay_createorder<
        Config extends Alova2MethodConfig<object> & {
          params: {
            id?: string;
          };
        }
      >(
        config: Config
      ): Alova2Method<object, 'general.post_api_wechatpay_createorder', Config>;
      /**
       * ---
       *
       * [POST] 添加钻石配置
       *
       * **path:** /diamond/config/add
       *
       * ---
       *
       * **RequestBody**
       * ```ts
       * type RequestBody = {
       *   // 钻石数量（允许小数2位）
       *   diamondCount: number
       *   // 金额（元）
       *   amount: string
       * }
       * ```
       *
       * ---
       *
       * **Response**
       * ```ts
       * type Response = object
       * ```
       */
      post_diamond_config_add<
        Config extends Alova2MethodConfig<object> & {
          data: {
            /**
             * 钻石数量（允许小数2位）
             */
            diamondCount: number;
            /**
             * 金额（元）
             */
            amount: string;
          };
        }
      >(
        config: Config
      ): Alova2Method<object, 'general.post_diamond_config_add', Config>;
      /**
       * ---
       *
       * [DELETE] 删除钻石配置
       *
       * **path:** /diamond/config/2
       *
       * ---
       *
       * **Response**
       * ```ts
       * type Response = {
       *   msg: string
       *   code: number
       * }
       * ```
       */
      delete_diamond_config_2<
        Config extends Alova2MethodConfig<{
          msg: string;
          code: number;
        }>
      >(
        config?: Config
      ): Alova2Method<
        {
          msg: string;
          code: number;
        },
        'general.delete_diamond_config_2',
        Config
      >;
      /**
       * ---
       *
       * [GET] 获取钻石配置详情
       *
       * **path:** /diamond/config/{id}
       *
       * ---
       *
       * **Path Parameters**
       * ```ts
       * type PathParameters = {
       *   // id主键
       *   id: number
       * }
       * ```
       *
       * ---
       *
       * **Response**
       * ```ts
       * type Response = object
       * ```
       */
      get_diamond_config_id<
        Config extends Alova2MethodConfig<object> & {
          pathParams: {
            /**
             * id主键
             */
            id: number;
          };
        }
      >(
        config: Config
      ): Alova2Method<object, 'general.get_diamond_config_id', Config>;
      /**
       * ---
       *
       * [PUT] 修改钻石配置
       *
       * **path:** /diamond/config/4
       *
       * ---
       *
       * **RequestBody**
       * ```ts
       * type RequestBody = {
       *   // 主键
       *   id: number
       *   // 钻石个数
       *   diamondCount: number
       *   // 金额
       *   amount: number
       *   // 有效状态 1有效 0无效
       *   status: number
       * }
       * ```
       *
       * ---
       *
       * **Response**
       * ```ts
       * type Response = {
       *   msg: string
       *   code: number
       * }
       * ```
       */
      put_diamond_config_4<
        Config extends Alova2MethodConfig<{
          msg: string;
          code: number;
        }> & {
          data: {
            /**
             * 主键
             */
            id: number;
            /**
             * 钻石个数
             */
            diamondCount: number;
            /**
             * 金额
             */
            amount: number;
            /**
             * 有效状态 1有效 0无效
             */
            status: number;
          };
        }
      >(
        config: Config
      ): Alova2Method<
        {
          msg: string;
          code: number;
        },
        'general.put_diamond_config_4',
        Config
      >;
      /**
       * ---
       *
       * [GET] 查询钻石配置列表
       *
       * **path:** /diamond/config/list
       *
       * ---
       *
       * **Query Parameters**
       * ```ts
       * type QueryParameters = {
       *   pageNum?: number
       *   pageSize?: number
       * }
       * ```
       *
       * ---
       *
       * **Response**
       * ```ts
       * type Response = {
       *   total: number
       *   // [items] start
       *   // [items] end
       *   rows: Array<{
       *     createBy?: null
       *     createTime?: string
       *     updateBy?: null
       *     updateTime?: null
       *     remark?: null
       *     id?: number
       *     diamondCount?: number
       *     amount?: number
       *     status?: number
       *   }>
       *   code: number
       *   msg: string
       * }
       * ```
       */
      get_diamond_config_list<
        Config extends Alova2MethodConfig<{
          total: number;
          rows: Array<{
            createBy?: null;
            createTime?: string;
            updateBy?: null;
            updateTime?: null;
            remark?: null;
            id?: number;
            diamondCount?: number;
            amount?: number;
            status?: number;
          }>;
          code: number;
          msg: string;
        }> & {
          params: {
            pageNum?: number;
            pageSize?: number;
          };
        }
      >(
        config: Config
      ): Alova2Method<
        {
          total: number;
          rows: Array<{
            createBy?: null;
            createTime?: string;
            updateBy?: null;
            updateTime?: null;
            remark?: null;
            id?: number;
            diamondCount?: number;
            amount?: number;
            status?: number;
          }>;
          code: number;
          msg: string;
        },
        'general.get_diamond_config_list',
        Config
      >;
      /**
       * ---
       *
       * [POST] 添加配置
       *
       * **path:** /otherConfig/otherConfig/add
       *
       * ---
       *
       * **Response**
       * ```ts
       * type Response = {
       *   msg: string
       *   code: number
       * }
       * ```
       */
      post_otherconfig_otherconfig_add<
        Config extends Alova2MethodConfig<{
          msg: string;
          code: number;
        }>
      >(
        config?: Config
      ): Alova2Method<
        {
          msg: string;
          code: number;
        },
        'general.post_otherconfig_otherconfig_add',
        Config
      >;
      /**
       * ---
       *
       * [GET] 获取列表
       *
       * **path:** /otherConfig/otherConfig/list
       *
       * ---
       *
       * **Query Parameters**
       * ```ts
       * type QueryParameters = {
       *   pageNum?: number
       *   pageSize?: number
       * }
       * ```
       *
       * ---
       *
       * **Response**
       * ```ts
       * type Response = {
       *   total: number
       *   // [items] start
       *   // [items] end
       *   rows: Array<{
       *     createBy: null
       *     createTime: string
       *     updateBy: null
       *     updateTime: null
       *     remark: null
       *     id: number
       *     durationSeconds: number
       *     diamondCount: number
       *     status: number
       *     type: number
       *   }>
       *   code: number
       *   msg: string
       * }
       * ```
       */
      get_otherconfig_otherconfig_list<
        Config extends Alova2MethodConfig<{
          total: number;
          rows: Array<{
            createBy: null;
            createTime: string;
            updateBy: null;
            updateTime: null;
            remark: null;
            id: number;
            durationSeconds: number;
            diamondCount: number;
            status: number;
            type: number;
          }>;
          code: number;
          msg: string;
        }> & {
          params: {
            pageNum?: number;
            pageSize?: number;
          };
        }
      >(
        config: Config
      ): Alova2Method<
        {
          total: number;
          rows: Array<{
            createBy: null;
            createTime: string;
            updateBy: null;
            updateTime: null;
            remark: null;
            id: number;
            durationSeconds: number;
            diamondCount: number;
            status: number;
            type: number;
          }>;
          code: number;
          msg: string;
        },
        'general.get_otherconfig_otherconfig_list',
        Config
      >;
      /**
       * ---
       *
       * [GET] 获取配置详情
       *
       * **path:** /otherConfig/otherConfig/{id}
       *
       * ---
       *
       * **Path Parameters**
       * ```ts
       * type PathParameters = {
       *   // id主键
       *   id: number
       * }
       * ```
       *
       * ---
       *
       * **Response**
       * ```ts
       * type Response = {
       *   msg: string
       *   code: number
       *   data: {
       *     createBy: null
       *     createTime: string
       *     updateBy: null
       *     updateTime: null
       *     remark: null
       *     id: number
       *     durationSeconds: number
       *     diamondCount: number
       *     status: number
       *     type: number
       *   }
       * }
       * ```
       */
      get_otherconfig_otherconfig_id<
        Config extends Alova2MethodConfig<{
          msg: string;
          code: number;
          data: {
            createBy: null;
            createTime: string;
            updateBy: null;
            updateTime: null;
            remark: null;
            id: number;
            durationSeconds: number;
            diamondCount: number;
            status: number;
            type: number;
          };
        }> & {
          pathParams: {
            /**
             * id主键
             */
            id: number;
          };
        }
      >(
        config: Config
      ): Alova2Method<
        {
          msg: string;
          code: number;
          data: {
            createBy: null;
            createTime: string;
            updateBy: null;
            updateTime: null;
            remark: null;
            id: number;
            durationSeconds: number;
            diamondCount: number;
            status: number;
            type: number;
          };
        },
        'general.get_otherconfig_otherconfig_id',
        Config
      >;
      /**
       * ---
       *
       * [PUT] 修改配置
       *
       * **path:** /otherConfig/otherConfig
       *
       * ---
       *
       * **RequestBody**
       * ```ts
       * type RequestBody = {
       *   id: number
       *   durationSeconds: number
       *   diamondCount: number
       *   status: number
       *   type: number
       * }
       * ```
       *
       * ---
       *
       * **Response**
       * ```ts
       * type Response = {
       *   msg: string
       *   code: number
       * }
       * ```
       */
      put_otherconfig_otherconfig<
        Config extends Alova2MethodConfig<{
          msg: string;
          code: number;
        }> & {
          data: {
            id: number;
            durationSeconds: number;
            diamondCount: number;
            status: number;
            type: number;
          };
        }
      >(
        config: Config
      ): Alova2Method<
        {
          msg: string;
          code: number;
        },
        'general.put_otherconfig_otherconfig',
        Config
      >;
      /**
       * ---
       *
       * [DELETE] 删除配置
       *
       * **path:** /otherConfig/otherConfig/{ids}
       *
       * ---
       *
       * **Path Parameters**
       * ```ts
       * type PathParameters = {
       *   // Long[] 主键
       *   ids: number
       * }
       * ```
       *
       * ---
       *
       * **RequestBody**
       * ```ts
       * type RequestBody = {
       *   id: number
       *   durationSeconds: number
       *   diamondCount: number
       *   status: number
       *   type: number
       * }
       * ```
       *
       * ---
       *
       * **Response**
       * ```ts
       * type Response = {
       *   msg: string
       *   code: number
       * }
       * ```
       */
      delete_otherconfig_otherconfig_ids<
        Config extends Alova2MethodConfig<{
          msg: string;
          code: number;
        }> & {
          pathParams: {
            /**
             * Long[] 主键
             */
            ids: number;
          };
          data: {
            id: number;
            durationSeconds: number;
            diamondCount: number;
            status: number;
            type: number;
          };
        }
      >(
        config: Config
      ): Alova2Method<
        {
          msg: string;
          code: number;
        },
        'general.delete_otherconfig_otherconfig_ids',
        Config
      >;
    };
    token: {
      /**
       * ---
       *
       * [POST] 登录
       *
       * **path:** /login
       *
       * ---
       *
       * **RequestBody**
       * ```ts
       * type RequestBody = {
       *   // 账号
       *   username: string
       *   // 密码
       *   password: string
       *   // 验证码
       *   code: string
       *   // 验证码唯一标识
       *   uuid: string
       * }
       * ```
       *
       * ---
       *
       * **Response**
       * ```ts
       * type Response = {
       *   // 状态码
       *   code?: number
       *   // 返回消息
       *   msg?: string
       *   // 认证token
       *   token?: string
       * }
       * ```
       */
      post_login<
        Config extends Alova2MethodConfig<{
          /**
           * 状态码
           */
          code?: number;
          /**
           * 返回消息
           */
          msg?: string;
          /**
           * 认证token
           */
          token?: string;
        }> & {
          data: {
            /**
             * 账号
             */
            username: string;
            /**
             * 密码
             */
            password: string;
            /**
             * 验证码
             */
            code: string;
            /**
             * 验证码唯一标识
             */
            uuid: string;
          };
        }
      >(
        config: Config
      ): Alova2Method<
        {
          /**
           * 状态码
           */
          code?: number;
          /**
           * 返回消息
           */
          msg?: string;
          /**
           * 认证token
           */
          token?: string;
        },
        'token.post_login',
        Config
      >;
    };
  }

  var Apis: Apis;
}
