<script lang="ts" setup="">
import type { RouteLocationRaw } from 'vue-router';

const useStoreApp = storeApp();
const { tabs } = pinia.storeToRefs(useStoreApp);
const sets = computed(
	(): TsTabs.Sets => ({
		type: "card",
		closable: tabs.value.length > 1,
	})
);
function onTab(pane: TsElementPlus.TabsPaneContext) {
	const { routeInfo } = useStoreApp;
	const paneName = pane.paneName as string;
	const tabRouteInfo = routeInfo[paneName];
	
	const to: RouteLocationRaw = {
		name: paneName,
		...(tabRouteInfo && {
			params: tabRouteInfo.params,
			query: tabRouteInfo.query
		})
	};

	router.push(to);
}
function onMove(name: TsMenu.Model) {
	let index = tabs.value.findIndex((tab) => tab.name === name);
	useStoreApp.tabsRemoved(name);
	if (index == tabs.value.length) index--;
	router.push({
		name: tabs.value[index].name,
	});
}
</script>
<template>
	<base-tabs v-model="$route.name" :options="tabs" :sets="sets" class="the-tabs" @tab-click="onTab" @tab-remove="onMove"></base-tabs>
</template>
<style scoped>
.the-tabs {
	padding: 0;
}
.the-tabs :deep(> .el-tabs__header) {
	margin-bottom: 0;
	border-bottom: none;
}
.the-tabs :deep(.el-tabs--card > .el-tabs__header) {
	border-bottom: none;
}
.the-tabs :deep(> .el-tabs__header .el-tabs__item) {
	border-bottom: none;
}
.the-tabs :deep(> .el-tabs__header .el-tabs__item.is-active) {
	background-color: var(--el-bg-color-page);
}
</style>
