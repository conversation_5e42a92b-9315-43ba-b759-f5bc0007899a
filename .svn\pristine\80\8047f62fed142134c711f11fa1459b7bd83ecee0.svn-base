<script  lang="ts" setup="">
const {sets = {}} = defineProps<{
    options: TsTabs.Options;
    sets?: TsTabs.Sets;
}>();
</script>
<template>
    <el-tabs
        v-bind="sets"
        class="base-tabs"
    >
        <el-tab-pane
            v-for="item in options"
            :key="item.name"
            :name="item.name"
        >
            <template #label>
                <div class="tab-border">
                    <div class="left-out"></div>
                    <div class="left-in"></div>
                </div>
                <slot :name="item.name + 'Label'">
                    <base-icon :icon="item.icon" v-if="item.icon"></base-icon>
                    <span>{{item.label}}</span>
                </slot>
                <div class="tab-border">
                    <div class="right-out"></div>
                    <div class="right-in"></div>
                </div>
            </template>
            <slot :name="item.name" v-bind="item"></slot>
        </el-tab-pane>
    </el-tabs>
</template>
<style scoped>
.base-tabs :deep(.el-tabs__item > .base-icon) {
    margin-right: 5px;
}
.base-tabs :deep(.el-tabs__nav),
.base-tabs :deep(.el-tabs__item) {
    border: none;
}
.base-tabs :deep(.el-tabs__nav-scroll) {
    padding: 0 10px;
}
.base-tabs :deep(.el-tabs__item:not(.is-active) + .el-tabs__item:not(.is-active)::before),
.base-tabs :deep(.el-tabs__item:not(.is-active):first-child::before) {
    content: "";
    display: block;
    width: 1px;
    height: 50%;
    background-color: var(--el-border-color);
    position: absolute;
    left: 0;
    top: 25%;
}
.base-tabs :deep(.el-tabs__item:not(.is-active):last-child::after) {
    content: "";
    display: block;
    width: 1px;
    height: 50%;
    background-color: var(--el-border-color);
    position: absolute;
    right: 0;
    top: 25%;
}
.base-tabs :deep(.el-tabs__item:not(.is-active) .tab-border) {
    display: none;
}
.left-out {
    width: 10px;
    height: 50%;
    left: -10px;
    bottom: 0;
    position: absolute;
    background-color: var(--el-bg-color-page);
}
.left-out::before {
    content: "";
    display: block;
    width: 10px;
    height: 100%;
    position: absolute;
    left: 0;
    bottom: 1px;
    background-color: var(--el-bg-color);
    border-radius: 0 0 10px 0;
}
.left-in {
    width: 10px;
    height: 50%;
    left: 0;
    top: 0;
    position: absolute;
    background-color: var(--el-bg-color);
}
.left-in::before {
    content: "";
    display: block;
    width: 10px;
    height: 100%;
    position: absolute;
    left: 0;
    top: 0;
    background-color: var(--el-bg-color-page);
    border-radius: 10px 0 0 0;
}
.right-out {
    width: 10px;
    height: 50%;
    right: -10px;
    bottom: 0;
    position: absolute;
    background-color: var(--el-bg-color-page);
}
.right-out::before {
    content: "";
    display: block;
    width: 10px;
    height: 100%;
    position: absolute;
    right: 0;
    bottom: 1px;
    background-color: var(--el-bg-color);
    border-radius: 0 0 0 10px;
}
.right-in {
    width: 10px;
    height: 50%;
    right: 0;
    top: 0;
    position: absolute;
    background-color: var(--el-bg-color);
}
.right-in::before {
    content: "";
    display: block;
    width: 10px;
    height: 100%;
    position: absolute;
    right: 0;
    top: 0;
    background-color: var(--el-bg-color-page);
    border-radius: 0 10px 0 0;
}
</style>