<script setup lang="ts">
import './dialogInside.scss'
import pointGoodCard from './pointGoodCard.vue';

defineProps<{
    balance: string
}>();

const emit = defineEmits<{
    updateChoose: [point: number],
}>();

provide('unit', '次');

// 充值选项
const rechargeOptions = [
    { time: 1, price: 3980, unit: '次'},
    { time: 2, price: 7960, unit: '次'},
    { time: 3, price: 11940, unit: '次'},
    { time: 5, price: 19900, unit: '次'}
].map((i, index) => ({
    ...i,
    id: index
}));

const remainingPoint = ref(rechargeOptions[0]);

// 选择充值时长
function handelClick(item: typeof rechargeOptions[number]) {
    remainingPoint.value = item;
    emit('updateChoose', item.price);
}

onMounted(() => {
    emit('updateChoose', remainingPoint.value.price);
})
</script>

<template>
    <!-- 信息显示 -->
    <div class="recharge-dialog-inside-info">
        <span>声音克隆·S级次数：{{ balance }}</span>
    </div>

    <!-- 充值选项网格 -->
    <div class="recharge-dialog-inside-grid">
        <pointGoodCard
            v-for="option in rechargeOptions"
            :key="option.id"
            :price="option.price"
            :num="option.time"
            :is-active="remainingPoint.id == option.id"
            @click="handelClick(option)"
        />
    </div>
</template>