// 模型类型映射
export const typeNameMap = new Map([
    [0, '绿幕'],
    [1, '其他'],
] as const);

// 性别映射
export const genderMap = new Map([
    [1, '♂️'],
    [2, '♀️'],
] as const);

// 类型映射
export const versionMap = new Map([
    [0, '2D'],
    [1, '3D'],
] as const);

// 星座映射
export const starSignsMap = new Map([
    [1, '白羊座 ♈️'],
    [2, '金牛座 ♉️'],
    [3, '双子座 ♊️'],
    [4, '巨蟹座 ♋️'],
    [5, '狮子座 ♌️'],
    [6, '处女座 ♍️'],
    [7, '天秤座 ♎️'],
    [8, '天蝎座 ♏️'],
    [9, '射手座 ♐️'],
    [10, '摩羯座 ♑️'],
    [11, '水瓶座 ♒️'],
    [12, '双鱼座 ♓️'],
] as const);

export const proportionOptions = [
    {
        label: '全部',
        value: 'all',
    },
    {
        label: '横版',
        value: 'horizontal',
    },
    {
        label: '竖版',
        value: 'vertical',
    }
];

export const cloneOption = [
    { label: '全部', value: 'all' },
    { label: '绿幕', value: 'green_screen' },
    { label: '商务', value: 'business' },
    { label: '休闲', value: 'casual' },
]
