const ModelApis = {
    getList(params: TsApis.ApiParamsWithoutPage<"general", "post_aivatarapi_robotlist"> = {}) {
        const method = (page: number, size: number) => Apis.general['post_aivatarapi_robotlist']({
            name: "post_aivatarapi_robotlist",
            params: {
                ...params,
                page,
                size,
            },
            transform: (res) => ({
                total: res.total ?? 0,
                data: res.rows ?? [],
            }),
        })

        return alova.usePagination(method, {
            watchingStates: [params, toRef(params as any, 'search')],
            debounce: [0, 300],
            initialPage: 1,
            initialPageSize: 10,
            immediate: true,
            initialData: {
                total: 0,
                data: [],
            },
        })
    },

    getDetail() {
        null
    },

    liek() {
        null
    },

    favorit() {
        null
    }
} as const;

export default ModelApis;
