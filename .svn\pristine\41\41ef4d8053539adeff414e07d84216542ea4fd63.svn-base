<script setup lang="ts">
const { balace } = pinia.storeToRefs(storeUser());
const activeName = ref<"recharge" | "spend" | "work">("recharge");
const optionsSegmented = [
	{ label: "充值详情", value: "recharge" },
	{ label: "钻石消耗", value: "spend" },
    { label: "作品消耗", value: "work" },
];
// 充值记录
const { data: rechargeData, page: rechargePage, pageSize: rechargePageSize, total: rechargeTotal, loading: rechargeLoading, refresh: rechargeRefresh } = apiMyOrder.recharge();

// 钻石消耗
const { data: spendData, page: spendPage, pageSize: spendPageSize, total: spendTotal, loading: spendLoading, refresh: spendRefresh } = apiMyOrder.spend();

// 作品消耗
const { data: workData, page: workPage, pageSize: workPageSize, total: workTotal, loading: workLoading, refresh: workRefresh } = apiMyOrder.work();
// 切换标签获取数据
const handelTabChaneg = (val: string | number) => {
	switch (val) {
		case "recharge":
			rechargeRefresh();
			break;
		case "spend":
			spendRefresh();
			break;
		case "work":
			workRefresh();
			break;
	}
};

// 充值弹窗相关
const rechargeDialog = storeRechargeDialog();
</script>

<template>
	<layout-table>
		<template #static>
			<div class="balance">
				<div class="balance-card">
					<div class="balance-item">
						<div class="left">
							<div class="balance-label">钻石余额</div>
							<div class="balance-value">{{ balace.diamondBalance }}</div>
						</div>
						<div class="balance-action">
							<base-button size="small">去充值</base-button>
						</div>
					</div>
				</div>
				<div class="balance-card">
					<div class="balance-item">
						<div class="left">
							<div class="balance-label">视频时长余额</div>
							<div class="balance-value">{{ balace.videoDuration }}秒</div>
						</div>
						<div class="balance-action">
							<base-button size="small" @click="rechargeDialog.open('general_video_duration')">去充值</base-button>
						</div>
					</div>
				</div>
				<div class="balance-card">
					<div class="balance-item">
						<div class="left">
							<div class="balance-label">音频时长余额</div>
							<div class="balance-value">{{ balace.audioDuration }}秒</div>
						</div>
						<div class="balance-action">
							<base-button size="small" @click="rechargeDialog.open('audio_duration')">去充值</base-button>
						</div>
					</div>
				</div>
				<div class="balance-card">
					<div class="balance-item">
						<div class="left">
							<div class="balance-label">S级形象克隆余额</div>
							<div class="balance-value">{{ balace.avatarCloneS }}次</div>
						</div>
						<div class="balance-action">
							<base-button size="small">去充值</base-button>
						</div>
					</div>
				</div>
				<div class="balance-card">
					<div class="balance-item">
						<div class="left">
							<div class="balance-label">E级形象克隆v</div>
							<div class="balance-value">{{ balace.avatarCloneE }}次</div>
						</div>
						<div class="balance-action">
							<base-button size="small" @click="rechargeDialog.open('image_clone_e')">去充值</base-button>
						</div>
					</div>
				</div>
				<div class="balance-card">
					<div class="balance-item">
						<div class="left">
							<div class="balance-label">E级声音余额</div>
							<div class="balance-value">{{ balace.voiceCloneE }}次</div>
						</div>
						<div class="balance-action">
							<base-button size="small">去充值</base-button>
						</div>
					</div>
				</div>
			</div>
		</template>
		<template #form>
			<el-segmented v-model="activeName" :options="optionsSegmented" @change="handelTabChaneg" class="tabs" />
		</template>
		<template #table>
			<my-order-recharge-table v-if="activeName == 'recharge'" :data="rechargeData" :loading="rechargeLoading" />
			<my-order-spend-table v-else-if="activeName == 'spend'" :data="spendData" :loading="spendLoading" />
			<my-order-work-table v-else :data="workData" :loading="workLoading" />
		</template>
		<template #pagination>
			<base-pagination v-if="activeName == 'recharge'" v-model:current-page="rechargePage" v-model:page-size="rechargePageSize" :total="rechargeTotal" />
			<base-pagination v-else-if="activeName == 'spend'" v-model:current-page="spendPage" v-model:page-size="spendPageSize" :total="spendTotal" />
			<base-pagination v-else v-model:current-page="workPage" v-model:page-size="workPageSize" :total="workTotal" />
		</template>
	</layout-table>
</template>
<style scoped lang="scss">
.balance {
	display: flex;
	gap: var(--el-gap);
	margin-bottom: var(--el-gap-half);
	background-color: var(--el-bg-color);
}

.balance-card {
	flex: 1;
	background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
	border-radius: var(--el-border-radius-base);
	padding: var(--el-gap);
	color: white;
	position: relative;
	overflow: hidden;
}

.balance-card:nth-child(1) {
	background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.balance-card:nth-child(2) {
	background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
}

.balance-card:nth-child(3) {
	background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
}

.balance-card:nth-child(4) {
	background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
}

.balance-card:nth-child(5) {
	background: linear-gradient(135deg, #fa709a 0%, #fee140 100%);
}

.balance-item {
	display: flex;
	height: 100%;
	justify-content: space-between;
	align-items: center;
}

.balance-label {
	font-size: var(--el-font-size-small);
	opacity: 0.9;
	margin-bottom: var(--el-gap-half);
}

.balance-value {
	font-size: var(--el-font-size-extra-large);
	font-weight: bold;
	margin-bottom: var(--el-gap);
}

.balance-action {
	margin-right: var(--el-gap);
}

.balance-action .base-button {
	background-color: rgba(255, 255, 255, 0.2);
	border: 1px solid rgba(255, 255, 255, 0.3);
	color: white;
	font-size: var(--el-font-size-small);
}

.balance-action .base-button:hover {
	background-color: rgba(255, 255, 255, 0.3);
	border-color: rgba(255, 255, 255, 0.5);
}
</style>