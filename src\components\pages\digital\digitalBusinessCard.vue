<script lang="ts" setup>
const {
  data
} = defineProps<{
  data: TsApis.ApiResponse<"general", "post_aivatarapi_getdigitalcard">["rows"][number];
}>();

defineEmits<{
  clickUse: [id: TsModelMarket.ModleData['id']],
}>();

const isCover = ref(false);

const video = useTemplateRef<HTMLVideoElement>('videoRef');

const mouseMoveIn = () => {
  isCover.value = true;
  video.value!.play().catch(null);
}

const mouseMoveOut = () => {
  isCover.value = false;
  video.value!.pause();
  lowPriorityFn(() => {
    video.value!.currentTime = 0;
  })
}

// url处理
const baseURL = import.meta.env.VITE_BASE_DIGITAL_URL;

const URL = shallowRef({
  video: baseURL + data.videoCoverUrl,
  image: baseURL + data.coverUrl,
})

defineExpose({
  name: 'DigitalBusinessCard'
})
</script>

<template>
  <div class="card">
    <div class="video" @mouseenter="() => mouseMoveIn()" @mouseleave="() => mouseMoveOut()">
      <img
          v-show="!isCover"
          :alt="data.themeName ?? ''"
          :src="URL.image"
      />
      <video
          v-show="isCover"
          ref="videoRef"
          preload="none"
          controls
          muted
          height="194"
          width="346"
          :poster="URL.image"
          disablepictureinpicture
      >
        <source :src="URL.video" type="video/mp4"/>
      </video>
    </div>
    <div class="down">
      <div class="name-use">
        <span class="name">
            <auto-tooltip :content="data.themeName!" placement="bottom" />
        </span>
        <el-button type="primary" size="small" @click="$emit('clickUse', data.id)">使用</el-button>
      </div>
<!--      <div class="popularity">
        <base-icon class="icon" icon="solar:fire-bold"/>
        <span class="num">{{ data.fakePopularity || 0 }}</span>
      </div>-->
    </div>
  </div>
</template>

<style lang="scss" scoped>
$fire-color: rgb(255, 79, 79);
$video-height: 194px;

.card {
  position: relative;
  width: 346px;
  border-radius: var(--el-border-radius-base);
  overflow: hidden;

  .video {
    height: $video-height;
    width: 100%;
    object-fit: cover;
    margin: 0 auto;
    overflow-clip-margin: content-box;
    overflow: clip;
  }

  .down {
    background: var(--el-bg-color);
    font-size: var(--el-font-size-base);
    padding: var(--el-gap-half);

    .name-use {
      display: flex;
      justify-content: space-between;
      align-items: center;

      .name {
        line-height: 16px;
        max-width: 56px;
      }
    }

    .popularity {
      height: 18px;
      margin-top: 8px;
      color: $fire-color;
      display: flex;
      justify-content: flex-start;
      align-items: center;
      font-size: var(--el-font-size-extra-small);

      .icon {
        :deep(svg) {
          height: var(--el-font-size-extra-small);
          width: var(--el-font-size-extra-small);
        }
      }

      .num {
        margin-left: var(--el-gap-half);
      }
    }
  }
}
</style>
