<script lang="ts" setup>
import CONST from '@/assets/const/myOrder';
const stroe = storeRechargeDialog();

// 组件与tab定义对象
const tabComponentMap: Record<TsMyOrder.Dialog.tab, TsMyOrder.Dialog.insideComponents> = {
    recharge_point: defineAsyncComponent(() => import('./subRechargeComponents/rechargePoingInside.vue')),
    general_video_duration: defineAsyncComponent(() => import('./subRechargeComponents/genetalVideoDurationInside.vue')),
    audio_duration: defineAsyncComponent(() => import('./subRechargeComponents/audioDurationInside.vue')),
    image_clone_s: defineAsyncComponent(() => import('./subRechargeComponents/imageCloneSInside.vue')),
    image_clone_e: defineAsyncComponent(() => import('./subRechargeComponents/imageCloneEInside.vue')),
    image_clone_s_hd: defineAsyncComponent(() => import('./subRechargeComponents/imageCloneSHdInside.vue')),
    voice_clone_e: defineAsyncComponent(() => import('./subRechargeComponents/voiceCloneEInside.vue')),
    voice_clone_s: defineAsyncComponent(() => import('./subRechargeComponents/voiceCloneSInside.vue')),
}

Object.freeze(tabComponentMap);

const visible = computed({
    get() {
        return stroe.visible;
    },
    set(val) {
        stroe.visible = val;
    }
})

// tag相关
const typeOption = shallowRef(CONST.rechargeDialog.tabs);
const showComponent = computed<TsMyOrder.Dialog.insideComponents>(() => tabComponentMap[stroe.tab]);
const currentBalance = ref('0');
const handelClickTab = (tab: TsMyOrder.Dialog.tab) => {
    stroe.tab = tab;
    currentBalance.value = CONST.rechargeDialog.fakeData.map[tab];
}

// 积分处理
const nowChoosePoint = ref(1);
const currentBalancePoint = ref(0);

function updateChoose(point: number) {
    nowChoosePoint.value = point;
}

// 支付二维码相关
const qrcode = reactive({
    orderNum: '',
    visible: false,
    params: undefined as TsMyOrder.Dialog.fetchParam | undefined,
})

const handlePayConfirm = (orderNum: string) => {
    qrcode.orderNum = orderNum;
}

const openQrcode = (params?: TsMyOrder.Dialog.fetchParam) => {
    qrcode.params = params;
    qrcode.visible = true;
}

// 确认购买
function handleConfirm() {
    if (stroe.tab === 'recharge_point') {
        openQrcode({ id: 114514 });
    } else {
        stroe.close();
    }    
}

// 取消
function handleCancel() {
    stroe.close();
}
</script>

<template>
    <el-dialog
        class="recharge-dialog"
        v-model="visible"
        width="600px"
        :show-close="false"
    >
        <div class="recharge-dialog">
            <header>
                <div class="title">充值购买</div>
                <base-icon
                    class="close-btn"
                    icon="solar:close-square-outline"
                    @click="handleCancel"
                />
            </header>
            <!-- 顶部导航 -->
            <div class="nav-tabs">
                <div
                    v-for="item in typeOption"
                    :key="item.value"
                    class="nav-item"
                    :class="{ 'active': item.value === stroe.tab }"
                    @click="() => handelClickTab(item.value)"
                >
                    {{ item.label }}
                </div>
            </div>

            <Suspense>
                <template #default>
                    <component
                        :is="showComponent"
                        :balance="currentBalance"
                        @update-choose="updateChoose"
                    />
                </template>
                <template #fallback>
                    <div class="loading-placeholder">
                        <div class="loading-spinner"></div>
                        <span>加载中...</span>
                    </div>
                </template>
            </Suspense>

            <!-- 底部信息 -->
            <div class="bottom-info">
                <div class="balance-info" v-if="stroe.tab !== 'recharge_point'">
                    <span class="balance-label">积分余额：</span>
                    <base-icon icon="solar:verified-check-line-duotone" />
                    <span class="balance-value">{{ currentBalancePoint }}</span>
                    <span class="balance-status" v-if="nowChoosePoint > currentBalancePoint">
                        (积分余额不足，无法购买支付，建议前往充值)
                    </span>
                </div>

                <div class="down">
                    <button class="recharge-btn" @click="handleConfirm">
                        立即购买
                    </button>
                    <div class="terms">
                        <p>1.您购买的商品为虚拟服务，购买后不支持退订、转让；</p>
                        <template v-if="stroe.tab === 'audio_duration'">
                            <p>2.您购买的时长只会在合成视频时消耗，视频合成不成功不消耗时长；</p>
                            <p>3.购买后，可在我的订单中查看。</p>
                        </template>
                        <template v-else> 
                            <p>2.购买后，可在我的订单中查看。</p>
                        </template>
                    </div>  
                </div>

            </div>
        </div>
    </el-dialog>
    <dialog-recharge-scan-qr-code
        v-model="qrcode.visible"
        :fetch-param="qrcode.params"
        @confirm="handlePayConfirm"
        @close="null"
    />
</template>

<style lang="scss">
.el-dialog.recharge-dialog {
    padding: 0;
    border-radius: var(--el-border-radius-round);

    header {
        padding: 0;
    }
}
</style>

<style lang="scss" scoped>
.recharge-dialog {
    --mix-color: var(--mix-color);
    background: linear-gradient(135deg, #ff9a56 0%, #ff6b35 100%);
    border-radius: var(--el-border-radius-round);
    padding: 20px;
    color: white;
    position: relative;
    overflow: hidden;

    header {
        width: 100%;
        height: 60px;
        display: flex;
        align-items: center;
        padding-bottom: var(--el-gap);
        position: relative;

        .title {
            font-size: 30px;
            font-weight: bold;
        }

        .close-btn {
            position: absolute;
            top: var(--el-gap-half);
            right: var(--el-gap-half); 
            background: none;
            border: none;
            font-size: 24px;
            cursor: pointer;
            z-index: 2;

            &:hover {
                transition: transform 0.3s ease;
                transform: scale(1.1);
                }
            }
    }
}

.dark .recharge-dialog {
    --mix-color: 0, 0, 0;
}

.nav-tabs {
    display: flex;
    gap: 8px;
    margin-bottom: 20px;
    padding-bottom: 2px;
    overflow-x: scroll;
}

.nav-item {
    padding: 6px 12px;
    background: rgba(var(--mix-color), 0.2);
    border-radius: 20px;
    font-size: 12px;
    cursor: pointer;
    transition: all 0.3s ease;
    white-space: nowrap;
}

.nav-item.active {
    background: rgba(255, 255, 255, 0.7);
    color: #ff6b35;
    font-weight: bold;
}

.nav-item:hover:not(.active) {
    background: rgba(var(--mix-color), 0.3);
}

.remaining-time {
    background: rgba(var(--mix-color), 0.15);
    border-radius: 8px;
    padding: 12px 16px;
    margin-bottom: 20px;
    font-size: 14px;
}

.recharge-grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 12px;
    margin-bottom: 20px;
}

.bottom-info {
    background: rgba(var(--mix-color), 0.1);
    border-radius: 8px;
    padding: 16px;
}

.balance-info {
    display: flex;
    align-items: center;
    gap: 6px;
    margin-bottom: 12px;
    font-size: 14px;
}

.balance-label {
    font-weight: bold;
}

.balance-value {
    font-weight: bold;
    color: #fff;
}

.balance-status {
    color: #ffeb3b;
    font-size: 12px;
}

.down {
    width: 100%;
    display: flex;
    flex-direction: row-reverse;
    justify-content: space-between;
    height: 70px;

    .recharge-btn {
        flex-basis: 6em;
        display: flex;
        align-items: center;
        justify-content: center;
        height: 100%;
        font-size: var(--el-font-size-large);
        border: 1px solid rgba(255, 255, 255, 0.8);
        border-radius: 8px;
        background: none;
        color: #fff;
        cursor: pointer;

        &:not(:disabled):hover {
            background: rgba(255, 255, 255, 0.2);
        }

        &:disabled {
            background: rgba(255, 255, 255, 0.1);
            cursor: not-allowed;
        }
    }

    .terms {
        font-size: 12px;
        line-height: 1.5;
        opacity: 0.9;

        p {
            margin: 4px 0;
        }
    }
}

.loading-placeholder {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 40px 20px;
    gap: 12px;
    min-height: 200px;

    .loading-spinner {
        width: 32px;
        height: 32px;
        border: 3px solid rgba(255, 255, 255, 0.3);
        border-top: 3px solid rgba(255, 255, 255, 0.8);
        border-radius: 50%;
        animation: spin 1s linear infinite;
    }

    span {
        font-size: 14px;
        opacity: 0.8;
    }
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}
</style>
