<script lang="ts" setup>
import { typeNameMap } from '@/views/pages/digital/market/model/const';

const props = defineProps<{
  videoInfo: Partial<TsModelMarket.VideoType>;
}>();

const video = useTemplateRef<HTMLVideoElement>('videoRef');

const showData = computed(() => {
  // 获取视频时长并转换为"xx分xx秒"格式
  const duration = props.videoInfo.duration || 0;
  const minutes = Math.floor(duration / 60);
  const seconds = Math.floor(duration % 60);
  const formattedDuration = `${minutes}分${seconds}秒`;
  
  // 获取场景类型名称
  const sceneTypeName = typeNameMap.get(props.videoInfo.sceneType || 0) || '未知';
  
  // 返回转换后的数据
  return {
    sceneName: props.videoInfo.sceneName || '',
    sceneCode: props.videoInfo.sceneCode || '',
    proportion: props.videoInfo.proportion || '',
    description: props.videoInfo.description || '',
    videoUrl: props.videoInfo.videoUrl || '',
    coverUrl: props.videoInfo.coverUrl || '',
    coverMattingUrl: props.videoInfo.coverMattingUrl || null,
    duration: formattedDuration,
    sceneType: sceneTypeName
  };
})

</script>

<template>
  <div class="video-container">
    <!-- 视频信息区域 -->
    <div class="video-info">
      <div class="info-item">
        <span class="label">名称:</span>
        <span class="value">{{ showData.sceneName }}</span>
      </div>
      <div class="info-item">
        <span class="label">比例:</span>
        <span class="value">{{ showData.proportion }}</span>
      </div>
      <div class="info-item">
        <span class="label">类型:</span>
        <span class="value type-badge">{{ showData.sceneType }}</span>
      </div>
      <div class="info-item">
        <span class="label">时长:</span>
        <span class="value">{{ showData.duration }}</span>
      </div>
      <div class="info-item description">
        <span class="label">简介:</span>
        <span class="value">{{ showData.description }}</span>
      </div>
    </div>

    <!-- 视频播放区域 -->
    <div class="video-player">
      <video
        ref="videoRef"
        :src="showData.videoUrl"
        :poster="showData.coverUrl"
        class="video"
        controls
      ></video>
    </div>
  </div>
</template>

<style scoped lang="scss">
.video-container {
  width: 100%;
  max-width: 800px;
  margin: 0 auto;
  border-radius: var(--el-border-radius-middle, 8px);
  overflow: hidden;
}

.video-info {
  padding: var(--el-gap, 16px);
  background-color: var(--el-color-white, #fff);
  position: relative;

  .info-item {
    display: flex;
    margin-bottom: var(--el-gap-half, 8px);

    .label {
      width: 60px;
      font-weight: bold;
      color: var(--el-text-color-secondary, #666);
    }

    .value {
      flex: 1;
      color: var(--el-text-color-primary, #333);
    }

    .type-badge {
      display: inline-block;
      padding: 2px var(--el-gap-half, 8px);
      background-color: var(--el-color-success, #4caf50);
      color: var(--el-color-white, white);
      border-radius: var(--el-border-radius-base, 4px);
      font-size: 12px;
    }

    &.description {
      .value {
        white-space: normal;
        word-break: break-word;
      }
    }
  }

  .close-btn {
    position: absolute;
    top: var(--el-gap, 16px);
    right: var(--el-gap, 16px);
    background: none;
    border: none;
    font-size: 20px;
    cursor: pointer;
    color: var(--el-text-color-placeholder, #999);

    &:hover {
      color: var(--el-text-color-primary, #333);
    }
  }
}

.video-player {
  position: relative;
  width: 100%;
  background-color: var(--el-bg-color);

  .video {
    width: 100%;
    height: auto;
    display: block;
  }
}
</style>