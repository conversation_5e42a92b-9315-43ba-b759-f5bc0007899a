[{"id": 1, "orderNo": "SP202310260001", "productCategory": "Category A", "productName": "Product 1", "productAmount": 100.5, "productPrice": 10.0, "orderStatus": "Completed", "orderTime": "2023-10-26 10:00:00"}, {"id": 2, "orderNo": "SP202310260002", "productCategory": "Category B", "productName": "Product 2", "productAmount": 200.0, "productPrice": 20.0, "orderStatus": "Pending", "orderTime": "2023-10-26 11:00:00"}, {"id": 3, "orderNo": "SP202310260003", "productCategory": "Category A", "productName": "Product 3", "productAmount": 50.75, "productPrice": 5.0, "orderStatus": "Cancelled", "orderTime": "2023-10-26 12:00:00"}, {"id": 4, "orderNo": "SP202310260004", "productCategory": "Category C", "productName": "Product 4", "productAmount": 150.25, "productPrice": 15.0, "orderStatus": "Completed", "orderTime": "2023-10-26 13:00:00"}, {"id": 5, "orderNo": "SP202310260005", "productCategory": "Category B", "productName": "Product 5", "productAmount": 75.0, "productPrice": 7.5, "orderStatus": "Pending", "orderTime": "2023-10-26 14:00:00"}, {"id": 6, "orderNo": "SP202310260006", "productCategory": "Category A", "productName": "Product 6", "productAmount": 120.0, "productPrice": 12.0, "orderStatus": "Completed", "orderTime": "2023-10-26 15:00:00"}, {"id": 7, "orderNo": "SP202310260007", "productCategory": "Category C", "productName": "Product 7", "productAmount": 300.0, "productPrice": 30.0, "orderStatus": "Processing", "orderTime": "2023-10-26 16:00:00"}, {"id": 8, "orderNo": "SP202310260008", "productCategory": "Category B", "productName": "Product 8", "productAmount": 90.0, "productPrice": 9.0, "orderStatus": "Completed", "orderTime": "2023-10-26 17:00:00"}, {"id": 9, "orderNo": "SP202310260009", "productCategory": "Category A", "productName": "Product 9", "productAmount": 180.0, "productPrice": 18.0, "orderStatus": "Refunded", "orderTime": "2023-10-26 18:00:00"}, {"id": 10, "orderNo": "SP202310260010", "productCategory": "Category C", "productName": "Product 10", "productAmount": 250.0, "productPrice": 25.0, "orderStatus": "Completed", "orderTime": "2023-10-26 19:00:00"}]