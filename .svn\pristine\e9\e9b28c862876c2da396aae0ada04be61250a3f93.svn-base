<script setup lang="ts">
const props = defineProps<{
  option: TsDigitalAudio.Play
}>()

// 音频播放状态
const isPlaying = ref(false)
// 音频相关状态
const currentTime = ref(0)
const duration = ref(0)
const audioRef = ref<HTMLAudioElement>()

// 按钮设置
const listenSets = computed(() => ({
  disabled: !props.option.audioSrc,
  icon: isPlaying.value ? markRaw(IconSolarPauseBoldDuotone) : markRaw(IconSolarPlayBoldDuotone),
}))

// 格式化时间显示
const formatTime = (seconds: number): string => {
  const mins = Math.floor(seconds / 60)
  const secs = Math.floor(seconds % 60)
  return `${mins.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`
}

// 音频元数据加载完成
const onLoadedMetadata = () => {
  if (audioRef.value) {
    duration.value = audioRef.value.duration
  }
}

// 音频时间更新
const onTimeUpdate = () => {
  if (audioRef.value) {
    currentTime.value = audioRef.value.currentTime
  }
}

// 音频播放结束
const onAudioEnded = () => {
  isPlaying.value = false
  currentTime.value = 0
}

// 进度条改变事件
const onProgressChange = () => {
  if (audioRef.value) {
    audioRef.value.currentTime = currentTime.value
  }
}

// 播放/暂停切换
const togglePlay = () => {
  if (!audioRef.value || !props.option.audioSrc) return

  if (isPlaying.value) {
    audioRef.value.pause()
  } else {
    audioRef.value.play()
  }
  isPlaying.value = !isPlaying.value
}
</script>

<template>
  <div class="audio-player">
    <div class="audio-controls">
      <div class="avatar-container">
        <img v-if="option.avatar" :src="option.avatar" alt="avatar" class="avatar" />
        <div v-else class="avatar-placeholder"></div>
      </div>
      <el-slider v-model="currentTime" :max="duration" :disabled="!option.audioSrc" @change="onProgressChange"
        class="progress-slider" />
      <span class="time-display">{{ formatTime(currentTime) }} / {{ formatTime(duration) }}</span>
      <base-button size="small" @click="togglePlay" :disabled="!option.audioSrc" :sets="listenSets"
        class="listen-button">
        {{ isPlaying ? "暂停" : "播放" }}
      </base-button>
    </div>
    <!-- 隐藏的音频元素 -->
    <audio ref="audioRef" :src="option.audioSrc" @loadedmetadata="onLoadedMetadata" @timeupdate="onTimeUpdate"
      @ended="onAudioEnded" preload="metadata">
    </audio>
  </div>
</template>

<style scoped>
.audio-player {
  padding: 15px;
  background-color: #f9fafb;
  border-radius: 6px;
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.audio-controls {
  display: flex;
  align-items: center;
  gap: var(--el-gap);
}

.avatar-container {
  width: 60px;
  height: 60px;
}

.avatar {
  width: 100%;
  height: 100%;
  border-radius: 50%;
  object-fit: cover;
}

.avatar-placeholder {
  width: 100%;
  height: 100%;
  border-radius: 50%;
  background-color: #e0e0e0;
  display: flex;
  align-items: center;
  justify-content: center;
}

.progress-slider {
  flex: 1;
}

.time-display {
  width: 80px;
  text-align: center;
  font-size: 12px;
  color: #666;
}

.listen-button {
  width: 120px;
  height: 40px;
  font-size: 16px;
  background-color: #409eff;
  color: white;
  border: none;
}

:deep(.el-slider__button) {
  background-color: #409eff;
  border-color: #409eff;
}

:deep(.el-slider__bar) {
  background-color: #409eff;
}
</style>