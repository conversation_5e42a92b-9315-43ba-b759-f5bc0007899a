<script setup lang="ts">
    defineProps<{
        sets?: TsInput.Sets;
    }>()
    defineSlots<TsInput.Slots>();
    const inputEl = useTemplateRef<InstanceType<typeof ElementPlus.ElInput>>("inputEl");
    function focus() {
        inputEl.value && inputEl.value.focus();
    }
    defineExpose({
        focus,
    })
</script>

<template>
    <el-input
        class="base-input"
        placeholder="请输入"
        clearable
        v-bind="sets"
        ref="inputEl"
    >
        <template v-for="(_,slotName) in $slots" :key="slotName" #[slotName]>
            <slot :name="slotName as keyof TsInput.Slots"></slot>
        </template>
    </el-input>
</template>
<style scoped>
</style>