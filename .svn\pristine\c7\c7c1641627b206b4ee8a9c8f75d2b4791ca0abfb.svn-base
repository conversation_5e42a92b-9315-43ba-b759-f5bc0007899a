<script setup lang="ts">
defineProps<{
	option: TsApis.ApiResponseRowsItem<"post_aivatarapi_getvideolist">;
}>();
const videoConfig:TsDigitalVideo.Config = {
	width: "100%",
	height: "100%",
	controls: true,
}
function getState(val: TsApis.ApiResponseRowsItem<"post_aivatarapi_getvideolist">["synthesisStatus"]): {
	label: string;
	type: TsElementPlus.TagProps["type"];
} {
	const obj = synthesisStatus.find((item) => item.value == val);
	return {
		label: obj?.label ?? "未知",
		type: obj?.type ?? "warning",
	};
}
</script>

<template>
	<div class="digital-video-part">
		<div class="cover-img">
			<digital-video-play :video-src="option.videoUrl" :cover-url="option.coverUrl" :config="videoConfig" v-if="option.videoUrl" />
		</div>
		<div class="digital-work-msg">
			<div class="title">{{ option.videoName }}</div>
			<div class="create-time">
				<base-icon icon="solar:clock-square-linear" />
				<span>{{ option.createTime }}</span>
			</div>
			<div class="state">
				<el-tag :type="getState(option.synthesisStatus).type">{{ getState(option.synthesisStatus).label }}</el-tag>
			</div>
		</div>
	</div>
</template>

<style scoped lang="scss">
.digital-video-part {
	width: 200px;
	border-radius: var(--el-border-radius-middle);
	box-sizing: border-box;
	background-color: var(--el-bg-color-page);
	overflow: hidden;
}

.cover-img {
	width: 100%;
	height: 350px;
	object-fit: cover;
	cursor: pointer;
	position: relative;
}

.digital-work-msg {
	display: flex;
	flex-direction: column;
	align-items: stretch;
	justify-content: space-between;
	padding: var(--el-gap-half);
	gap: 10px;
}

.title {
	font-size: var(--el-font-size-large);
	font-weight: bold;
	color: var(--el-text-color-primary);
	overflow: hidden;
	text-overflow: ellipsis;
	white-space: nowrap;
}

.create-time {
	display: flex;
	align-items: center;
	gap: var(--el-gap-half);
	font-size: var(--el-font-size-base);
	color: var(--el-text-color-regular);
}

.delete-icon {
	color: var(--el-color-danger);
	cursor: pointer;
}
</style>