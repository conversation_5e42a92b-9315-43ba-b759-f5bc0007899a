import { create<PERSON>lova } from 'alova';
import fetchAdapter from 'alova/fetch';
import vueHook from 'alova/vue';
import { createApis, withConfigType, mountApis } from './createApis';
import { createServerTokenAuthentication } from "alova/client";
import { tokenExpired } from "@/assets/config/app";

const { onAuthRequired, onResponseRefreshToken } = createServerTokenAuthentication<typeof vueHook>({
	// 附加 token
	assignToken(method) {
		const { token } = pinia.storeToRefs(storeApp());
		method.config.headers.Authorization = "Bearer " + token.value;
        method.config.headers["Content-Type"] = "application/json; charset=utf-8";
	},
	// 无感刷新token
	refreshTokenOnSuccess: {
		// 判断token过期的条件
		isExpired: async (response) => {
			// 先尝试解析响应
			try {
				const data = await response.clone().json();
				return data.code === 401 || data.code === 403;
			} catch {
				return false;
			}
		},

		// 当token过期时触发刷新
		handler: async () => {
			try {
                if(tokenExpired === "refreshToken") {
                    await apiApp.refreshToken(); // 刷新token
                } else if(tokenExpired === "reLogin") {
                    eventBus.emit("logout"); // 退出登录
					throw new Error();
                }
			} catch (error) {
				ElMessage({type: "error", message: "请重新登录"});
				throw error;
			}
		},
	},
});

export const alovaInstance = createAlova({
  baseURL: import.meta.env.VITE_BASE_ROUTER_PREFIX + import.meta.env.VITE_BASE_API,
  statesHook: vueHook,
  requestAdapter: fetchAdapter(),
  beforeRequest: onAuthRequired(),
  responded: onResponseRefreshToken({
    onSuccess: async (response) => {
        //todo 如果返回二进制流数据直接返回结果
        // return response;
        const res = await response.json();
		if(res.code !== 200) {
			ElMessage({type: "error", message: res.msg});
			throw new Error(res.msg);
		}
		return res;
    },
    onError: (error) => {
        ElMessage({type: "error", message: error.message});
        throw new Error(error.message);
    }
  })
});

export const $$userConfigMap = withConfigType({});

const Apis = createApis(alovaInstance, $$userConfigMap);

mountApis(Apis);

export default Apis;
