<script setup lang="ts">
const route = useRoute();
// 模型定制数据
const { form, send: sendSubmit } = apiDigitalModel.videoTaskInfo();
form.value.id = route.query.id as string;

// 表单引用
const formRef = useTemplateRef<TsForm.El>("formRef")
// 绿幕选项
const optionsIsNot = [
  { label: '是', value: '1' },
  { label: '否', value: '0' }
];
// 定制类型选项
const optionsType = [
    { label: '极速形象克隆', value: '1'},
    { label: '极速形象克隆-Pro版', value: '3'},
    { label: '形象克隆-S级-超清', value: '358'},
    { label: '形象克隆-E级', value: '359'},
    { label: '形象克隆-S级', value: '360'}
];
// 表单设置
const setsForm:TsForm.Sets = {
    labelWidth: '10em',
}
// 表单验证
const rules:TsElementPlus.FormRules = {
    level: [
        { required: true, message: '请选择定制类型', trigger: 'change' },
    ],
    name: [
        { required: true, message: '请输入模特名称', trigger: 'blur' },
    ],
    greenScreen: [
        { required: true, message: '请选择是否绿幕', trigger: 'change' },
    ],
    compress: [
        { required: true, message: '请选择是否压缩', trigger: 'change' },
    ],
    videoUrl: [
        { required: true, message: '请输入视频链接', trigger: 'blur' },
    ]
}
// 表单提交
const onSubmit = async () => {    
  formRef.value.validate().then(() => {
    sendSubmit();
  });
};

// 重置表单
const onReset = () => {
    formRef.value.resetFields();
};
</script>

<template>
  <el-card header="模型定制" class="model-customized">
    <base-form ref="formRef" :model-value="form" :sets="setsForm" :rules="rules">
      <base-form-item label="定制类型：" prop="level">
        <base-select v-model="form.level" :options="optionsType" />
      </base-form-item>
      <base-form-item label="模特名称：" prop="name">
        <base-input v-model="form.name" :sets="{ placeholder: '请输入模特名称' }" />
      </base-form-item>
      <base-form-item label="是否绿幕：" prop="greenScreen">
        <base-radio v-model="form.greenScreen" :options="optionsIsNot"/>
      </base-form-item>
      <base-form-item label="视频素材压缩：" prop="compress">
        <base-radio v-model="form.compress" :options="optionsIsNot"/>
      </base-form-item>
      <base-form-item label="训练视频：" prop="videoUrl">
        <digital-upload v-model="form.videoUrl" type="video" />
      </base-form-item>
      <div class="form-actions">
        <base-button type="primary" @click="onSubmit">提交</base-button>
        <base-button @click="onReset">重置</base-button>
      </div>
    </base-form>
  </el-card>
</template>

<style scoped lang="scss">
.model-customized {
    background-color: var(--el-bg-color);
    height: 100%;
    border-radius: var(--el-border-radius-middle);
    .el-form {
        width: 500px;
        margin: 0 auto;
    }
}

.form-actions {
  display: flex;
  gap: 10px;
  justify-content: center;
  margin-top: 30px;
}
</style>