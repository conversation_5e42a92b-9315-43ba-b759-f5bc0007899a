<script setup lang="ts">
const props = defineProps<{
    data: TsApis.ApiResponse<"general", "get_order_order_list">["rows"],
    loading: boolean,
}>();
const emits = defineEmits(["update:data"]);
const tableData = vueuse.useVModel(props, 'data', emits);
// 格式化订单状态
function formatOrderStatus(row: TsMyOrder.Recharge.Data) {
    const statusMap: Record<number, string> = {
        0: '待支付',
        1: '已支付',
        2: '已取消',
        3: '已超时'
    };
    return statusMap[row.status] || '未知状态';
}

// 格式化支付方式
function formatPaymentMethod(row: TsMyOrder.Recharge.Data) {
    const typeMap: Record<number, string> = {
        1: '微信支付',
        2: '支付宝'
    };
    return typeMap[row.type] || '未知方式';
}
</script>

<template>
    <base-table v-loading="loading" v-model="tableData">
        <base-table-column label="订单编号" prop="orderNo" />
        <base-table-column label="商品名称" prop="productDesc" />
        <base-table-column label="充值额度(钻石)" prop="amount" />
        <base-table-column label="订单状态" prop="status" :formatter="formatOrderStatus" />
        <base-table-column label="支付方式" prop="paymentMethod" :formatter="formatPaymentMethod" />
        <base-table-date label="创建时间" prop="createTime" />
        <base-table-date label="支付时间" prop="payTime" />
        <base-table-column label="过期时间" prop="expireTime" />
    </base-table>
</template>

<style lang="scss" scoped>

</style>
