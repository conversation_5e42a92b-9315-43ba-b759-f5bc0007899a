<script lang="ts" setup>
const { data: props } = defineProps<{
	data: TsModelMarket.ModleData;
}>();

const emits = defineEmits<{
	clickLike: [id: TsModelMarket.ModleData["id"], newLikeNum: number];
	clickFavorite: [id: TsModelMarket.ModleData["id"], oldFavorite: boolean];
	clickUse: [data: TsModelMarket.ModleData];
	clickDetail: [data: TsModelMarket.ModleData];
}>();

// 点赞
const likeNum = ref(props.popularity ?? 0);
// const showLikeEffect = ref(false);
// const likeEffectPosition = shallowRef({ x: 0, y: 0 });

// const clickLike = (e: MouseEvent) => {
// 	likeNum.value++;
// 	emits("clickLike", props.id, likeNum.value);

// 	// 显示点赞动效
// 	const btnRect = e.currentTarget;
// 	if (!(btnRect instanceof HTMLElement)) {
// 		return;
// 	}

// 	const rect = btnRect.getBoundingClientRect();
// 	const cardRect = btnRect.closest(".card")?.getBoundingClientRect();

// 	if (cardRect) {
// 		likeEffectPosition.value = {
// 			x: rect.left - cardRect.left + rect.width / 2,
// 			y: rect.top - cardRect.top + rect.height / 2,
// 		};

// 		showLikeEffect.value = true;

// 		// 1秒后隐藏
// 		setTimeout(() => {
// 			showLikeEffect.value = false;
// 		}, 1000);
// 	}
// };

// 收藏
const clickFavorite = (e: MouseEvent) => {
	e.preventDefault();
	emits("clickFavorite", props.id, false);
};

// 分享
// const clickShare = (e: MouseEvent) => {
// 	e.preventDefault();
// 	copyText(props.robotName ?? "")
// 		.then(() => {
// 			ElMessage.success("复制成功");
// 		})
// 		.catch(() => {
// 			ElMessage.error("复制失败");
// 		});
// };

// 热度
const enthusiasmShow = computed(() => {
	const halfEnthusiasm = Math.floor((props.starSigns || 0) / 2);

	if (halfEnthusiasm >= 5) {
		return {
			showHalf: false,
			enthusiasm: 5,
		};
	}

	return {
		showHalf: (props.starSigns || 0) % 2 > 0,
		enthusiasm: halfEnthusiasm,
	};
});

// 详情
const clickDetail = () => {
	emits("clickDetail", props);
};

// 使用
const clickUse = () => {
	emits("clickUse", props);
};

const isCover = ref(false);

const video = useTemplateRef<HTMLVideoElement>("videoRef");

const mouseMoveIn = () => {
	isCover.value = true;
	video.value?.play().catch(null);
};

const mouseMoveOut = () => {
	isCover.value = false;
	video.value?.pause();
	lowPriorityFn(() => {
		if (video.value) {
			video.value.currentTime = 0;
		}
	});
};

const URL = shallowRef({
	img: props.coverUrl || props.sceneList?.[0]?.coverUrl,
	video: props.sceneList?.[0]?.exampleUrl,
});
</script>

<template>
	<div class="card" @mouseenter="() => mouseMoveIn()" @mouseleave="() => mouseMoveOut()">
		<!-- 点赞动效元素 -->
		<!-- <div v-show="showLikeEffect" class="like-effect" :style="{ left: `${likeEffectPosition.x}px`, top: `${likeEffectPosition.y}px` }">点赞+1</div> -->

		<header class="header" @click.stop>
			<div class="btn-left">
				<!-- @click="clickLike" -->
				<button class="like-btn">
					<base-icon class="icon" icon="solar:like-linear" />
					{{ likeNum }}
				</button>
				<button v-if="false" v-show="isCover && !props" @click="clickFavorite">
					<base-icon class="icon favorite-not-active" icon="solar:heart-linear" />
				</button>
				<div v-if="false" class="favorite-active-container" v-show="isCover && props">
					<base-icon class="icon favorite-active" icon="solar:heart-bold" />
				</div>
			</div>
			<!-- <div class="btn-right">
				<button v-show="isCover" @click="clickShare">
					<base-icon class="icon" icon="solar:share-outline" />
				</button>
			</div> -->
		</header>
		<div class="main" @click="clickDetail">
			<img v-show="!isCover || !URL.video" class="img" :src="URL.img" alt="modelIMG" />
			<div v-if="URL.video" v-show="isCover" class="video">
				<video ref="videoRef" :src="URL.video!" width="200" height="354" preload="none" muted disablepictureinpicture></video>
			</div>
		</div>
		<footer class="footer" @click.stop>
			<div class="footer-left">
				<span class="name">
					<auto-tooltip :content="props.robotName ?? ''" placement="bottom" />
				</span>
				<div class="enthusiasm">
					<div class="enthusiasmItem" v-for="index in enthusiasmShow.enthusiasm" :key="index">
						<base-icon icon="solar:fire-bold" />
					</div>
					<base-icon v-if="enthusiasmShow.showHalf" icon="solar:fire-bold-duotone" />
				</div>
			</div>
			<div class="useButton">
				<el-button v-show="isCover" type="primary" size="small" @click="clickUse"> 使用 </el-button>
			</div>
		</footer>
	</div>
</template>

<style lang="scss" scoped>
$fire-color: rgb(255, 79, 79);

.card {
	position: relative;
	// 200存在白边
	width: 199.4px;
	height: 354px;
	border-radius: 8px;
	overflow: hidden;
	cursor: pointer;
	background: linear-gradient(180deg, #c9cbd8, #bcbdc7);

	.img {
		height: 354px;
		overflow: clip;
		object-fit: cover;
		margin: 0 auto;
	}

	// 点赞动效样式
	.like-effect {
		position: absolute;
		transform: translate(-50%, -50%);
		color: #fff;
		font-size: 12px;
		font-weight: bold;
		pointer-events: none;
		animation: likeEffect 0.5s ease-out forwards;
	}

	@keyframes likeEffect {
		0% {
			opacity: 1;
			transform: translate(-50%, -70%);
		}
		50% {
			opacity: 1;
			transform: translate(-55%, -120%);
		}
		100% {
			opacity: 0;
			transform: translate(-50%, -200%);
		}
	}
}

.header {
	position: absolute;
	margin: 10px 0;
	padding: 0 10px;
	height: 24px;
	display: flex;
	align-items: center;
	width: 100%;
	justify-content: space-between;
	z-index: 1;
	cursor: auto;

	.btn-left {
		display: flex;
		gap: 8px;
	}

	button {
		background: rgba(0, 0, 0, 0.3);
		min-width: 24px;
		box-sizing: border-box;
		height: 24px;
		border: none;
		border-radius: 4px;
		color: white;
		cursor: pointer;
		display: flex;
		align-items: center;
		justify-content: center;
		transition: background 0.2s;
		cursor: pointer;

		&:hover {
			background: rgba(0, 0, 0, 0.5);
		}
	}

	.favorite-active-container {
		height: 24px;
		width: 24px;
		border: none;
		border-radius: 4px;
		color: white;
		cursor: pointer;
		display: flex;
		align-items: center;
		justify-content: center;
		background: none;
		cursor: pointer;
	}

	.like-btn {
		font-size: 12px;
		padding: 0 4px;
	}

	.icon {
		width: 24px;
		height: 24px;
		display: flex;
		align-items: center;
		justify-content: center;

		svg {
			width: 16px;
			height: 16px;
		}
	}

	.favorite-not-active {
		color: #fff;
	}

	.favorite-active {
		color: $fire-color;
	}
}

.footer {
	position: absolute;
	bottom: 0;
	width: 100%;
	height: 32px;
	display: flex;
	align-items: center;
	justify-content: space-between;
	background: linear-gradient(180deg, transparent, #000);
	cursor: auto;

	.footer-left {
		display: flex;
		align-items: center;
		flex: 1;
		white-space: nowrap;
		overflow: hidden;
	}

	.name {
		padding: 10px;
		color: #fff;
		font-size: 14px;
		white-space: nowrap;
		text-overflow: ellipsis;
		overflow: hidden;
		max-width: 100px;
		text-align: left;
	}

	.enthusiasm {
		display: flex;
		color: rgb(255, 79, 79);

		&-item {
			height: 16px;
			width: 16px;
		}
	}

	.useButton {
		margin: 0 8px;
		flex: 0 0 auto;
	}
}
</style>
