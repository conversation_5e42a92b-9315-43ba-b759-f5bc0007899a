namespace TsBalace {
    namespace RechargeDialog {
        type tab = 'recharge_diamond' | 'general_video_duration' | 'audio_duration' | 'image_clone_s' | 'image_clone_e' | 'image_clone_s_hd' | 'voice_clone_e' | 'voice_clone_s';
        // @ts-ignore
        type insideComponents = DefineComponent<{
            balance: string;
        }, {
            updateChoose?: [point: number],
        }>
        type fetchParam = TsApis.ApiParams<'general', 'post_api_wechatpay_createorder'>
    }
    type Info = {
        // 用户ID
       userId: number
       // 钻石余额
       diamondBalance: number
       // 视频通用时长（秒）
       videoDuration: number
       // 音频通用时长（秒）
       audioDuration: number
       // S级形象克隆次数（次）
       avatarCloneS: number
       // E级形象克隆次数（次）
       avatarCloneE: number
       // E级声音次数（次）
       voiceCloneE: number
    }
}