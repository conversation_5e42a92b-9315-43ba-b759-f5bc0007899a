<script setup lang="ts">
const setsIconLeft = computed(
	(): TsButton.Sets => ({
		icon: markRaw(IconSolarDoubleAltArrowLeftLinear),
		circle: true,
		plain: hideLeft.value,
		size: "small",
	})
);
const setsIconSearch = computed(
	(): TsButton.Sets => ({
		icon: markRaw(IconSolarRoundedMagniferLinear),
		circle: true,
		plain: hideForm.value,
		size: "small",
	})
);
const setsIconStatic = computed(
	(): TsButton.Sets => ({
		icon: markRaw(IconSolarChartLinear),
		circle: true,
		plain: hideStatic.value,
		size: "small",
	})
);
const hideSlots = ref<TsLayoutTable.slot[]>([]);

const leftEl = useTemplateRef<HTMLDivElement>("leftEl");
const { width: leftWidth } = vueuse.useElementSize(leftEl);
const hideLeft = computed(() => hideSlots.value.includes("left"));

const formEl = useTemplateRef<HTMLDivElement>("formEl");
const { height: formHeight } = vueuse.useElementSize(formEl);
const hideForm = computed(() => hideSlots.value.includes("form"));

const staticEl = useTemplateRef<HTMLDivElement>("staticEl");
const { height: staticHeight } = vueuse.useElementBounding(staticEl);
const hideStatic = computed(() => hideSlots.value.includes("static"));

const { isReady } = utilMountedReady();
const gridTemplate = ref<string>("");

function onToggle(name: TsLayoutTable.slot): void {
	const index = hideSlots.value.indexOf(name);
	if (index > -1) {
		hideSlots.value.splice(index, 1);
	} else {
		hideSlots.value.push(name);
	}
}

watchEffect(function () {
	if (!isReady.value) return;
	let row = [!staticHeight.value || hideStatic.value ? "0" : staticHeight.value + 10 + "px", "auto", !formHeight.value || hideForm.value ? "0" : formHeight.value + 10 + 18 + "px", "1fr", "auto"];
	let col = [!leftWidth.value || hideLeft.value ? "0" : leftWidth.value + 10 + 20 + "px", "1fr", "auto"];
	gridTemplate.value = "grid-template:" + row.join(" ") + " / " + col.join(" ");
});
</script>
<template>
	<div class="layout-table" :style="gridTemplate">
		<div class="layout-left" v-if="$slots.left">
			<div class="part" ref="leftEl">
				<slot name="left"></slot>
			</div>
		</div>
		<div class="layout-static" v-if="$slots.static">
			<div class="part" ref="staticEl">
				<slot name="static"></slot>
			</div>
		</div>
		<div class="layout-handle" v-if="$slots.handle || $slots.handleExtra">
			<div class="part">
				<div class="layout-handle-default">
					<base-button :title="hideLeft ? '展开左侧' : '收起左侧'" :sets="setsIconLeft" :class="hideLeft ? 'rotate' : ''" @click="onToggle('left')" v-if="$slots.left" />
					<base-button :title="hideForm ? '展开筛选' : '收起筛选'" :sets="setsIconSearch" @click="onToggle('form')" v-if="$slots.form" />
					<base-button :title="hideStatic ? '展开统计' : '收起统计'" :sets="setsIconStatic" @click="onToggle('static')" v-if="$slots.static" />
					<slot name="handle"></slot>
				</div>
				<div class="layout-handle-extra" v-if="$slots.handleExtra">
					<slot name="handleExtra"></slot>
				</div>
			</div>
		</div>
		<div class="layout-form" v-if="$slots.form">
			<div class="part" ref="formEl">
				<slot name="form"></slot>
			</div>
		</div>
		<div class="layout-table-main" v-if="$slots.table">
			<div class="part">
				<el-auto-resizer>
					<template #default="{ width, height }">
						<slot name="table" :width="width" :height="height"></slot>
					</template>
				</el-auto-resizer>
			</div>
		</div>
		<div class="layout-pagination" v-if="$slots.pagination">
			<div class="part">
				<slot name="pagination"></slot>
			</div>
		</div>
		<div class="layout-right" v-if="$slots.right">
			<div class="part">
				<slot name="right"></slot>
			</div>
		</div>
		<div class="layout-custom" v-if="$slots.custom">
			<slot name="custom"></slot>
		</div>
	</div>
</template>
<style scoped lang="scss">
.part {
	background-color: var(--el-bg-color);
	border-radius: var(--el-border-radius-base);
	padding: var(--el-gap-half);
	overflow: auto;
	width: 100%;
	height: 100%;
}

.layout-table {
	display: grid;
	width: 100%;
	height: 100%;
	grid-template: auto auto 1fr 62px / auto 1fr auto;
	transition: all var(--el-transition-duration);
}

.layout-left {
	grid-row: 1 / 6;
	grid-column: 1 / 2;
	overflow: hidden;
	margin-right: var(--el-gap-half);
	.part {
		width: max-content;
		min-width: 200px;
		max-width: 450px;
	}
}
.layout-right {
	grid-row: 1 / 6;
	grid-column: 3 / 4;
	overflow: hidden;
	margin-left: var(--el-gap-half);
}

.layout-form {
	grid-row: 3 / 4;
	grid-column: 2 / 3;
	overflow: hidden;
	margin-bottom: var(--el-gap-half);
	.part {
		height: max-content;
		&:has(.base-form) {
			padding-top: 18px;
			padding-bottom: 0;
		}
	}
}
.layout-custom {
	grid-row: 1 / 6;
	grid-column: 2 / 4;
	overflow-x: hidden;
	overflow-y: auto;
}
.layout-static {
	grid-row: 1 / 2;
	grid-column: 2 / 3;
	overflow: hidden;
	margin-bottom: var(--el-gap-half);
	.part {
		height: max-content;
	}
}
.layout-handle {
	grid-row: 2 / 3;
	grid-column: 2 / 3;
	overflow: hidden;
	margin-bottom: var(--el-gap-half);
	.part {
		display: flex;
		align-items: center;
		justify-content: space-between;
	}
	.rotate {
		transform: rotate(180deg);
	}
}

.layout-table-main {
	grid-row: 4 / 5;
	grid-column: 2 / 3;
	overflow: hidden;
}

.layout-pagination {
	grid-row: 5 / 6;
	grid-column: 2 / 3;
	overflow: hidden;
	margin-top: var(--el-gap-half);
}

.base-icon {
	cursor: pointer;
}
</style>
