export default pinia.defineStore("storePassing", () => {
    const visible = ref(false);
    const tab = ref<TsMyOrder.Dialog.tab>('recharge_diamond');
    const onCloseFn = shallowRef<() => void>(() => null);

    /**
     * 打开弹窗
     * @param tabValue 弹窗场景名
     */
    function open(tabValue: TsMyOrder.Dialog.tab = 'recharge_diamond') {
        visible.value = true;
        tab.value = tabValue;
    }

    /**
     * 关闭弹窗，主要为弹窗内部调用，请尽量不要使用
     */
    function close() {
        visible.value = false;
        onCloseFn.value = () => null;
    }
    /**
     * 弹窗关闭回调
     * @param fn 
     */
    function onClose(fn: () => void) {
        onCloseFn.value = fn;
    }

    return {
        visible,
        tab,
        open,
        close,
        onClose,
    }
})