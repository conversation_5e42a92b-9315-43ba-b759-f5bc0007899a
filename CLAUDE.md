# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Development Commands

- `npm run dev` - Start development server on port 9527
- `npm run build` - Build for production (runs vue-tsc type checking first)
- `npm run preview` - Preview production build locally

## Project Architecture

This is a Vue 3 + TypeScript + Vite application with the following key characteristics:

### Core Stack
- **Vue 3** with Composition API and `<script setup>`
- **TypeScript** for type safety
- **Vite** for build tooling and development
- **Element Plus** as the UI component library
- **Pinia** for state management with session storage persistence
- **Vue Router** with dynamic routing support
- **Alova** for API management with auto-generated endpoints

### Auto-Import System
The project uses extensive auto-imports configured in `vite.config.ts`:
- Vue APIs (`ref`, `reactive`, `computed`, etc.) are auto-imported
- Element Plus components are auto-imported (use `<el-button>`, `<el-table>`, etc.)
- Pinia stores are accessible via `pinia.` prefix
- API calls are available via `Apis.` prefix  
- VueUse utilities via `vueuse.` prefix
- Alova utilities via `alova.` prefix
- Echarts via `echarts.` prefix
- Custom components and utilities from `src/components/`, `src/router/`, `src/store/`, `src/assets/`

### State Management
- **storeApp**: System parameters including token, tabs, theme settings, lock screen state
- **storeUser**: User information including ID, avatar, username, permissions
- Both stores use session storage persistence and can be reset via `clearStore` event

### API Architecture
- API endpoints are auto-generated from `src/assets/data/api.json` using Alova
- Generated APIs are available globally as `Apis.*`
- Automatic token handling with refresh token support
- SSO authentication support configurable via environment variables

### Component Structure
Follow this priority when using components:
1. **Custom base components** in `src/components/base/` (e.g., `baseButton.vue`, `baseTable.vue`)
2. **Layout components** in `src/components/layout/` (e.g., `layoutTable.vue` for left tree + right table)
3. **Element Plus components** directly
4. **Custom development** only when needed

### Key Directories
- `src/components/base/` - Reusable base components wrapping Element Plus
- `src/components/layout/` - Layout components like `layoutTable.vue`
- `src/views/` - Page components
- `src/assets/api/` - API interface definitions
- `src/assets/types/` - TypeScript type definitions
- `src/assets/data/` - Static data including dynamic route configuration
- `src/request/` - API request configuration and generated endpoints

### Routing
- Uses Vue Router with dynamic route loading
- Routes are loaded from API (`getRoutesDynamic()`) and added programmatically
- Includes authentication guards and tab management
- Supports SSO authentication flow

### Development Notes
- Server runs on configurable port (default via `VITE_RUN_PORT` env var) with proxy configuration
- Uses `@` alias for `src/` directory
- Includes compression plugin for production builds with gzip
- Auto-generated type definitions in `auto-imports.d.ts` and `components.d.ts`
- Build output includes timestamped filenames for cache busting

## Code Conventions

### Naming Conventions (from README.md)
- **Pages**: Directory names start with lowercase, use camelCase; page files start with uppercase, use PascalCase (e.g., `Demo.vue`, `DemoTest.vue`)
- **Components**: Start with lowercase, use camelCase
- **TypeScript Types**: Store in `/assets/types/views/pages/` with naming pattern `[pageName].d.ts`
- **API Files**: Store in `src/assets/api/` with naming pattern `api[PageName].ts` (e.g., `apiDemo.ts` for `Demo.vue`)

### API Generation
- APIs are auto-generated from Swagger/OpenAPI spec via Alova wormhole
- Configuration in `alova.config.ts` pulls from `http://127.0.0.1:4523/export/openapi/2`
- Auto-updates every 5 minutes and on editor launch
- Generated APIs exposed globally as `Apis.*` on `globalThis`