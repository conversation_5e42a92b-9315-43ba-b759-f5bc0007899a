const digitalMarketModel = {
    option: {
        proportion: [
            {
                label: '全部',
                value: 'all',
            },
            {
                label: '横版',
                value: 'horizontal',
            },
            {
                label: '竖版',
                value: 'vertical',
            }
        ],
        clone: [
            { label: '全部', value: 'all' },
            { label: '绿幕', value: 'green_screen' },
            { label: '商务', value: 'business' },
            { label: '休闲', value: 'casual' },
        ],
    },
    map: {
        typeName: new Map([
            [0, '绿幕'],
            [1, '其他'],
        ] as const),
        gender: new Map([
            [1, '♂️'],
            [2, '♀️'],
        ] as const),
        version: new Map([
            [0, '2D'],
            [1, '3D'],
        ] as const),
        starSign: new Map([
            [1, '白羊座 ♈️'],
            [2, '金牛座 ♉️'],
            [3, '双子座 ♊️'],
            [4, '巨蟹座 ♋️'],
            [5, '狮子座 ♌️'],
            [6, '处女座 ♍️'],
            [7, '天秤座 ♎️'],
            [8, '天蝎座 ♏️'],
            [9, '射手座 ♐️'],
            [10, '摩羯座 ♑️'],
            [11, '水瓶座 ♒️'],
            [12, '双鱼座 ♓️'],
        ] as const),
    }
}

export default digitalMarketModel
