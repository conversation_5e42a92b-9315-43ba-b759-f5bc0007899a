<script lang="ts" setup>
import { typeNameMap } from '@/views/pages/digital/market/model/const';

const props = defineProps<{
  videoInfo: Partial<TsModelMarket.VideoType>;
}>();

const emits = defineEmits<{
  close: [],
}>();

const video = useTemplateRef<HTMLVideoElement>('videoRef');

const showData = computed(() => {
  // 获取视频时长并转换为"xx分xx秒"格式
  const duration = props.videoInfo.duration || 0;
  const minutes = Math.floor(duration / 60);
  const seconds = Math.floor(duration % 60);
  const formattedDuration = `${minutes}分${seconds}秒`;
  
  // 获取场景类型名称
  const sceneTypeName = typeNameMap.get(props.videoInfo.sceneType || 0) || '未知';
  
  // 返回转换后的数据
  return {
    sceneName: props.videoInfo.sceneName || '',
    sceneCode: props.videoInfo.sceneCode || '',
    proportion: props.videoInfo.proportion || '',
    description: props.videoInfo.description || '适用于后期制作',
    videoUrl: props.videoInfo.videoUrl || '',
    coverUrl: props.videoInfo.coverUrl || '',
    coverMattingUrl: props.videoInfo.coverMattingUrl || '',
    duration: formattedDuration,
    sceneType: sceneTypeName as typeof sceneTypeName,
  };
})

// 是否展示信息
const isSelected = ref(false);

watch(
  () => props.videoInfo,
  () => {
    isSelected.value = true;
    video.value?.play().catch(null);
  },
  { immediate: false }
)

const close = () => {
  video.value?.pause();
  lowPriorityFn(() => {
    if (video.value) {
        video.value.currentTime = 0;
    }
  })
  isSelected.value = false;
  emits('close');
}

</script>

<template>
  <div class="video-container">
    <!-- 视频信息区域 -->
    <div class="video-info" v-if="isSelected">
      <div class="info-item">
        <span class="label">名称:</span>
        <span class="value">{{ showData.sceneName }}</span>
      </div>
      <div class="info-item">
        <span class="label">比例:</span>
        <span class="value">{{ showData.proportion }}</span>
      </div>
      <div class="info-item">
        <span class="label">类型:</span>
        <span class="value">
          <el-tag
            :type="showData.sceneType === '绿幕' ? 'success' : 'info'"
          >
            {{ showData.sceneType }}
          </el-tag>
        </span>
      </div>
      <div class="info-item">
        <span class="label">时长:</span>
        <span class="value">{{ showData.duration }}</span>
      </div>
      <div class="info-item description">
        <span class="label">简介:</span>
        <span class="value">
          <auto-tooltip
            :content="showData.description"
            placement="top"
          />
        </span>
      </div>
      <base-icon
        class="close-btn"
        icon="solar:close-square-outline"
        @click="close"
      />
    </div>

    <!-- 视频播放区域 -->
    <div class="video-player">
      <img 
        v-show="!isSelected"
        :src="showData.coverUrl"
        :alt="showData.sceneName"
        class="video"
      />
      <video
        v-show="isSelected"
        ref="videoRef"
        :src="showData.videoUrl"
        :poster="showData.coverUrl"
        class="video"
        controls
        disablepictureinpicture
      ></video>
    </div>
  </div>
</template>

<style scoped lang="scss">
.video-container {
  width: 100%;
  height: 414px;
  margin: 0 auto;
  border-radius: var(--el-border-radius-middle, 8px);
  overflow: hidden;
  position: relative;
  padding: var(--el-gap);
  --info-background-color: linear-gradient(180deg, #505050, transparent);
  --info-text-color: #fff;
}

.dark .video-container {
  --info-background-color: linear-gradient(180deg, #000000, transparent);
  --info-text-color: #aaaaaa;
}

.video-info {
  margin: var(--el-gap);
  padding: var(--el-gap-half);
  background: var(--info-background-color);
  position: absolute;
  top: 0;
  left: 0;
  width: calc(100% - var(--el-gap) * 2);
  z-index: 1;
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  row-gap: var(--el-gap-half);
  font-size: var(--el-font-size-small);
  height: auto;
  color: var(--info-text-color);

  .info-item {
    display: flex;
    width: 100%;
    align-items: center;
    height: 20px;
    line-height: 20px;

    .label {
      width: 4em;
      font-weight: bold;
    }

    &.description {
      grid-column-start: 2;
      grid-column-end: 4;
    }
  }

  .close-btn {
    position: absolute;
    top: var(--el-gap-half);
    right: var(--el-gap-half); 
    background: none;
    border: none;
    font-size: 20px;
    cursor: pointer;
    z-index: 2;

    &:hover {
      transition: transform 0.3s ease;
      transform: scale(1.1);
    }
  }
}

.video-player {
  position: relative;
  width: 100%;
  background-color: var(--el-bg-color-page);
  display: flex;
  height: 100%;
  justify-content: center;

  .video {
    height: 100%;
    display: block;
  }
}
</style>