<script setup lang="ts">
defineProps<{
  option: TsDigital.AudioMarketOption
}>()

// 处理去制作按钮点击事件
function handleCreate() {
  ElMessage({
    message: '去制作功能暂未实现',
    type: 'info'
  })
}

// 生成星级评分数组
function getStarRating(rating: number) {
  const stars = []
  const fullStars = Math.floor(rating)
  const hasHalfStar = rating % 1 !== 0

  // 添加满星
  for (let i = 0; i < fullStars; i++) {
    stars.push({ type: 'full', key: i })
  }

  // 添加半星
  if (hasHalfStar) {
    stars.push({ type: 'half', key: fullStars })
  }

  // 添加空星
  const emptyStars = 5 - Math.ceil(rating)
  for (let i = 0; i < emptyStars; i++) {
    stars.push({ type: 'empty', key: fullStars + (hasHalfStar ? 1 : 0) + i })
  }

  return stars
}
</script>

<template>
  <div class="digital-audio-market">
    <!-- 用户头像 -->
    <div class="avatar-container">
      <img :src="option.avatar" :alt="option.name" class="avatar" />
    </div>

    <!-- 用户信息 -->
    <div class="user-info">
      <!-- 用户名 -->
      <div class="user-name">{{ option.name }}</div>

      <!-- 星级评分 -->
      <div class="rating-container">
        <div class="stars">
          <template v-for="star in getStarRating(option.rating)" :key="star.key">
            <div class="star-wrapper" v-if="star.type === 'half'">
              <base-icon icon="solar:star-outline" class="star star-empty" />
              <base-icon icon="solar:star-bold" class="star star-half-fill" />
            </div>
            <base-icon
              v-else-if="star.type === 'full'"
              icon="solar:star-bold"
              class="star star-full"
            />
            <base-icon
              v-else
              icon="solar:star-outline"
              class="star star-empty"
            />
          </template>
        </div>
      </div>
      
      <!-- 标签 -->
      <div class="tags-container" v-if="option.tags && option.tags.length > 0">
        <el-tag 
          v-for="(tag, index) in option.tags" 
          :key="index"
          size="small"
          type="info"
          class="tag-item"
        >
          {{ tag }}
        </el-tag>
      </div>
    </div>

    <!-- 去制作按钮 -->
    <div class="action-container">
      <base-button @click="handleCreate" :sets="{ size: 'small', type: 'primary' }">
        去制作
      </base-button>
    </div>
  </div>
</template>

<style lang="scss" scoped>
.digital-audio-market {
  width: 200px;
  padding: var(--el-gap);
  background-color: var(--el-bg-color-page);
  border-radius: var(--el-border-radius-base);
  // box-shadow: var(--el-box-shadow-light);
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: var(--el-gap-half);
  transition: all 0.3s ease;

  &:hover {
    // background-color: var(--el-bg-color-light-hover);
    // box-shadow: var(--el-box-shadow);
    transform: translateY(-2px);
  }
}

.avatar-container {
  width: 80px;
  height: 80px;
  border-radius: 50%;
  overflow: hidden;
  border: 3px solid var(--el-color-primary-light-8);

  .avatar {
    width: 100%;
    height: 100%;
    object-fit: cover;
    display: block;
  }
}

.user-info {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 6px;
}

.user-name {
  font-size: var(--el-font-size-base);
  font-weight: 500;
  color: var(--el-text-color-primary);
  text-align: center;
}

.rating-container {
  display: flex;
  align-items: center;
  justify-content: center;
}

.stars {
  display: flex;
  align-items: center;
  gap: 2px;
}

.star-wrapper {
  position: relative;
  display: inline-block;

  .star-half-fill {
    position: absolute;
    top: 0;
    left: 0;
    width: 50%;
    overflow: hidden;
    color: #ffd700;
  }
}

.star {
  font-size: 16px;

  &.star-full {
    color: #ffd700; // 金色
  }

  &.star-empty {
    color: var(--el-color-info-light-5);
  }
}

.tags-container {
  display: flex;
  flex-wrap: wrap;
  justify-content: center;
  gap: 4px;
  margin-top: 4px;
  
  .tag-item {
    margin: 0;
  }
}

.action-container {
  margin-top: 4px;
}
</style>