export default {
	// 获取用户列表（带分页）
	getList: (params: TsApis.ApiParamsWithoutPage<"general", "get_system_user_list">) => {
		const method = (page: number, pageSize: number) =>
			Apis.general.get_system_user_list({
				name: "get_system_user_list",
				params: {
					...params,
					pageNum: page,
					pageSize,
				},
				transform: (res) => ({
					total: res.total ?? 0,
					data:
						res.rows?.map((row) => {
							const { dept, ...rest } = row;
							return {
								...rest,
								deptName: dept?.deptName ?? "",
							};
						}) ?? [],
				}),
				hitSource: ["post_system_user", "put_system_user", "delete_system_user_userid"],
			});
		return alova.usePagination(method, {
			watchingStates: [params, toRef(params, "userName"), toRef(params, "phonenumber")],
			debounce: [0, 300, 300],
			initialPage: 1,
			initialPageSize: 10,
			immediate: true,
			middleware: alova.actionDelegationMiddleware("get_system_user_list"),
			initialData: {
				total: 0,
				data: [],
			},
		});
	},

	// 获取用户详情
	getDetail: (userId: Ref<TsUser.Id>) => {
		const methodRequest = alova.useRequest(
			() =>
				Apis.general.get_system_user_userid({
					name: "get_system_user_userid",
					pathParams: { userId: userId.value },
					hitSource: ["put_system_user", "delete_system_user_userid"],
				}),
			{
				immediate: false,
				middleware: alova.actionDelegationMiddleware("get_system_user_userid"),
				initialData: {
					code: 0,
					msg: "",
					data: {
						userId: 0,
						userName: "",
						nickName: "",
						email: "",
						phonenumber: "",
						sex: "",
						avatar: "",
						status: "",
						deptId: 0,
						dept: {
							deptId: 0,
							deptName: "",
						},
						admin: false,
						remark: "",
					},
					postIds: [],
					posts: [],
					roleIds: [],
					roles: [],
				},
			}
		);

		const { onError } = methodRequest;
		onError((error) => {
			ElMessage.error(error.error.msg || "获取用户详情失败");
		});
		return methodRequest;
	},

	// 获取用户初始化信息（角色和岗位列表）
	getInitInfo: () => {
		const method = Apis.general.get_system_user({
			name: "get_system_user",
			transform: (res) => ({
				posts:
					res.posts?.map((item) => ({
						label: item.postName || "",
						value: item.postId || 0,
						disabled: item.status === "1",
					})) || [],
				roles:
					res.roles?.map((item) => ({
						label: item.roleName || "",
						value: item.roleId || 0,
						disabled: item.status === "1",
					})) || [],
			}),
		});

		return alova.useRequest(() => method, {
			immediate: true,
			middleware: alova.actionDelegationMiddleware("get_system_user"),
			initialData: {
				posts: [],
				roles: [],
			},
		});
	},

	// 新增用户
	add: () => {
		const methodRequest = alova.useForm(
			(data: TsApis.ApiData<"general", "post_system_user">) =>
				Apis.general.post_system_user({
					name: "post_system_user",
					data,
				}),
			{
				resetAfterSubmiting: true,
				middleware: alova.actionDelegationMiddleware("post_system_user"),
				initialForm: {
					nickName: "",
					deptId: undefined,
					phonenumber: "",
					email: "",
					sex: "",
					status: "0",
					postIds: [],
					roleIds: [],
					remark: "",
					userName: "",
					password: "",
				},
			}
		);

		const { onSuccess, onError } = methodRequest;
		onSuccess((res) => {
			if (res.data.code === 200) {
				ElMessage.success(res.data.msg || "添加用户成功");
				alova.accessAction("get_system_user_list", (api) => api.refresh());
			} else {
				ElMessage.error(res.data.msg || "添加用户失败");
			}
		});

		onError((error) => {
			ElMessage.error(error.error.msg || "添加用户失败");
		});

		return methodRequest;
	},

	// 更新用户
	update: () => {
		const methodRequest = alova.useForm(
			(data: TsApis.ApiData<"general", "put_system_user">) =>
				Apis.general.put_system_user({
					name: "put_system_user",
					data,
				}),
			{
				immediate: false,
				middleware: alova.actionDelegationMiddleware("put_system_user"),
				initialForm: {
					userId: 0,
					nickName: "",
					deptId: 0,
					phonenumber: "",
					email: "",
					sex: "",
					status: "0",
					postIds: [],
					roleIds: [],
					remark: "",
				},
			}
		);

		const { onSuccess, onError } = methodRequest;
		onSuccess((res) => {
			if (res.data.code === 200) {
				ElMessage.success(res.data.msg || "更新用户成功");
				alova.accessAction("get_system_user_list", (api) => api.refresh());
			} else {
				ElMessage.error(res.data.msg || "更新用户失败");
			}
		});

		onError((error) => {
			ElMessage.error(error.error.msg || "更新用户失败");
		});

		return methodRequest;
	},

	// 删除用户
	delete: (userId: Ref<TsUser.Id>) => {
		const methodRequest = alova.useRequest(
			() =>
				Apis.general.delete_system_user_userid({
					name: "delete_system_user_userid",
					pathParams: { userId: userId.value },
				}),
			{
				immediate: false,
				middleware: alova.actionDelegationMiddleware("delete_system_user_userid"),
				initialData: {
					code: 0,
					msg: "",
				},
			}
		);

		const { onSuccess, onError } = methodRequest;
		onSuccess((res) => {
			if (res.data.code === 200) {
				ElMessage.success(res.data.msg || "删除用户成功");
				alova.accessAction("get_system_user_list", (api) => api.refresh());
			} else {
				ElMessage.error(res.data.msg || "删除用户失败");
			}
		});

		onError((error) => {
			ElMessage.error(error.error.msg || "删除用户失败");
		});

		return methodRequest;
	},
	
	// 重置密码
	resetPassword: () => {
		const methodRequest = alova.useForm(
			(data: { userId: TsUser.Id; password: string }) =>
				Apis.general.put_system_user_resetpwd({
					name: "put_system_user_resetpwd",
					data,
				}),
			{
				immediate: false,
				middleware: alova.actionDelegationMiddleware("put_system_user_resetpwd"),
				initialForm: {
					userId: 0,
					password: "",
				},
			}
		);

		const { onSuccess, onError } = methodRequest;
		onSuccess((res) => {
			if (res.data.code === 200) {
				ElMessage.success(res.data.msg || "密码重置成功");
			} else {
				ElMessage.error(res.data.msg || "密码重置失败");
			}
		});

		onError((error) => {
			ElMessage.error(error.error.msg || "密码重置失败");
		});

		return methodRequest;
	},
};
