<script setup lang="ts">
const props = defineProps<{
    data: TsApis.ApiResponse<"general", "get_workconsumption_record_list">["rows"],
    loading: boolean,
}>();
const emits = defineEmits(["update:data"]);
const tableData = vueuse.useVModel(props, 'data', emits);
</script>

<template>
    <base-table v-loading="loading" v-model="tableData">
        <base-table-column label="作品名称" prop="workName" />
        <base-table-column label="消耗来源" prop="consumptionSource" />
        <base-table-column label="消耗量" prop="consumption" />
        <base-table-column label="剩余量" prop="remaining" />
        <base-table-date label="消耗时间" prop="consumptionTime" />
    </base-table>
</template>

<style lang="scss" scoped>

</style>
