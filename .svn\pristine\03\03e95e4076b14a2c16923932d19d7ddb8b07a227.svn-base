<script setup lang="ts">
import './dialogInside.scss'
import pointGoodCard from './pointGoodCard.vue';

defineProps<{
    balance: string
}>();

defineEmits<{
    updateChoose: [point: number],
}>();

// 充值选项
const rechargeOptions = [
    { point: 1, price: 1 },
    { point: 10, price: 10 },
    { point: 50, price: 50 },
    { point: 100, price: 100 },
    { point: 500, price: 500 },
    { point: 1000, price: 1000 },
    { point: 2000, price: 2000 },
    { point: 8000, price: 8000 },
]

const remainingPoint = ref(rechargeOptions[0]);

// 选择充值时长
function handelClick(item: { point: number; price: number; }) {
    remainingPoint.value = item
}
</script>

<template>
    <!-- 信息显示 -->
    <div class="recharge-dialog-inside-info">
        <span>积分余额：{{ balance }}</span>
    </div>

    <!-- 充值选项网格 -->
    <div class="recharge-dialog-inside-grid">
        <pointGoodCard
            v-for="option in rechargeOptions"
            :key="option.point"
            :point="option.price"
            :num="option.point"
            :is-active="remainingPoint.point == option.point"
            is-point-recharge
            @click="handelClick(option)"
        />
    </div>
</template>
