import avatarImg from "@/assets/image/avatar.jpg";
export default {
	// 登录验证码
	getCaptchaImage: () => {
		return alova.useRequest(
			Apis.general.get_captchaimage({
				transform: (res) => {
					return {
						img: "data:image/png;base64," + res.img,
						uuid: res.uuid,
						captchaEnabled: res.captchaEnabled,
					};
				},
			}),
			{
				force: true,
				immediate: true,
				initialData: {
					img: "",
					uuid: "",
					captchaEnabled: true,
				},
			}
		);
	},
	// 登录
	login: (form: TsLogin.Form) => {
		const methodRequest = alova.useRequest(
			() =>
				Apis.general.post_login({
					data: form,
					transform: (res) => {
						return {
							token: res.token,
							code: res.code,
							msg: res.msg,
						};
					},
				}),
			{
				immediate: false,
			}
		);
		const { onSuccess, onError } = methodRequest;
		onSuccess((res) => {
			if (res.data.code === 200) {
				const { token } = pinia.storeToRefs(storeApp());
				token.value = res.data.token || "";
			}
		});
		onError((err) => {
			ElMessage.error(err.error.msg || "登录失败");
		});
		return methodRequest;
	},
	// 获取用户信息
	getUserInfo: () => {
		const methodRequest = alova.useRequest(
			() =>
				Apis.general.get_getinfo({
					transform: (res) => res,
				}),
			{
				immediate: false,
				initialData: {
					code: "",
					msg: "",
					permissions: [],
					roles: [],
					user: {
						userId: "",
						userName: "",
						userType: "",
						nickName: "",
						avatar: avatarImg,
						sex: "",
						phonenumber: "",
						admin: false,
						dept: {
							deptId: "",
							deptName: "",
						},
					},
				},
			}
		);
		const { onSuccess } = methodRequest;
		onSuccess((res) => {
			if (res.data && res.data.code === 200) {
				// 获取用户信息成功，保存到store中
				const { userId, userName, userType, nickName, avatar, sex, phonenumber, admin, deptId, deptName, permissions, roles } = pinia.storeToRefs(storeUser());
				userType.value = res.data.user?.userType || "";
				userId.value = res.data.user?.userId || "";
				userName.value = res.data.user?.userName || "";
				nickName.value = res.data.user?.nickName || "";
				avatar.value = res.data.user?.avatar || avatarImg;
				sex.value = res.data.user?.sex || "";
				phonenumber.value = res.data.user?.phonenumber || "";
				admin.value = res.data.user?.admin || false;
				deptId.value = res.data.user?.dept?.deptId || "";
				deptName.value = res.data.user?.dept?.deptName || "";
				permissions.value = res.data.permissions || [];
				roles.value = res.data.roles || [];
			}
		});

		return methodRequest;
	},
	// 退出登录
	logout: () => {
		const methodRequest = alova.useRequest(() => Apis.general.post_logout(), {
			immediate: false,
			initialData: {
				code: 0,
				msg: "",
			},
		});

		const { onSuccess, onError } = methodRequest;
		onSuccess((res) => {
			if (res.data && res.data.code === 200) {
				ElMessage.success(res.data.msg || "退出成功");
				eventBus.emit("clearStore"); // 清除store
				eventBus.emit("logout"); // 退出登录
			}
		});

		onError((err) => {
			ElMessage.error(err.error.msg || "退出失败");
		});

		return methodRequest;
	},
	//todo 刷新token
	refreshToken: () => {
		return new Promise((resolve) => {
			setTimeout(() => {
				resolve({
					code: 200,
					data: {
						token: "1234567890",
					},
				});
			}, 1000);
		});
	},
	//todo 通过code获取token
	getTokenByCode: (code: string): Promise<string> => {
		console.log("code", code);
		return new Promise((resolve) => {
			const res: TsRequest.ResponseData<{ token: string }> = {
				code: 200,
				msg: "success",
				data: {
					token: "1234567890",
				},
			};
			setTimeout(() => {
				// code处理完成后，清除URL中的code参数
				const newUrl = decodeURIComponent(utilUrlRemoveParam("code"));
				window.history.replaceState({}, "", newUrl);
				resolve(res.data.token);
			}, 300);
		});
	},
	// 获取动态路由
	getRoutesDynamic: (): Promise<TsStore.RouteDynamic[]> => {
		return new Promise((resolve) => {
			const res: TsRequest.ResponseData<TsStore.RouteDynamic[]> = {
				code: 200,
				msg: "success",
				data: dataRoutes,
			};
			setTimeout(() => {
				resolve(res.data);
			}, 300);
		});
	},
	// 获取短信验证码
	getPhoneCode: () => {
		return alova.useRequest(
			(params: TsApis.ApiParamsWithoutPage<"general", "post_aivatarapi_sendmsg">) => Apis.general.post_aivatarapi_sendmsg({
				name: "post_aivatarapi_sendmsg",
				params,
			}),
			{
				immediate: false,
			}
		);
	},
	// 注册
	register: () => {
		const methodRequest = alova.useForm(
			(data: TsApis.ApiData<"general", "post_register">) =>
				Apis.general.post_register({
					name: "post_aivatarapi_register",
					data: data,
				}),
			{
				resetAfterSubmiting: true,
				middleware: alova.actionDelegationMiddleware("post_register"),
				initialForm: {
					nickname: "",
					phonenumber: "",
					code: "",
					password: "",
					username: "",
				},
			}
		);
		const { onSuccess } = methodRequest;
		onSuccess((res) => {
			if (res.data.code === 200) {
				ElMessage.success(res.data.msg || "注册成功");
				router.push({
					name: "Login",
				});
			}
		});
		return methodRequest;
	},
	// 获取账号信息
	getAccount() {
		const methodRequest = alova.useRequest(
			() =>
				Apis.general.post_aivatarapi_getmemberinfo({
					params: {},
				}),
			{
				immediate: false,
				initialData: {
					code: "",
					msg: "",
					data: {
						user: {
							admin: false,
							createBy: '',
							createTime: '',
							delFlag: '',
							dept: {
								ancestors: '',
								children: [],
								deptId: 0,
								deptName: '',
								leader: '',
								orderNum: 0,
								params: { '@type': '' },
								parentId: 0,
								status: ''
							},
							deptId: 0,
							loginDate: '',
							loginIp: '',
							nickName: '',
							params: { '@type': '' },
							phonenumber: '',
							pwdUpdateDate: '',
							roles: [],
							sex: '',
							status: '',
							userId: 0,
							userName: '',
							userType: ''
						},
						account: {
							createBy: null,
							createTime: '',
							updateBy: null,
							updateTime: '',
							remark: null,
							id: 0,
							userId: 0,
							diamondBalance: 0,
							videoDuration: 0,
							audioDuration: 0,
							avatarCloneS: 0,
							avatarCloneE: 0,
							voiceCloneE: 0
						}
					}
				},
			}
		);
		const { onSuccess } = methodRequest;
		onSuccess((res) => {
			if(!res || res.data.code !== 200 || !res.data.data.account) return;
			storeUser().updateAccount(res.data.data.user.admin, res.data.data.account);
		});

		return methodRequest;
	},
};
