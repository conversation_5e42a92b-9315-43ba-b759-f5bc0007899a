<script setup lang="ts">
// 弹框显示状态
const visible = ref(false);
// 表单ref
const formRef = useTemplateRef<TsForm.El>("formRef");


// 表单校验规则
const rules = reactive<TsElementPlus.FormRules>({
	userName: [{ required: true, message: "用户名不能为空", trigger: "blur" }],
	nickName: [{ required: true, message: "用户昵称不能为空", trigger: "blur" }],
	password: [{ required: true, message: "用户密码不能为空", trigger: "blur" }],
	phonenumber: [
		{
			pattern: REGEXP.phone,
			message: "请输入正确的手机号码",
			trigger: "blur",
		},
	],
	email: [
		{
			type: "email",
			message: "请输入正确的邮箱地址",
			trigger: "blur",
		},
	],
});

// 表单API
const {
    form,
    
} = apiDiamondConfig.add();

// 提交表单
function handleSubmit() {
	if (!formRef.value) return;
	formRef.value.validate().then(() => {
		addUser();
	});
}

// 取消
function handleCancel() {
	resetAdd();
	visible.value = false;
}

// 打开弹框
function open() {
	visible.value = true;
	resetAdd();
}

// 向父组件暴露方法
defineExpose({
	open,
});
</script>

<template>
	<base-dialog v-model="visible" title="新增用户" width="650px" @confirm="handleSubmit" @cancel="handleCancel">
		<base-form ref="formRef" :model-value="formData" :rules="rules" label-width="100px" v-loading="loadingAdd">
            <base-form-item label="用户昵称" prop="nickName">
                <base-input v-model="formData.nickName" placeholder="请输入用户昵称" />
            </base-form-item>
    
            <base-form-item label="归属部门" prop="deptId">
                <base-tree-select v-model="formData.deptId" :options="deptOptions" />
            </base-form-item>
		</base-form>
	</base-dialog>
</template>
