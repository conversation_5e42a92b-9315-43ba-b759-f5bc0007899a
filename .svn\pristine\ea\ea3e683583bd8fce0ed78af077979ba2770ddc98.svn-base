<script setup lang="ts">
defineProps<{
  option: TsDigitalWorkOption
}>();

function getState(val: TsDigitalWorkOption['state']): {
  label: string,
  type: TsElementPlus.TagProps['type']
} {
  switch (val) {
    case 1:
      return {
        label: '已完成',
        type: 'success'
      };
    case 2:
      return {
        label: '编辑中',
        type: 'primary'
      };
    case 3:
      return {
        label: '合成失败',
        type: 'danger'
      };
    default:
      return {
        label: '未开始',
        type: 'info'
      };
  }
}

function handleEdit() {
  ElMessage({
    message: '编辑功能暂未实现',
    type: 'warning'
  });
};

function handleDelete() {
  ElMessage({
    message: '删除功能暂未实现',
    type: 'warning'
  });
};
</script>

<template>
  <div class="digital-work-part">
    <!-- 封面图片 -->
    <img :src="option.img" alt="封面图片" class="cover-img" @click="handleEdit"/>
    <div class="digital-work-msg">
      <!-- 标题 -->
      <div class="title">
        <span>{{ option.title }}</span>
        <base-icon icon="solar:trash-bin-minimalistic-line-duotone" class="delete-icon" @click="handleDelete" />
      </div>
      <!-- 状态和创建时间一行两端对齐 -->
      <div class="state-time-row">
        <div class="create-time">
          <base-icon icon="solar:clock-square-linear" />
          <span>{{ option.createTime }}</span>
        </div>
        <div class="state">
          <el-tag :type="getState(option.state).type">{{ getState(option.state).label }}</el-tag>
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped>
.digital-work-part {
  width: 250px;
  border-radius: var(--el-border-radius-round);
  box-sizing: border-box;
  background-color: var(--el-bg-color);
  box-shadow: var(--el-box-shadow-light);
  overflow: hidden;
}

.cover-img {
  width: 100%;
  height: 180px;
  object-fit: cover;
  transition: transform 0.3s;
  cursor: pointer;
}

.cover-img:hover {
  transform: scale(1.05);
}

.digital-work-msg {
  display: flex;
  flex-direction: column;
  align-items: stretch;
  justify-content: space-between;
  padding: var(--el-gap-half);
  gap: 10px;
}

.state-time-row {
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
  width: 100%;
  margin-bottom: 4px;
}

.create-time {
  display: flex;
  align-items: center;
  gap: var(--el-gap-half);
  font-size: var(--el-font-size-base);
  color: var(--el-text-color-regular);
}

.title {
  font-size: 20px;
  font-weight: bold;
  color: var(--el-text-color-primary);
  display: flex;
  align-items: flex-start;
  justify-content: space-between;
}

.delete-icon {
  color: var(--el-color-danger);
  cursor: pointer;
}
</style>