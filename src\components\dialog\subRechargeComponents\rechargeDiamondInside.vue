<script setup lang="ts">
import './dialogInside.scss'
import pointGoodCard from './pointGoodCard.vue';

defineProps<{
    balance: string
}>();

const emits = defineEmits<{
    updateChoose: [id: number, point: number],
}>();

// 充值选项
const {
    data,
    loading,
    page,
} = apiDiamondConfig.getList({}, 9);

const rechargeOptions = computed(() => data.value.map(item => ({
    diamond: item.diamondCount,
    price: Number(item.amount),
    id: item.id,
})));

const remainingPoint = ref(rechargeOptions.value[0]);

// 选择充值时长
function handelClick(item: typeof rechargeOptions.value[number]) {
    remainingPoint.value = item;
    emits('updateChoose', item.id, item.diamond);
}
</script>

<template>
    <!-- 信息显示 -->
    <div class="recharge-dialog-inside-info">
        <span>钻石余额：{{ balance }}</span>
    </div>

    <!-- 充值选项网格 -->
    <div class="recharge-dialog-inside-grid" v-loading="loading">
        <pointGoodCard
            v-for="option in rechargeOptions"
            :key="option.id"
            :price="option.price"
            :num="option.diamond"
            :is-active="remainingPoint.id == option.id"
            is-point-recharge
            @click="handelClick(option)"
        />
    </div>
</template>
