<script setup lang="ts">
const props = defineProps<{
    modelValue?: TsTree.Model;
    options: TsTree.Options;
    sets?: TsTree.Sets;
}>()
const defaultProps: TsTree.Props = {
    label: "label",
    isLeaf: "isLeaf",
    disabled: "disabled",
    children: "children",
}
const treeEl = useTemplateRef<InstanceType<typeof ElementPlus.ElTree>>("treeEl");
const emits = defineEmits(["update:modelValue"]);
const model = vueuse.useVModel(props, "modelValue", emits);

// 选中更新modelValue的值
function onNodeClick(option: TsTree.Option) {
    model.value = option.value;
}
// 移除高亮
function removeHighlight() {
    if(!treeEl.value) return;
    const current = treeEl.value.$el.getElementsByClassName("is-current");
    if (current && current.length > 0) {
        current[0].classList.remove("is-current");
    }
}

// 数据筛选
function filter(val:string) {
    treeEl.value!.filter(val)
}
function filterNode(value: string, data: TsBase.Object) {
    if (!value) return true
    return data.label.includes(value)
}
//handle 折叠/展开
function onFold(val: boolean) {
    let nodes = treeEl.value?.store.nodesMap;
    for (const key in nodes) {
        nodes[key].expanded = val;
    }
}

//handle 全选
function onCheckedAll() {
    let res: TsTree.Option["value"][] = [];
    props.options.forEach((item) => {
        res.push(item.value);
    });
    treeEl.value?.setCheckedKeys(res, false);
}

//handle 全部取消
function onUncheckAll() {
    treeEl.value?.setCheckedKeys([]);
}
defineExpose({
    filter,
    removeHighlight,
    onFold,
    onCheckedAll,
    onUncheckAll,
})
</script>
<template>
    <el-tree
        class="base-tree"
        ref="treeEl"
        :data="options"
        :highlight-current="true"
        :props="defaultProps"
        :filter-node-method="filterNode"
        v-bind="sets"
        @node-click="onNodeClick"
    />
</template>
<style scoped>

</style>