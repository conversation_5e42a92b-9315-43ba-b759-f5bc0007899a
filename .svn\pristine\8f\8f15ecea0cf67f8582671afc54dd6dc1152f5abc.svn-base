const routes: TsVueRouter.RouteRecordRaw[] = [
	{
		path: "/error",
		name: "<PERSON>rro<PERSON>",
		meta: {
			noTab: true,
		},
		component: () => import("@/views/app/Error.vue"),
	},
	{
		path: "/lock",
		name: "Lock",
		component: () => import("@/views/app/Lock.vue"),
		meta: {
			noTab: true,
			label: "锁屏",
		},
	},
	{
		path: "/login",
		name: "Login",
		component: () => import("@/views/app/Login.vue"),
		meta: {
			noTab: true,
			label: "登录",
		},
	},
	{
		path: "/register",
		name: "Register",
		component: () => import("@/views/app/Register.vue"),
		meta: {
			noTab: true,
			label: "注册",
		},
	},
	{
		path: "",
		redirect: "/home",
	},
	{
		path: "",
		component: () => import("@/components/layout/layoutDigital.vue"),
		children: [
			{
				path: "/home",
				name: "Home",
				component: () => import("@/views/pages/Home.vue"),
				meta: {
					label: "首页",
					icon: "solar:home-smile-linear",
				},
			}
		]
	}
];
export default routes;
