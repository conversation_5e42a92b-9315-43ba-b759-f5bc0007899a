<script setup lang="ts">
// 整体
const isRechage = ref(true);
let name = 'first';
const activeName = computed({
  get() {
    return name;
  },
  set(val) {
    isRechage.value = val === 'first';
    name = val;
  }
});

// 充值记录相关
const rechargeData = shallowRef<TsMyOrder.Recharge.Data[]>([]);
const rechargeInfo = reactive({
  total: 0,
  page: 1,
  size: 10,
  loading: false,
})
const sortParams = ref<any>(null);

// 获取订单列表
async function getOrderList(params: Partial<TsMyOrder.Recharge.FormData> = {}) {
  rechargeInfo.loading = true;
  try {
    const res = await apiMyOrder.recharge.getList(params)(rechargeInfo.page, rechargeInfo.size);
    rechargeData.value = res.data;
    rechargeInfo.total = res.total;
  } finally {
    rechargeInfo.loading = false;
  }
}

// 搜索处理
function handleSearchRecharge(formData: Partial<TsMyOrder.Recharge.FormData> = {}) {
  getOrderList(formData);
}

handleSearchRecharge();

// 消费记录相关
const spendData = shallowRef<TsMyOrder.Spend.Data[]>([]);
const spendInfo = reactive({
  total: 0,
  page: 1,
  size: 10,
  loading: false,
})

async function getSpendOrderList(params: Partial<TsMyOrder.Spend.FormData> = {}) {
  spendInfo.loading = true;
  try {
    const res = await apiMyOrder.spend.getList(params)(spendInfo.page, spendInfo.size);
    spendData.value = res.data;
    spendInfo.total = res.total;
  } finally {
    spendInfo.loading = false;
  }
}

// 搜索处理
function handleSearchSpend(formData: TsMyOrder.Spend.FormData) {
  getSpendOrderList(formData);
}

// 分页相关
const page = computed({
  get() {
    return isRechage.value ? rechargeInfo.page : spendInfo.page;
  },
  set(val) {
    if (isRechage.value) {
      rechargeInfo.page = val;
    } else {
      spendInfo.page = val;
    }
  }
});
const pageSize = computed({
  get() {
    return isRechage.value ? rechargeInfo.size : spendInfo.size;
  },
  set(val) {
    if (isRechage.value) {
      rechargeInfo.size = val;
    } else {
      spendInfo.size = val;
    }
  }
});
const total = computed(() => {
  return isRechage.value ? rechargeInfo.total : spendInfo.total;
});

// 切换标签获取数据
const handelTabChaneg = (val: string | number) => {
  if (val === 'first') {
    getOrderList();
  } else {
    getSpendOrderList();
  }
}

// 充值弹窗相关
const rechargeDialog = storeRechargeDialog();

defineExpose({
    name: 'MyOrder',
})
</script>

<template>
	<layout-table>
        <template #static>
            <div class="balance">
                <div class="balance-card">
                    <div class="balance-item">
                        <div class="left">
                          <div class="balance-label">积分余额</div>
                          <div class="balance-value">0.00</div>
                        </div>
                        <div class="balance-action">
                            <base-button size="small" @click="rechargeDialog.open('recharge_point')">去充值</base-button>
                        </div>
                    </div>
                </div>
                <div class="balance-card">
                    <div class="balance-item">
                        <div class="left">
                            <div class="balance-label">收益时长</div>
                            <div class="balance-value">59分</div>
                        </div>
                        <div class="balance-action">
                            <base-button size="small">去充值</base-button>
                        </div>
                    </div>
                </div>
                <div class="balance-card">
                    <div class="balance-item">
                        <div classs="left">
                          <div class="balance-label">绘画余额次数</div>
                          <div class="balance-value">0次</div>
                        </div>
                        <div class="balance-action">
                            <base-button size="small">联系客服</base-button>
                        </div>
                    </div>
                </div>
                <div class="balance-card">
                    <div class="balance-item">
                        <div class="left">
                          <div class="balance-label">音频时长</div>
                          <div class="balance-value">9分59秒</div>
                        </div>
                        <div class="balance-action">
                            <base-button size="small">去充值</base-button>
                        </div>
                    </div>
                </div>
                <div class="balance-card">
                    <div class="balance-item">
                        <div class="left">
                          <div class="balance-label">音频消费次数</div>
                          <div class="balance-value">0次</div>
                        </div>
                        <div class="balance-action">
                            <base-button size="small">去充值</base-button>
                        </div>
                    </div>
                </div>
            </div>
        </template>
        <template #form>
            <el-tabs v-model="activeName" type="card" class="tabs" @tab-change="handelTabChaneg">
              <el-tab-pane label="充值详情" name="first">
                <my-order-recharge-form 
                    :loading="rechargeInfo.loading" 
                    @search="handleSearchRecharge" 
                />
              </el-tab-pane>
              <el-tab-pane label="消费详情" name="second">
                <my-order-spend-form 
                    :loading="spendInfo.loading" 
                    @search="handleSearchSpend" 
                />
              </el-tab-pane>
            </el-tabs>
        </template>
        <template #table>
            <my-order-recharge-table
                v-if="isRechage"
                :data="rechargeData" 
                :loading="rechargeInfo.loading"
                @sort="null" 
            />
            <my-order-spend-table
                v-else
                :data="spendData" 
                :loading="spendInfo.loading"
                @sort="null" 
            />
        </template>
        <template #pagination>
            <base-pagination 
                v-model:current-page="page" 
                v-model:page-size="pageSize" 
                :total="total"
            />
        </template>
	</layout-table>
</template>
<style scoped lang="scss">
.balance {
    display: flex;
    gap: var(--el-gap);
    margin-bottom: var(--el-gap-half);
    padding: var(--el-gap);
    background-color: var(--el-bg-color);
}

.balance-card {
    flex: 1;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: var(--el-border-radius-base);
    padding: var(--el-gap);
    color: white;
    position: relative;
    overflow: hidden;
}

.balance-card:nth-child(1) {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.balance-card:nth-child(2) {
    background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
}

.balance-card:nth-child(3) {
    background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
}

.balance-card:nth-child(4) {
    background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
}

.balance-card:nth-child(5) {
    background: linear-gradient(135deg, #fa709a 0%, #fee140 100%);
}

.balance-item {
    display: flex;
    height: 100%;
    justify-content: space-between;
    align-items: center;
}

.balance-label {
    font-size: var(--el-font-size-small);
    opacity: 0.9;
    margin-bottom: var(--el-gap-half);
}

.balance-value {
    font-size: var(--el-font-size-extra-large);
    font-weight: bold;
    margin-bottom: var(--el-gap);
}

.balance-action {
    margin-right: (--el-gap);
}

.balance-action .base-button {
    background-color: rgba(255, 255, 255, 0.2);
    border: 1px solid rgba(255, 255, 255, 0.3);
    color: white;
    font-size: var(--el-font-size-small);
}

.balance-action .base-button:hover {
    background-color: rgba(255, 255, 255, 0.3);
    border-color: rgba(255, 255, 255, 0.5);
}

.tabs {
  padding: var(--el-gap);
}

:deep(.layout-form .part) {
  padding: 0 !important;
}
</style>
