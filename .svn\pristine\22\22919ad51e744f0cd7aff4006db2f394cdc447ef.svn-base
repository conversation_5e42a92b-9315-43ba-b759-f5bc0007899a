# SVN删除脚本 - 删除所有状态为!的文件
# 使用方法: ./svnDelete.ps1

function Remove-SvnMissingFiles {
    # 检查是否在SVN工作目录中
    if (-not (Test-Path -Path ".svn" -PathType Container)) {
        Write-Host "错误: 当前目录不是SVN工作目录" -ForegroundColor Red
        return
    }

    # 获取所有状态为!的文件
    $missingFiles = @(svn st | Select-String -Pattern "^!" | ForEach-Object { $_.ToString().Substring(1).Trim() })
    
    if ($missingFiles.Count -eq 0) {
        Write-Host "没有发现状态为!的文件" -ForegroundColor Yellow
        return
    }
    
    Write-Host "发现以下状态为!的文件:" -ForegroundColor Cyan
    foreach ($file in $missingFiles) {
        Write-Host "  $file"
    }
    
    Write-Host ""
    Write-Host "总共 $($missingFiles.Count) 个文件将被从SVN中删除" -ForegroundColor Yellow
    Write-Host ""
    
    # 确认删除
    $confirm = Read-Host "确认删除这些文件吗? (y/N)"
    
    if ($confirm -match "^[yY](es)?$") {
        Write-Host "开始删除..." -ForegroundColor Cyan
        foreach ($file in $missingFiles) {
            Write-Host "删除: $file" -ForegroundColor Gray
            svn rm "$file"
        }
        Write-Host "删除完成!" -ForegroundColor Green
        Write-Host ""
        Write-Host "请运行 'svn commit' 提交更改" -ForegroundColor Yellow
    } else {
        Write-Host "操作已取消" -ForegroundColor Red
    }
}

# 执行删除函数
Remove-SvnMissingFiles