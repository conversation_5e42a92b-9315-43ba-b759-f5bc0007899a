export default {
	submit() {
		const method = (data: TsAudioClone.SubmitData) => Apis.general.post_aivatarapi_clone({ 
            name: "post_aivatarapi_clone",
            data,
         });

		return alova.useForm(method, {
			immediate: false,
            middleware: alova.actionDelegationMiddleware("post_aivatarapi_clone"),
			initialForm: {
				name: "",
				audioUrl: "",
				sex: 1,
			},
		});
	},
};
