<template>
  <div class="container">
    <div class="video-section">
      <div class="video-wrapper">
        <video :src="videoUrl" class="vertical-video" controls />
      </div>
    </div>

    <div class="upload-label">讲解内容</div>
    <div class="upload-section">
      <label class="upload-button">
        <input type="file" accept="video/*" class="file-input" />
        <span class="btn-text">上传录音</span>
        <i class="icon-question" title="提示信息">?</i>
      </label>
    </div>
  </div>
</template>

<script lang="ts" setup>
const { cardEdit } = pinia.storeToRefs(storePassing());
// 视频地址
const videoUrl = ref(import.meta.env.VITE_BASE_DIGITAL_URL + cardEdit.value?.videoCoverUrl);

</script>

<style scoped lang="scss">
.container {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 20px;
  width: 100%;

  .video-section {
    width: 100%;
    display: flex;
    flex-direction: column;
    align-items: center;
    margin-bottom: 20px;
    background-color: #dcdce1;

    .video-wrapper {
      width: 800px;
      height: 450px;
      overflow: hidden;
      display: flex;
      justify-content: center;
      align-items: center;
      background-color: #000000;

      .vertical-video {
        width: 100%;
        height: 100%;
        object-fit: cover;
      }
    }
  }

  .upload-label {
    width: 100%;
    text-align: left;
    margin-bottom: 8px;
  }

  .upload-section {
    width: 100%;
    height: 100px;
    margin-bottom: 20px;
    display: flex;
    justify-content: center;
    align-items: center;

    .upload-button {
      width: 98px;
      height: 32px;
      display: flex;
      align-items: center;
      background-color: #434af9;
      color: #fff;
      border-radius: 4px;
      cursor: pointer;
      transition: background-color 0.2s;

      &:hover {
        background-color: #3a40f0;
      }

      .file-input {
        display: none;
      }

      .btn-text {
        margin-right: 4px;
      }

      .icon-question {
        font-size: 12px;
        border: 1px solid #fff;
        border-radius: 50%;
        width: 16px;
        height: 16px;
        display: flex;
        justify-content: center;
        align-items: center;
      }
    }
  }
}
</style>