const eventNames = ["login", "logout", "lockScreen", "clearStore"] as const;
type EventNames = typeof eventNames[number];

class EventBus {
    private static instance: EventBus;
    private eventMap: Map<EventNames, (...args: any[]) => void>;

    private constructor() {
        this.eventMap = new Map();
    }

    public static getInstance(): EventBus {
        if (!EventBus.instance) {
            EventBus.instance = new EventBus();
        }
        return EventBus.instance;
    }

    public on(eventName: EventNames, callback: (...args: any[]) => void): void {
        this.eventMap.set(eventName, callback);
    }

    public emit(eventName: EventNames, ...args: any[]): void {
        const callback = this.eventMap.get(eventName);
        if (callback) {
            callback(...args);
        }
    }

    public off(eventName: EventNames): void {
        this.eventMap.delete(eventName);
    }
}

export default EventBus.getInstance();

export { EventBus };