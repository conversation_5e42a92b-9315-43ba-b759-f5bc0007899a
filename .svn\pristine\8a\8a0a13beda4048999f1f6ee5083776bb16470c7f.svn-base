declare namespace TsMyOrder {
    namespace Recharge {
        interface Data {
            id: number;
            orderNo: string;
            productDesc: string;
            amount: number;
            status: number;
            type: number;
            createTime: string;
            payTime: string | null;
            expireTime: string;
            [key: string]: any;
        }
        interface FormData {
            orderNo: string;
            status: string;
            type: string;
            orderTimeRange: string[];
            [key: string]: any;
        }
    }
    namespace Spend {
        interface Data {
            id: number,
            orderNo: string;
            productType: number;
            productName: string;
            consumptionAmount: number;
            createTime: string;
            [key: string]: any;
        }
        interface FormData {
            productName: string;
            productType: string;
            orderTimeRange: string[];
        }
    }
}