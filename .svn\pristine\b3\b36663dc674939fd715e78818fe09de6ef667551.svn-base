<template>
	<div class="container">
		<div class="image-section">
			<div class="image-wrapper">
				<video v-if="modelEdit?.sceneList?.[0]?.exampleUrl" :src="modelEdit?.sceneList[0].exampleUrl" class="vertical-video" controls />
				<img v-else :src="imageUrl" class="vertical-img" />
			</div>
		</div>

		<div class="textarea-label">讲解内容</div>
		<!-- 文本域区域 -->
		<div class="textarea-section">
			<el-input type="textarea" :rows="7" v-model="textareaContent" maxlength="20000" show-word-limit />
		</div>
		<el-button type="primary" @click="handleAudio" class="audio-section"> 试听 </el-button>
	</div>
</template>

<script lang="ts" setup>
const { modelEdit } = pinia.storeToRefs(storePassing());

// 图片地址
const imageUrl = ref("https://cdn.guiji.cn/model/png/4d18aabc9ccb619c5d329b63e75bec41.png");

// 文本域内容
const textareaContent = ref("");

// 试听按钮
const handleAudio = () => {
	console.log("点击试听，讲解内容：", textareaContent.value);
};
</script>

<style scoped>
.container {
	height: 100%;
	display: flex;
	flex-direction: column;
	align-items: center;
	padding: var(--el-gap);
	width: 100%;
	background-color: var(--el-bg-color);
	border-radius: var(--el-border-radius-middle);
	overflow: auto;

	.image-section {
		width: 100%;
		display: flex;
		flex-direction: column;
		align-items: center;
		margin-bottom: var(--el-gap);
		background-color: var(--el-bg-color-page);
		padding: var(--el-gap);

		.image-wrapper {
			width: 240px;
			height: 420px;
			overflow: hidden;
			display: flex;
			justify-content: center;
			align-items: center;
			background-color: var(--el-bg-color);
			border-radius: var(--el-border-radius-base);

			.vertical-img {
				width: 100%;
				height: 100%;
				object-fit: cover;
				transition: object-fit 0.3s ease;
			}
			
			.vertical-video {
				width: 100%;
				height: 100%;
				object-fit: cover;
			}
		}
	}

	.textarea-label {
		width: 100%;
		text-align: left;
		margin-bottom: 8px;
	}

	.textarea-section {
		width: 100%;
		margin-bottom: 20px;
	}

	.audio-section {
		width: 98px;
		height: 32px;
		align-self: flex-end;
		background-color: #434af9;
		color: #ffffff;
		border-radius: 4px;
		text-align: center;
		font-size: 14px;
		cursor: pointer;
	}
}
</style>