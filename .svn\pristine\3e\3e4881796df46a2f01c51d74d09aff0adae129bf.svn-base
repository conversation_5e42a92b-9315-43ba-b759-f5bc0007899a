<script setup lang="ts">
const options: TsDigital.VideoOption[] = [
	{
		id: "1",
		img: "https://picsum.photos/200/300?random=1",
		state: 1,
		title: "作品标题1",
		createTime: "2021-01-01",
	},
	{
		id: "2",
		img: "https://picsum.photos/200/300?random=2",
		state: 2,
		title: "作品标题2",
		createTime: "2021-02-01",
	},
	{
		id: "3",
		img: "https://picsum.photos/200/300?random=3",
		state: 3,
		title: "作品标题3",
		createTime: "2021-03-01",
	},
	{
		id: "4",
		img: "https://picsum.photos/200/300?random=4",
		state: 1,
		title: "作品标题4",
		createTime: "2021-04-01",
	},
	{
		id: "5",
		img: "https://picsum.photos/200/300?random=5",
		state: 2,
		title: "作品标题5",
		createTime: "2021-05-01",
	},
	{
		id: "6",
		img: "https://picsum.photos/200/300?random=6",
		state: 3,
		title: "作品标题6",
		createTime: "2021-06-01",
	},
	{
		id: "7",
		img: "https://picsum.photos/200/300?random=7",
		state: 1,
		title: "作品标题7",
		createTime: "2021-07-01",
	},
];
// 表单ref
const formRef = useTemplateRef<TsForm.El>("formRef");
// 表单数据
const formData = reactive<TsDigital.AudioForm>({
	title: "",
	state: "",
});
const page = ref(1);
const pageSize = ref(10);
const total = ref(7);
// 获取状态字典数据（假设有视频状态字典）
const statusOptions = ref([
	{ label: "待审核", value: 1 },
	{ label: "已发布", value: 2 },
	{ label: "已下架", value: 3 },
]);

// 筛选后的数据
const filteredOptions = computed(() => {
	return options.filter((item) => {
		const titleMatch = !formData.title || item.title.includes(formData.title);
		const stateMatch = formData.state === "" || item.state === formData.state;
		return titleMatch && stateMatch;
	});
});

// 重置筛选
function handleReset() {
  formRef.value?.resetFields();
}
</script>
<template>
  <layout-table>
    <template #form>
      <div class="market-header">
        <base-form :model-value="formData" :sets="{ inline: true }" ref="formRef">
          <base-form-item label="作品名称" prop="title">
            <base-input v-model="formData.title" placeholder="请输入作品名称" />
          </base-form-item>
          <base-form-item label="状态" prop="state">
            <base-select v-model="formData.state" :options="statusOptions" placeholder="请选择状态" clearable />
          </base-form-item>
          <base-form-item>
            <base-button @click="handleReset">重置</base-button>
          </base-form-item>
        </base-form>
      </div>
    </template>
    <template #table>
      <div class="video-container">
        <div class="video-new">
          <base-icon icon="solar:add-square-bold-duotone" />
          <span>新增视频</span>
        </div>
        <digital-video-part v-for="option in filteredOptions" :key="option.id" :option="option" />
      </div>
    </template>
    <template #pagination>
      <base-pagination
          v-model:current-page="page"
          v-model:page-size="pageSize"
          :total="total"
      />
    </template>
  </layout-table>
</template>
<style scoped lang="scss">
.video-container {
	display: flex;
	gap: var(--el-gap);
	flex-wrap: wrap;
  padding: var(--el-gap);
  background-color: var(--el-bg-color);
  border-radius: var(--el-border-radius-round);
}
.video-new {
	width: 250px;
	height: 266px;
	border-radius: var(--el-border-radius-middle);
	box-sizing: border-box;
	background-color: var(--el-bg-color-page);
	overflow: hidden;
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;
	gap: var(--el-gap);
	cursor: pointer;
	span {
		color: var(--el-color-primary);
	}
	.base-icon {
		font-size: 42px;
		color: var(--el-color-primary-light-5);
		transition: all 0.3s ease;
	}
	&:hover .base-icon {
		color: var(--el-color-primary);
	}
}
</style>
