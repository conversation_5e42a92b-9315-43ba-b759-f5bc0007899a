<script setup lang="ts">
const { sets = {} } = defineProps<{
	label: TsTableDate.Label;
	prop: TsTableDate.Prop;
	sets?: TsTableDate.Sets<TsBase.Object>;
}>();

function format(val: string): string {
	if (!val) return "--";
	let res = vueuse.useDateFormat(val, sets.fmtDate || "YYYY-MM-DD hh:mm:ss");
	return res.value;
}
</script>
<template>
	<base-table-column :label="label" :prop="prop" width="160" v-bind="sets" class="base-table-date">
		<template #default="{ row }">
			{{ format(row[prop]) }}
		</template>
	</base-table-column>
</template>
<style scoped></style>
