export default {
    // 数字市场-模特列表
    getMarketList() {
        const method = (page: number, size: number) => Apis.general['post_aivatarapi_robotlist']({
            name: "post_aivatarapi_robotlist",
            params: {
                page,
                size,
            },
            transform: (res) => ({
                total: res.total ?? 0,
                data: res.rows ?? [],
            }),
        })

        return alova.usePagination(method, {
            initialPage: 1,
            initialPageSize: 10,
            immediate: true,
            initialData: {
                total: 0,
                data: [],
            },
        })
    },
    // 我的作品-视频列表
    getWorksList() {
        const method = (page: number, pageSize: number) => Apis.general.post_aivatarapi_getvideolist({
            name: "post_aivatarapi_getvideolist",
            params: {
                page: page,
                size: pageSize
            },
            transform: (res) => {
                return {
                    data: res.rows ?? [],
                    total: res.total ?? 0,
                };
            }
        })
        return alova.usePagination(method, {
            immediate: true,
            initialPage: 1,
            initialPageSize: 10,
            initialData: {
                total: 0,
                data: [],
            },
        })
    },
    // 文本合成视频
    synthesizeVideo: () => {
        const method = (data: TsApis.ApiData<"general","post_aivatarapi_createvideobytext">) => Apis.general.post_aivatarapi_createvideobytext({
            name: "post_aivatarapi_createvideobytext",
            data: data,
        })
        return alova.useForm(method, {
            immediate: false,
            middleware: alova.actionDelegationMiddleware("post_aivatarapi_createvideobytext"),
            initialForm: {
                speakerId: "",
                sceneId: "",
                text: "",
                videoName: "",
                captureVideoFrame: true,
            }
        })
    },

    getDetail() {
        return null
    },

    liek() {
        return null
    },

    favorit() {
        return null
    },
    // 模特编辑合成
    modelEdit(data: TsApis.ApiData<"general", "post_aivatarapi_createvideobytext">) {
        const method = () => Apis.general.post_aivatarapi_createvideobytext({
            name: "post_aivatarapi_createvideobytext",
            data: data,
        })
        return alova.useRequest(method, {
            immediate: false,
            middleware: alova.actionDelegationMiddleware("post_aivatarapi_createvideobytext"),
        })
    },
    // 数字人训练
    training: () => {
        const method = (data: TsApis.ApiData<"general", "post_aivatarapi_training">) => Apis.general.post_aivatarapi_training({
            name: "post_aivatarapi_training",
            data,
        });
        const methodRequest = alova.useForm(method, {
            resetAfterSubmiting: true,
            middleware: alova.actionDelegationMiddleware("post_aivatarapi_training"),
            initialForm: {
                id: "",
                name: "",
                videoUrl: "",
                level: null,
                greenScreen: "0",
                compress: "0",
            }
        })
        const { onSuccess } = methodRequest;
        onSuccess(() => {
            ElMessage.success("定制模特提交成功")
        })
        return methodRequest;
    }
}
