<script setup lang="ts">
// 音频市场制作者数据
const options: TsDigital.AudioMarketOption[] = [
  {
    id: "1",
    avatar: "https://picsum.photos/100/100?random=1",
    name: "艾姐",
    rating: 4.5,
    tags: ["女声", "温柔", "成熟"]
  },
  {
    id: "2",
    avatar: "https://picsum.photos/100/100?random=2",
    name: "小明",
    rating: 5.0,
    tags: ["男声", "磁性", "青春"]
  },
  {
    id: "3",
    avatar: "https://picsum.photos/100/100?random=3",
    name: "小红",
    rating: 4.0,
    tags: ["女声", "甜美", "青春"]
  },
  {
    id: "4",
    avatar: "https://picsum.photos/100/100?random=4",
    name: "小李",
    rating: 4.8,
    tags: ["男声", "成熟", "严肃"]
  },
  {
    id: "5",
    avatar: "https://picsum.photos/100/100?random=5",
    name: "小王",
    rating: 3.5,
    tags: ["男声", "冷静", "磁性"]
  },
  {
    id: "6",
    avatar: "https://picsum.photos/100/100?random=6",
    name: "小张",
    rating: 4.2,
    tags: ["女声", "高贵", "优雅"]
  },
];
// 表单ref
const formRef = useTemplateRef<TsForm.El>("formRef");
// 表单数据
const formData = reactive<TsDigital.SoundForm>({
  keyWord: "",
  rating: "",
});
const page = ref(1);
const pageSize = ref(10);
const total = ref(6);
// 评分选项
const ratingOptions = ref([
  {label: "5星", value: 5},
  {label: "4星及以上", value: 4},
  {label: "3星及以上", value: 3},
  {label: "2星及以上", value: 2},
  {label: "1星及以上", value: 1},
]);

// 筛选后的数据
const filteredOptions = computed(() => {
  return options.filter((item) => {
    const nameMatch = !formData.keyWord || item.name.includes(formData.keyWord);
    const ratingMatch = formData.rating === "" || item.rating >= formData.rating;
    return nameMatch && ratingMatch;
  });
});

// 重置筛选
function handleReset() {
  formRef.value?.resetFields();
}
</script>
<template>
  <layout-table>
    <template #form>
      <base-form :model-value="formData" :sets="{ inline: true }" ref="formRef">
        <base-form-item label="关键词" prop="keyWord">
          <base-input v-model="formData.keyWord" placeholder="请输入关键词"/>
        </base-form-item>
        <base-form-item label="评分" prop="rating">
          <base-select v-model="formData.rating" :options="ratingOptions" placeholder="请选择评分" clearable/>
        </base-form-item>
        <base-form-item>
          <base-button @click="handleReset">重置</base-button>
        </base-form-item>
      </base-form>
    </template>
    <template #table>
      <div class="sound-main">
        <digital-audio-market v-for="option in filteredOptions" :key="option.id" :option="option"/>
      </div>
    </template>
    <template #pagination>
      <base-pagination
          v-model:current-page="page"
          v-model:page-size="pageSize"
          :total="total"
      />
    </template>
  </layout-table>
</template>
<style scoped lang="scss">
.base-form {
  padding-top: 8px;
}

.sound-main {
  display: flex;
  gap: var(--el-gap);
  flex-wrap: wrap;
}
</style>