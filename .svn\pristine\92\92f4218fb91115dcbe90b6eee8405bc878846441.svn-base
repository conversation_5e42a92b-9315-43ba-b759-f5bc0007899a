<script setup lang="ts">
import { setData } from "./useDetail";

const router = useRouter();

const query = reactive({
	search: "",
	modelType: "" as TsModelMarket.ModelType,
	hotShortType: undefined as TsBaseShortTypes.SortType,
	likesShortType: undefined as TsBaseShortTypes.SortType,
});

const { loading, data, page, pageSize, total, refresh } = apiDigitalModel.getList();

// 卡片操作
const clickLike = (id: number | undefined, newLikeNum: number) => {
	null;
};

const clickFavorite = (id: number | undefined, oldFavorite: boolean) => {
	null;
};

const { modelEdit } = pinia.storeToRefs(storePassing());
const clickUse = (item: TsApis.ApiResponse<"general","post_aivatarapi_robotlist">["rows"][number]) => {
	modelEdit.value = item;
	router.push({
		name: "ModelEdit",
	})
};

const clickDetail = (data: TsModelMarket.ModleData) => {
	setData(data);
	router.push({
		name: "ModelDetail",
		params: {
			id: data.id,
		},
	});
};

const searchSet = shallowRef<TsInput.Sets>({
	placeholder: "搜索形象关键词",
	prefixIcon: markRaw(IconSolarMagniferLinear),
});

defineExpose({
	name: "ModelMarket",
});
</script>
<template>
	<layout-table>
		<template #form>
			<div class="market-header">
				<el-tabs v-model="query.modelType" class="category-tabs">
					<el-tab-pane label="全部" name=""></el-tab-pane>
					<el-tab-pane label="最新" name="latest"></el-tab-pane>
					<el-tab-pane label="最热" name="hottest"></el-tab-pane>
					<el-tab-pane label="男生" name="male"></el-tab-pane>
					<el-tab-pane label="女生" name="female"></el-tab-pane>
					<el-tab-pane label="绿幕" name="greenScreen"></el-tab-pane>
					<el-tab-pane label="站姿" name="standing"></el-tab-pane>
					<el-tab-pane label="坐姿" name="sitting"></el-tab-pane>
					<el-tab-pane label="高级" name="advanced"></el-tab-pane>
					<el-tab-pane label="外模" name="foreign"></el-tab-pane>
				</el-tabs>
				<div class="header-actions">
					<div class="sort">
						<base-short v-model:short-type="query.hotShortType" name="热度" />
						<base-short v-model:short-type="query.likesShortType" name="点赞" />
					</div>
					<base-input v-model="query.search" class="search-input" :sets="searchSet" @keyup.enter="() => refresh()" />
				</div>
			</div>
		</template>
		<template #table>
			<div class="grid-container" v-loading="loading" element-loading-text="加载中...">
				<template v-for="item in data" :key="item.id">
					<digital-model-card :data="item" @click-like="clickLike" @click-favorite="clickFavorite" @click-use="clickUse" @click-detail="clickDetail" />
				</template>
			</div>
		</template>
		<template #pagination>
			<base-pagination v-model:current-page="page" v-model:page-size="pageSize" :total="total" />
		</template>
	</layout-table>
</template>
<style scoped lang="scss">
.market-container {
	height: 100%;
	display: flex;
	flex-direction: column;
	background: var(--el-bg-color);
}

.market-header {
	display: flex;
	align-items: center;
	justify-content: space-between;
	padding: 0 var(--el-gap);
	.category-tabs {
		flex-basis: auto;
		--el-border-color-light: var(--el-bg-color);

		:deep(.el-tabs__header) {
			margin: 0;
		}
	}

	.header-actions {
		display: flex;
		gap: var(--el-gap-half);
		align-items: center;
		width: 400px;

		.sort {
			display: flex;
			gap: var(--el-gap-half);
			width: min-content;
		}
	}

	.search-input {
		flex: 1;
	}
}

.grid-container {
	min-height: 100%;
	display: grid;
	grid-template-columns: repeat(auto-fill, 200px);
	grid-auto-rows: 354px;
	row-gap: var(--el-gap);
	column-gap: 25px;
	overflow-y: auto;
	flex: 1;
	padding: var(--el-gap);
}

.pagination-container {
	padding: var(--el-gap-half);
	display: flex;
	justify-content: center;
	border-top: 1px solid var(--el-border-color);
}
</style>
