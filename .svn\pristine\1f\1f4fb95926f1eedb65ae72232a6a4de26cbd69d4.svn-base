<script setup lang="ts">
const { audioEdit } = pinia.storeToRefs(storePassing());
const { data, page, pageSize, total, loading } = apiDigitalAudio.getSpeakerList();
function itemClick(item: TsApis.ApiResponse<"general", "post_aivatarapi_speakerlist">["rows"][number]) {
	audioEdit.value = item;
	router.push({
		name: "AudioEdit",
	});
}
</script>
<template>
	<layout-table>
		<template #table>
			<div class="audio-main" v-loading="loading" element-loading-text="加载中...">
				<digital-audio-market v-for="option in data" :key="option.id" :option="option" @item="itemClick(option)" />
			</div>
		</template>
		<template #pagination>
			<base-pagination v-model:current-page="page" v-model:page-size="pageSize" :total="total" />
		</template>
	</layout-table>
</template>
<style scoped lang="scss">
.base-form {
	padding-top: 8px;
}

.audio-main {
	display: flex;
	gap: var(--el-gap);
	flex-wrap: wrap;
	min-height: 100%;
}
</style>
