<script setup lang="ts">
const props = defineProps<{
    data: TsMyOrder.Recharge.Data[],
    loading: boolean,
}>();

const emit = defineEmits<{
    sort: [params: any];
}>();

const tableData = computed<TsMyOrder.Recharge.Data[]>({
    get() {
        return props.data;
    },
    set(newData: TsMyOrder.Recharge.Data[]) {
        // 这里可以添加数据更新逻辑，如果需要的话
    }
});

// 处理表格排序变化
function handleSortChange(params: any) {
    emit('sort', params);
}

defineExpose({
    name: 'MyOrderRechargeTable',
})
</script>

<template>
    <base-table
        v-loading="loading"
        v-model="tableData"
        :sets="{ border: true }"
        @sort-change="handleSortChange"
    >
        <base-table-column label="订单编号" prop="orderNo" />
        <base-table-column label="充值来源" prop="rechargeSource" />
        <base-table-column label="充值类型" prop="rechargeType" />
        <base-table-column label="商品名称" prop="productName" />
        <base-table-column label="充值额度" prop="rechargeAmount" />
        <base-table-column label="付款金额(元)" prop="paymentAmount" />
        <base-table-column label="合同金额(元)" prop="contractAmount" />
        <base-table-column label="订单状态" prop="orderStatus" />
        <base-table-column label="支付方式" prop="paymentMethod" />
        <base-table-date label="下单时间" prop="orderTime" sortable />
        <base-table-date label="付款时间" prop="paymentTime" sortable />
        <base-table-column label="充值账户" prop="rechargeAccount" />
        <base-table-column label="当前余额(积分)" prop="currentBalance" />
    </base-table>
</template>

<style lang="scss" scoped>

</style>
