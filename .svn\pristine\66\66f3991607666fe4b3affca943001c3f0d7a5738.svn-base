const ModelApis = {
    // 模特列表
    getList(params: TsApis.ApiParamsWithoutPage<"general", "post_aivatarapi_robotlist"> = {}) {
        const method = (page: number, size: number) => Apis.general['post_aivatarapi_robotlist']({
            name: "post_aivatarapi_robotlist",
            params: {
                ...params,
                page,
                size,
            },
            transform: (res) => ({
                total: res.total ?? 0,
                data: res.rows ?? [],
            }),
        })

        return alova.usePagination(method, {
            watchingStates: [params, toRef(params as any, 'search')],
            debounce: [0, 300],
            initialPage: 1,
            initialPageSize: 10,
            immediate: true,
            initialData: {
                total: 0,
                data: [],
            },
        })
    },

    getDetail() {
        null
    },

    liek() {
        null
    },

    favorit() {
        null
    },
    // 定制模特
    videoTaskInfo: () => {
        const method = (data: TsApis.ApiData<"general","post_aivatarapi_videotaskinfo">) => Apis.general.post_aivatarapi_videotaskinfo({
            name: "post_aivatarapi_videotaskinfo",
            data,
        });
        const methodRequest = alova.useForm(method, {
            resetAfterSubmiting: true,
            middleware: alova.actionDelegationMiddleware("post_aivatarapi_videotaskinfo"),
            initialForm: {
                id: "",
                name: "",
                videoUrl: "",
                level: "",
                greenScreen: "0",
                compress: "0",
            }
        })
        const { onSuccess } = methodRequest;
        onSuccess(() => {
            ElMessage.success("定制模特提交成功")
        })
        return methodRequest;
    }
} as const;

export default ModelApis;
