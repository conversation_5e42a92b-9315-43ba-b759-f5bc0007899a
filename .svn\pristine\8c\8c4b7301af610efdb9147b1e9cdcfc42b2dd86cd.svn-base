<script setup lang="ts">
import { ElCheckbox, ElUpload } from 'element-plus';

// 表单ref
const formRef = useTemplateRef<TsForm.El>("formRef");

// 表单数据
const formData = ref<TsAudioClone.FormData>({
    customType: 'voice_clone_e',
    name: '',
    sex: 'male',
    audioUrl: '',
});

// 表单校验规则
const rules = reactive({
    customType: [{ required: true, message: "请选择定制类型", trigger: "change" }],
    name: [{ required: true, message: "模型名称不能为空", trigger: "blur" }],
    audioUrl: [{ required: true, message: "请上传音频", trigger: "blur" }],
    sex: [{ required: true, message: "请选择性别", trigger: "change" }],
});

// 定制类型选项
const customTypeOptions: TsSelect.Options = [
    { label: '声音克隆-E级', value: 'voice_clone_e' },
];

// 性别选项
const genderOptions = [
    { label: '男', value: 'male' },
    { label: '女', value: 'female' }
];

// 当前选择的定制类型
const selectedType = ref('voice_clone_e');

// 标签描述
const description = ref('');

// 推荐人
const referrer = ref('');

// 音频文件上传
const audioFile = ref<File | null>(null);
const audioPreview = ref('');
const isUploading = ref(false);
let uploadUrl = '';
let audioUrl = '';

// 处理音频文件上传
const handleAudioUpload = (file: File) => {

    // 创建音频预览URL
    if (file) {
        uploadAudio(file);
    }

    return false; // 阻止自动上传
};

const uploadSuccess = (file: File) => {
    audioFile.value = file;
    audioPreview.value = URL.createObjectURL(file);
    formData.value.audioUrl = audioUrl;
}

// 移除音频文件
const removeAudio = () => {
    audioFile.value = null;

    if (audioPreview.value) {
        URL.revokeObjectURL(audioPreview.value);
        audioPreview.value = '';
        formData.value.audioUrl = '';
        uploadUrl = '';
        audioUrl = '';
    }
};

const { send: getUploadLinkApi } = apiAudioClone.getUpload();

const getUploadLink = async () => {
    const res = await getUploadLinkApi();
    uploadUrl = res.data.temporaryUrl;
    audioUrl = res.data.objectUrl;
}

const uploadAudio = async (file: File) => {
    if (!file) return;
    isUploading.value = true;
    const Msg = ElMessage({
        message: '音频上传中...',
        type: 'info',
        duration: 0,
    });
    try {
        await getUploadLink();
        await apiAudioClone.upload(file, uploadUrl);
        Msg.close();
        ElMessage.success('音频上传成功');
        uploadSuccess(file);
    } catch (error) {
        Msg.close();
        ElMessage.error('音频上传失败');
        uploadUrl = '',
        audioUrl = '';
    } finally {
        isUploading.value = false;
    }
}

// 提交表单
const {
    loading,
    send,
} = apiAudioClone.submit();


const handleSubmit = async (e: SubmitEvent) => {
    e.preventDefault();
    if (!formRef.value) return;

    await formRef.value.validate();

    // 构建最终的表单数据
    const submitData: TsAudioClone.SubmitData = {
        name: formData.value.name,
        sex: formData.value.sex as any,
        audioUrl: formData.value.audioUrl || '',
    };
    
    try {
        await send(submitData);
    } catch (error) {
        ElMessage.error('声音克隆任务提交失败');
        return;
    }

    ElMessage.success('声音克隆任务已提交');
};

// 同意协议
const agreeTerms = ref(false);
</script>

<template>
    <div class="audio-clone-container">
        <base-form
            ref="formRef"
            class="form-card"
            :model-value="formData"
            :rules="rules"
            label-position="left"
            label-width="auto"
            @submit="handleSubmit"
        >
            <!-- 定制类型 -->
            <base-form-item label="定制类型" prop="customType">
                <base-select
                    v-model="selectedType"
                    :options="customTypeOptions"
                    placeholder="请选择定制类型"
                    disabled
                />
            </base-form-item>

            <!-- 模型名称 -->
            <base-form-item label="模型名称" prop="name">
                <base-input v-model="formData.name" placeholder="请输入人物模型名称" :maxlength="10" show-word-limit />
            </base-form-item>

            <!-- 头像上传区域 -->
            <base-form-item label="头像" v-if="false">
                <div class="avatar-upload">
                    <div class="avatar-placeholder">
                        <base-icon icon="solar:user-bold" size="48" color="#999" />
                        <span class="avatar-text">编辑头像</span>
                    </div>
                </div>
            </base-form-item>

            <!-- 音频上传区域 -->
            <base-form-item label="音频" prop="audioUrl">
                <div class="audio-upload">
                    <el-upload
                        class="audio-uploader"
                        :show-file-list="false"
                        :before-upload="handleAudioUpload"
                        :disabled="!!audioFile || isUploading"
                        accept=".wav"
                        auto-upload
                        drag
                    >
                        <div v-if="!audioFile" class="upload-content" v-loading="isUploading">
                            <base-icon icon="solar:music-note-bold" size="32" color="#409eff" />
                            <div class="upload-text">
                                <p>点击或拖拽音频文件到此处上传</p>
                                <p class="upload-tip">仅支持WAV格式</p>
                            </div>
                        </div>
                        <div v-else class="audio-preview">
                            <base-icon icon="solar:music-note-bold" size="24" color="#67c23a" />
                            <span class="audio-name">{{ audioFile.name }}</span>
                            <base-button :sets="{ size: 'small', type: 'danger', text: true }"
                                @click.stop="removeAudio">
                                删除
                            </base-button>
                        </div>
                    </el-upload>

                    <!-- 音频播放器 -->
                    <audio v-if="audioPreview" :src="audioPreview" controls class="audio-player" />
                </div>
            </base-form-item>

            <!-- 性别选择 -->
            <base-form-item label="性别" prop="sex">
                <el-radio-group v-model="formData.sex" class="gender-group">
                    <el-radio v-for="option in genderOptions" :key="option.value" :value="option.value">
                        {{ option.label }}
                    </el-radio>
                </el-radio-group>
            </base-form-item>

            <!-- 标签描述 -->
            <base-form-item label="标签" v-if="false">
                <el-input v-model="description" type="textarea" :rows="4" placeholder="请输入人物标签，多个以分号分割" maxlength="200"
                    show-word-limit class="description-input" />
            </base-form-item>

            <!-- 推荐人 -->
            <base-form-item label="推荐人" v-if="false">
                <base-input :sets="{
                    modelValue: referrer,
                    placeholder: '请输入推荐手机号或邀请码'
                }" @update:modelValue="referrer = $event" />
            </base-form-item>

            <!-- 小贴士 -->
            <el-alert
                title="小贴士"
                type="warning"
                :closable="false"
            >
                <template #title>
                    <div class="title">
                        <base-icon icon="solar:info-circle-outline" />
                        <span>小贴士</span>
                    </div>
                </template>
                <div>
                    <p>1. 录音时上传单条，时长在1-5分钟内；</p>
                    <p>2. 录音语料支持16K及以下采样率；</p>
                    <!-- <p>3. 录音时文字体现正确性上传，支持zip和rar格式</p> -->
                </div>
            </el-alert>

            <!-- 提交按钮 -->
            <div class="submit-section">
                <base-button
                    type="primary"
                    native-type="submit"
                    size="large"
                    :disabled="!agreeTerms"
                    :loading="loading"
                >
                    上传资料
                </base-button>

                <!-- 协议同意 -->
                <div class="agreement">
                    <el-checkbox v-model="agreeTerms">
                        我已阅读并同意
                        <a href="#">《声音克隆协议》</a>
                    </el-checkbox>
                </div>
            </div>
        </base-form>
    </div>
</template>

<style scoped lang="scss">
.audio-clone-container {
    max-width: 600px;
    margin: 0 auto;
    padding: 20px;
}

.form-card {
    background: var(--el-bg-color);
    border-radius: 8px;
    padding: 24px;
}

.upload-section {
    display: flex;
    gap: 20px;
    align-items: flex-start;
}

.avatar-upload {
    .avatar-placeholder {
        width: 80px;
        height: 80px;
        border: 2px dashed #dcdfe6;
        border-radius: 8px;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        cursor: pointer;
        transition: border-color 0.3s;

        &:hover {
            border-color: #409eff;
        }

        .avatar-text {
            font-size: 12px;
            color: #999;
            margin-top: 4px;
        }
    }
}

.audio-upload {
    flex: 1;

    .audio-uploader {
        width: 100%;

        :deep(.el-upload) {
            width: 100%;
        }

        :deep(.el-upload-dragger) {
            width: 100%;
            height: 120px;
        }
    }

    .upload-content {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        height: 100%;

        .upload-text {
            margin-top: 12px;
            text-align: center;

            p {
                margin: 4px 0;

                &.upload-tip {
                    font-size: 12px;
                    color: var(--el-text-color-placeholder);
                }
            }
        }
    }

    .audio-preview {
        display: flex;
        align-items: center;
        gap: 8px;
        padding: 12px;

        .audio-name {
            flex: 1;
            font-size: 14px;
            color: var(--el-text-color-primary);
        }
    }

    .audio-player {
        width: 100%;
        margin-top: 12px;
    }
}

.gender-group {
    display: flex;
    gap: 16px;
}

.description-input {
    width: 100%;
}

.title {
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: var(--el-font-size-medium);
    margin-bottom: var(--el-gap-half);
}

.submit-section {
    margin-top: var(--el-gap);
    text-align: center;

    .agreement {
        margin-top: 16px;
        font-size: 14px;
        color: #666;
    }
}
</style>