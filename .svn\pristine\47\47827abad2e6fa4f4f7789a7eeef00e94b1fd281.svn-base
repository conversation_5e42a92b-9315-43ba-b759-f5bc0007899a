<script setup lang="ts">
const {sets = {}} = defineProps<{
    sets?: TsSwitch.Sets
}>()
</script>
<template>
    <el-switch
        activeValue="0"
        inactiveValue="1"
        inline-prompt
        active-text="正常"
        inactive-text="停用"
        v-bind="sets"
        :style="
            (sets.colorOn ? '--el-switch-on-color: '+sets.colorOn+';':'') +
            (sets.colorOff ? '--el-switch-off-color: '+sets.colorOff+';':'')
        "
    />
</template>
<style scoped>

</style>