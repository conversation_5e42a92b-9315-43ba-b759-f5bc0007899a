namespace TsTable {
    type Model = Row[];

    interface Row extends TsBase.Object {
    }

    interface Sets<T> extends Partial<Omit<TsElementPlus.TableProps<T>, "data">> {
    }
}
namespace TsTableColumn {
    type Label = string;
    type Prop = string;

    interface Sets<T> extends Partial<TsElementPlus.TableColumnCtx<T>> {
    }
}
namespace TsTableDate {
    type Label = TsTableColumn.Label;
    type Prop = TsTableColumn.Prop;

    interface Sets<T> extends TsTableColumn<T> {
        fmtDate?: TsBase.FmtDate;
    }
}
namespace TsTableSpecial {
    type Type = "selection" | "index" | "expand" | "handle";
    type Label = TsTableColumn.Label;

    interface Sets<T> extends Partial<TsElementPlus.TableColumnCtx<T>> {
    }
}
namespace TsTableSwitch {
    type Label = TsTableColumn.Label;
    type Prop = TsTableColumn.Prop;

    interface Sets<T> extends TsTableColumn<T>, TsSwitch.Sets {
    }
}
namespace TsTableDict {
    type Label = TsTableColumn.Label;
    type Prop = TsTableColumn.Prop;

    interface Sets<T> extends TsTableColumn<T> {
    }
    type Options = TsDict.Response[];
}