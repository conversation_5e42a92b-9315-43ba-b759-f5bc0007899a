<script setup lang="ts">
import './dialogInside.scss'
import pointGoodCard from './pointGoodCard.vue';

defineProps<{
    balance: string
}>();

const emit = defineEmits<{
    updateChoose: [point: number],
}>();

provide('unit', '分钟');

// 充值选项
const rechargeOptions = [
    { time: 5, price: 5 },
    { time: 50, price: 50 },
    { time: 500, price: 500 },
    { time: 1000, price: 1000 },
    { time: 5000, price: 5000 },
    { time: 10000, price: 10000 }
].map((i, index) => ({
    ...i,
    id: index
}));

const remainingPoint = ref(rechargeOptions[0]);

// 选择充值时长
function handelClick(item: typeof rechargeOptions[number]) {
    remainingPoint.value = item;
    emit('updateChoose', item.price);
}

onMounted(() => {
    emit('updateChoose', remainingPoint.value.price);
})
</script>

<template>
    <!-- 信息显示 -->
    <div class="recharge-dialog-inside-info">
        <span>剩余时长：{{ balance }}</span>
    </div>

    <!-- 充值选项网格 -->
    <div class="recharge-dialog-inside-grid">
        <pointGoodCard
            v-for="option in rechargeOptions"
            :key="option.id"
            :price="option.price"
            :num="option.time"
            :is-active="remainingPoint.id == option.id"
            @click="handelClick(option)"
        />
    </div>
</template>