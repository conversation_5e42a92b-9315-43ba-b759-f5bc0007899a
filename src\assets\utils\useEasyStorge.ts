import { lowPriorityFn } from './eventFn';

/**
 * 生成唯一键
 * @returns 唯一标识符
 */
function generateUniqueKey() {
  return `easy_storage_${Date.now()}_${Math.floor(Math.random() * 10000)}`;
}

/**
 * 简易存储工具
 * @template T - 存储数据的类型
 * @param key - 存储键名，默认为自动生成的唯一键
 * @param needStorage - 是否需要持久化到localStorage
 * @returns 包含getData、cleanData和setData的对象
 */
function useEasyStorage<T = any>(key = generateUniqueKey(), needStorage = true) {
  let data: T | null = null;

  /**
   * 获取数据
   * @returns 存储的数据或null
   */
  let getData = (): T | null => {
    if (!data) {
      try {
        const storedData = localStorage.getItem(key);
        data = storedData ? JSON.parse(storedData) : null;
      } catch (error) {
        console.error('Failed to parse stored data:', error);
        data = null;
      }
    }
    return data;
  };

  /**
   * 清除数据
   */
  let cleanData = (): void => {
    data = null;
    lowPriorityFn(() => {
      localStorage.removeItem(key);
    });
  };

  /**
   * 设置数据
   * @param newData - 新的数据
   */
  let setData = (newData: T): void => {
    data = newData;
    lowPriorityFn(() => {
      try {
        localStorage.setItem(key, JSON.stringify(newData));
      } catch (error) {
        console.error('Failed to store data:', error);
      }
    });
  };

  // 如果不需要持久化存储，则重写函数
  if (!needStorage) {
    getData = (): T | null => data;
    cleanData = (): void => { data = null; };
    setData = (newData: T): void => { data = newData; };
  }

  return {
    getData,
    cleanData,
    setData,
  };
}

export default useEasyStorage;